import { heic2convert } from '../../../helper';
import Button from '@components/form/Button';
import Input from '@components/form/Input';
import Label from '@components/form/Label';
import api from '@configs/api';
import { MedicalDeviceManagement } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import CancelButton from '@partials/MaterialButton/CancelButton';
import File from '@partials/MaterialFile/File';
import Modal from '@partials/MaterialModal/Modal';
import RadioButton from '@partials/MaterialRadioButton/MaterialRadioButton';
import { Formik, FormikErrors } from 'formik';
import * as React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';

export interface IMedicalValues {
    product_name: string;
    model: string;
    brand: string;
    serial_number: string;
    supplier: string;
    // performed_maintenance: string;
    // upcoming_maintenance: string;
    upload_manual: File | undefined;
    supplier_agreement: File | undefined;
    compliance_declared: string;
    server?: string;
}
export interface MedicalDeviceModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    mutate: () => Promise<any>;
    selectedMedicalDevice?: MedicalDeviceManagement;
}

const MedicalDeviceModal: React.FC<MedicalDeviceModalProps> = ({ openModal, setOpenModal, mutate, selectedMedicalDevice }) => {
    const navigate = useNavigate();

    return (
        <Formik<IMedicalValues>
            initialValues={
                !selectedMedicalDevice
                    ? {
                          brand: '',
                          upload_manual: undefined,
                          supplier_agreement: undefined,
                          model: '',
                          //   performed_maintenance: '',
                          product_name: '',
                          serial_number: '',
                          supplier: '',
                          //   upcoming_maintenance: '',
                          compliance_declared: '0',
                      }
                    : {
                          brand: selectedMedicalDevice?.brand,
                          upload_manual: undefined,
                          supplier_agreement: undefined,
                          model: selectedMedicalDevice?.model,
                          //   performed_maintenance: selectedMedicalDevice?.performed_maintenance ?? '',
                          product_name: selectedMedicalDevice?.product_name,
                          serial_number: selectedMedicalDevice?.serial_number,
                          supplier: selectedMedicalDevice?.supplier,
                          //   upcoming_maintenance: selectedMedicalDevice?.upcoming_maintenance ?? '',
                          compliance_declared: selectedMedicalDevice?.compliance_declared ? '1' : '0',
                      }
            }
            enableReinitialize
            validate={validateMedicalDevice}
            onSubmit={async (values, { resetForm, setSubmitting, setFieldError }) => {
                const formData = new FormData();
                formData.set('product_name', values.product_name);
                formData.set('brand', values.brand);
                formData.set('model', values.model);
                // formData.set('performed_maintenance', values.performed_maintenance);
                formData.set('serial_number', values.serial_number);
                formData.set('supplier', values.supplier);
                // formData.set('upcoming_maintenance', values.upcoming_maintenance);
                if (values.compliance_declared) {
                    formData.set('compliance_declared', values.compliance_declared);
                }
                if (values?.upload_manual) {
                    formData.append('upload_manual', values?.upload_manual);
                }
                if (values?.supplier_agreement) {
                    formData.append('supplier_agreement', values?.supplier_agreement);
                }
                const response = await fetch(
                    selectedMedicalDevice ? api.management_device.update(selectedMedicalDevice.id) : api.management_device.store,
                    {
                        method: 'POST',
                        headers: {
                            Accept: 'application/json',
                            
                            
                            'X-App-Locale': strings.getLanguage(),
                        },
                        credentials: 'include',
                        body: formData,
                    },
                );

                const data = await response.json();

                if (response.status === 401) {
                    navigate('/');
                }

                if (data.status === '1') {
                    await mutate();
                    await resetForm();
                    toast.success(data.message);
                    setOpenModal(false);
                } else {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                }
                setSubmitting(false);
            }}
        >
            {({
                errors,
                resetForm,
                values,
                touched,
                dirty,
                handleSubmit,
                setFieldTouched,
                setFieldValue,
                isSubmitting,
                isValidating,
                handleBlur,
                handleChange,
            }) => {
                const handleClose = async () => {
                    if (isSubmitting || isValidating) return;
                    setOpenModal(false);
                    await resetForm();
                };
                return (
                    <Modal
                        open={openModal}
                        title={`${(selectedMedicalDevice ? strings.update : strings.add) + ' ' + strings.medical_device}`}
                        handleClose={handleClose}
                        size="large"
                        cancelButton={<CancelButton disabled={isSubmitting} onClick={handleClose} />}
                        submitButton={
                            <Button
                                loading={isSubmitting}
                                onClick={() => {
                                    if (!dirty && selectedMedicalDevice) {
                                        toast.success(strings.no_data_changed);
                                        handleClose();
                                        return;
                                    }
                                    if (handleSubmit) return handleSubmit();
                                }}
                            >
                                {strings.Submit}
                            </Button>
                        }
                    >
                        <FormikErrorFocus />
                        <div className="grid grid-cols-1 gap-x-6 gap-y-4 p-4 md:grid-cols-2">
                            <div className="md:col-span-2">
                                <Input
                                    name="product_name"
                                    label={strings.device_name}
                                    placeholder={strings.device_name}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    value={values.product_name}
                                    required
                                    error={touched?.product_name && errors.product_name}
                                />
                            </div>
                            <Input
                                name="model"
                                label={strings.model}
                                placeholder={strings.model}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.model}
                                required
                                error={touched?.model && errors.model}
                            />
                            <Input
                                name="brand"
                                label={strings.brand}
                                placeholder={strings.brand}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.brand}
                                required
                                error={touched?.brand && errors.brand}
                            />
                            <Input
                                name="serial_number"
                                label={strings.serial_number}
                                placeholder={strings.serial_number}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                required
                                value={values.serial_number}
                                error={touched?.serial_number && errors.serial_number}
                            />
                            <Input
                                name="supplier"
                                label={strings.supplier}
                                placeholder={strings.supplier}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                required
                                value={values.supplier}
                                error={touched?.supplier && errors.supplier}
                            />
                            {/* <Input
                                name="performed_maintenance"
                                label={strings.performed_maintenance}
                                placeholder={strings.performed_maintenance}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.performed_maintenance}
                            />
                            <Input
                                name="upcoming_maintenance"
                                label={strings.upcoming_maintenance}
                                placeholder={strings.upcoming_maintenance}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.upcoming_maintenance}
                            /> */}
                            <div className="flex space-x-2.5 md:col-span-2">
                                <p>{strings.compliance_declaration_check_performed}</p>
                                <RadioButton
                                    label={strings.Yes}
                                    value={values.compliance_declared}
                                    checked={values.compliance_declared === '1'}
                                    onChange={(e) => {
                                        setFieldTouched('compliance_declared');
                                        setFieldValue('compliance_declared', '1');
                                    }}
                                />
                                <RadioButton
                                    label={strings.No}
                                    value={values.compliance_declared}
                                    checked={values.compliance_declared === '0'}
                                    onChange={(e) => {
                                        setFieldTouched('compliance_declared');
                                        setFieldValue('compliance_declared', '0');
                                    }}
                                />
                            </div>
                            <div>
                                <Label label={strings.upload_manuals} />
                                <File
                                    onChange={async (e) => {
                                        let file: File | Blob | null = e?.target?.files ? e.target.files[0] : null;
                                        if (file && !file.type) {
                                            file = await heic2convert(file);
                                        }
                                        await setFieldTouched('upload_manual');
                                        setFieldValue('upload_manual', e?.target?.files ? e.target.files[0] : null);
                                    }}
                                    id="upload_manuals"
                                    name="upload_manual"
                                />
                            </div>
                            <div>
                                <Label label={strings.supplier_agreements} />
                                <File
                                    onChange={async (e) => {
                                        let file: File | Blob | null = e?.target?.files ? e.target.files[0] : null;
                                        if (file && !file.type) {
                                            file = await heic2convert(file);
                                        }

                                        await setFieldTouched('supplier_agreement');
                                        setFieldValue('supplier_agreement', e?.target?.files ? e.target.files[0] : null);
                                    }}
                                    multiple
                                    id="supplier_agreements"
                                    name="supplier_agreement"
                                />
                            </div>
                            <ServerError className="mt-4" error={errors?.server} />
                        </div>
                    </Modal>
                );
            }}
        </Formik>
    );
};
function validateMedicalDevice(values: IMedicalValues): void | object | Promise<FormikErrors<IMedicalValues>> {
    let errors: FormikErrors<IMedicalValues> = {};
    if (!values.product_name) {
        errors.product_name = strings.device_name_required;
    }
    if (!values.model) {
        errors.model = strings.model_name_required;
    }
    if (!values.brand) {
        errors.brand = strings.brand_name_required;
    }
    if (!values.serial_number) {
        errors.serial_number = strings.serial_number_required;
    }
    if (!values.supplier) {
        errors.supplier = strings.supplier_name_required;
    }
    return errors;
}
export default MedicalDeviceModal;
