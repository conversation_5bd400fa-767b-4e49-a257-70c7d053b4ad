import Card from '@components/card';
import PaginationCompo from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import Skeleton from '@components/Skeleton/Skeleton';
import api from '@configs/api';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { UserDocumentResponse, UserDocumentVersionsPaginatedResponse } from '@interface/common';
import { DocumentVersion } from '@interface/model/document';
import strings from '@lang/Lang';
import MaterialBreadcrumbs from '@partials/Breadcrumbs/MaterialBreadcrumbs';
import EmptyData from '@partials/Error/EmptyData';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import Table from '@partials/Table/PageTable';
import { useState } from 'react';
import { useParams } from 'react-router';
import useSWR from 'swr';
import DocumentViewModal from '../DocumentViewModal';
import DocumentVersionListItem from './DocumentVersionListItem';

export interface DocumentVersionsProps {}

const DocumentVersions: React.FC<DocumentVersionsProps> = ({}) => {
    const { documentId } = useParams();

    const { data: documentData } = useSWR<UserDocumentResponse, Error>(api.documents.view(documentId));

    const { data, mutate, page, setPage, loading, search, setSearch, orderBy, handleOrder, orderDirection } = usePaginationSWR<
        UserDocumentVersionsPaginatedResponse,
        Error
    >(api.documents.versions(documentId));

    const [openModal, setOpenModal] = useState(false);
    const [openViewModal, setOpenViewModal] = useState(false);
    const [selectedVersion, setSelectedVersion] = useState<DocumentVersion>();
    const [downloading, setDownloading] = useState<number>();

    const onDownloadClick = async (version: DocumentVersion) => {
        setDownloading(version.id);
        await download(`${documentData?.data.title.substring(0, 200)}-v${version.version}.pdf`, api.storageUrl(version.pdf));
        setDownloading(undefined);
    };

    return (
        <>
            <ModalSuspense>
                {selectedVersion && openViewModal && (
                    <DocumentViewModal
                        openModal={openViewModal}
                        onClose={() => setOpenViewModal(false)}
                        selectedDocumentId={documentId ? parseInt(documentId) : 0}
                        version={selectedVersion}
                    />
                )}
            </ModalSuspense>
            <div className="space-y-2">
                <MaterialBreadcrumbs />

                <Card className="space-y-4">
                    <div className="line-clamp-2 overflow-ellipsis break-all">
                        <Heading text={documentData?.data.title ?? strings.versions} variant="subTitle" />
                    </div>
                    <Table>
                        <Table.Head>
                            <Table.ThSort sort={orderBy === 'version' && orderDirection} onClick={() => handleOrder('version')}>
                                {strings.version}
                            </Table.ThSort>
                            <Table.ThSort sort={orderBy === 'created_at' && orderDirection} onClick={() => handleOrder('created_at')}>
                                {strings.added_on}
                            </Table.ThSort>
                            <Table.Th>{strings.Signed_by}</Table.Th>
                            <Table.Th children={strings.Actions} className="text-right" />
                        </Table.Head>
                        <Table.Body>
                            {!loading && !data?.data.length && <EmptyData cardHeight="!h-[72vh]" />}
                            {data?.data.map((version) => (
                                <DocumentVersionListItem
                                    version={version}
                                    key={version.id}
                                    onView={(version) => {
                                        setSelectedVersion(version);
                                        setOpenViewModal(true);
                                    }}
                                    onDownload={onDownloadClick}
                                    isDownloading={downloading === version.id}
                                />
                            ))}
                            {loading && <QuestionnairesSkeleton limit={10} />}
                            {!loading && !data?.data.length && <EmptyData cardHeight="!h-[72vh]" />}
                        </Table.Body>
                    </Table>
                    <PaginationCompo
                        pageSize={data?.per_page ?? 0}
                        totalCount={data?.total ?? 0}
                        currentPage={page}
                        onPageChange={(page) => setPage(page)}
                    />
                </Card>
            </div>
        </>
    );
};

async function download(fileName: string, url: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
        });

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);
        a.setAttribute('download', fileName);
        a.click();
    } catch (error) {
        console.error(error);
    }
}

function QuestionnairesSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}

export default DocumentVersions;
