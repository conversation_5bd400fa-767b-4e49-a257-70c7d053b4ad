import Button from '@components/form/Button';
import { MedicalDeviceManagement } from '@interface/model/medicalDevice';
import PowerIcon from '@partials/Icons/Power';
import * as React from 'react';
import { toast } from 'react-toastify';
import api from '../../../configs/api';
import strings from '../../../lang/Lang';
import DeleteModal from '../../../partials/MaterialModal/DeleteModal';

export interface MedicalDeviceDeleteModalProps {
    open?: boolean;
    handleClose?: () => void;
    selectedDevice?: MedicalDeviceManagement;
    mutate: () => Promise<any>;
}

const MedicalDeviceDeleteModal: React.FC<MedicalDeviceDeleteModalProps> = ({ open = false, handleClose = () => {}, selectedDevice, mutate }) => {
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    return (
        <DeleteModal
            open={open}
            handleClose={() => {
                if (isSubmitting) return;
                handleClose();
            }}
            icon={<PowerIcon slash={!selectedDevice?.deleted_at} />}
            text={`${selectedDevice?.deleted_at ? strings.RESTORE_CLIENT_1 : strings.INACTIVATE_CLIENT_1}?`}
            submitButton={
                <Button
                    loading={isSubmitting}
                    onClick={async () => {
                        setIsSubmitting(true);
                        const response = await fetch(
                            selectedDevice?.deleted_at
                                ? api.management_device.restore(selectedDevice.id)
                                : api.management_device.delete(selectedDevice?.id),
                            {
                                method: selectedDevice?.deleted_at ? 'GET' : 'DELETE',
                                headers: {
                                    Accept: 'application/json',
                                    
                                    'X-App-Locale': strings.getLanguage(),
                                    'Content-Type': 'application/json',
                                },
                                credentials: 'include',
                            },
                        );

                        const data = await response.json();

                        if (data.status === '1') {
                            await mutate();
                            toast.success(data.message);
                            handleClose();
                        } else {
                            toast.error(data.message || 'server error, please contact admin.');
                        }
                        setIsSubmitting(false);
                        handleClose();
                    }}
                >
                    {selectedDevice?.deleted_at ? strings.activate : strings.inactivate}
                </Button>
            }
        />
    );
};

export default MedicalDeviceDeleteModal;
