import api from '@configs/api';
import { ServiceResponse } from '@interface/common';
import strings from '@lang/Lang';
import useSWR from 'swr';
import TimePeriod from './TimePeriod';
import MarketingSelectService from './MarketingSelectService';
import cx from 'classix';

export interface MarketingServicesProps {
    value: string;
    servicesValue: number[];
    onChange: (value: string) => void;
    onServiceChange: (value: number[]) => void;
    error?: string;
}
const MarketingServices: React.FC<MarketingServicesProps> = ({ onChange, value, onServiceChange, servicesValue, error }) => {
    const { data: servicesData } = useSWR<ServiceResponse, Error>(api.servicePractitionersAll({ userIds: [] }));
    return (
        <div className={cx('w-full', error && 'pb-2')}>
            <div className="w-full space-y-2 rounded-lg bg-grayishBlue px-3 py-2 text-dimGray dark:border-darkGray dark:bg-darkGray dark:text-white">
                <p>{strings.target_clients_who_last_booked}</p>
                <div className="w-full">
                    <MarketingSelectService
                        onChange={onServiceChange}
                        placeholder={strings.select_a_service}
                        options={servicesData?.data || []}
                        value={servicesValue}
                        color="white"
                        className="dark:!border-black dark:!bg-black"
                    />
                </div>
                <div className="flex flex-wrap gap-2 items-center">
                    <p>
                        {strings.service.toLowerCase()} {strings.before}
                    </p>
                    <div className="w-32">
                        <TimePeriod onChange={onChange} value={value} />
                    </div>
                    <p>{strings.ago}</p>
                </div>
            </div>
            <p className="text-[10px] text-red-500 first-letter:capitalize">{error}</p>
        </div>
    );
};
export default MarketingServices;
