import { tagManagerArgs } from '@components/Register';
import LoginSideImage from '@components/auth/LoginSideImage';
import Button from '@components/form/Button';
import InfoCard from '@components/form/InfoCard';
import Input from '@components/form/Input';
import InputPassword from '@components/form/InputPassword';
import LanguageSelect from '@components/form/LanguageSelect';
import Heading from '@components/heading/Heading';
import AnchorLink from '@components/link/AnchorLink';
import RouterLink from '@components/link/RouterLink';
import api from '@configs/api';
import { findCountryByAbbr } from '@configs/countries';
import useAuth from '@hooks/useAuth';
import useLocalStorage from '@hooks/useLocalStorage';
import useTheme from '@hooks/useTheme';
import useTranslation from '@hooks/useTranslation';
import strings from '@lang/Lang';
import PrescriptionLogoWithName from '@partials/Icons/PrescriptionLogoWithName';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import ReCaptchaComponent from '@partials/ReCAPTCHA/ReCaptcha';
import { SignupFormValues, singUpValidation } from '@validations/signup';
import { Formik } from 'formik';
import { useEffect, useRef, useState } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';
import TagManager from 'react-gtm-module';
import { Navigate, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

export interface SignupProps {}

const Signup: React.FC<SignupProps> = () => {
    const [language] = useTranslation();
    const { theme } = useTheme();
    const captchaRef = useRef<string | React.RefObject<ReCAPTCHA> | ((instance: ReCAPTCHA | null) => void) | null | undefined>(null);
    const { user, loggedOut, mutate } = useAuth();
    const navigate = useNavigate();
    const { setStorageValue: setIsPrescriber } = useLocalStorage('is_prescriber', false);
    const [isCheck, setIsCheck] = useState(false);
    const [country, setCountry] = useState<string>('');
    async function getCountryInfo(): Promise<void> {
        try {
            const response = await fetch('https://api.country.is/');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: { ip: string; country: string } = await response.json();
            setCountry(data.country);
        } catch (error) {
            console.error('Error fetching country info:', error);
        }
    }
    useEffect(() => {
        // TagManager.initialize(tagManagerArgs);
        getCountryInfo();
    }, []);

    return (
        <div className="relative flex h-[100svh] w-full items-end justify-center bg-white dark:bg-black sm:items-center">
            {user && !loggedOut && <Navigate to="/prescription" replace={true} />}
            <LoginSideImage />
            <div className="z-10 h-full overflow-y-auto px-8 py-12 lg:hidden">
                <div className="w-full max-w-md rounded-2xl bg-white px-8 pt-8 dark:bg-black lg:rounded-none lg:pt-12 xl:max-w-xl xl:px-16">
                    {/* <PrescriptionLogoWithName clssName='block lg:hidden' />   */}
                    {view()}
                </div>
            </div>
            <div className="z-10 mx-8 hidden w-full max-w-md rounded-2xl bg-white dark:bg-black lg:block lg:rounded-none xl:max-w-xl xl:px-16">
                <div className="soft-searchbar h-screen space-y-5 overflow-auto px-8 py-8 pt-12">
                    <PrescriptionLogoWithName clssName="hidden lg:block" />
                    {view()}
                </div>
            </div>
        </div>
    );
    function view() {
        return (
            <Formik<SignupFormValues>
                initialValues={{
                    email: '',
                    password: '',
                    confirm_password: '',
                    recaptcha: '',
                    country: findCountryByAbbr(country)?.name || '',
                }}
                enableReinitialize
                validate={singUpValidation}
                onSubmit={async (values, { resetForm, setErrors, setSubmitting }) => {
                    const formData = new FormData();
                    formData.set('email', values.email);
                    formData.set('password', values.password);
                    formData.set('password_confirmation', values.confirm_password);
                    formData.set('platform', 'PRESCRIPTION');
                    // formData.set('country', values?.country);

                    const response = await fetch(api.signup, {
                        method: 'POST',
                        headers: {
                            Accept: 'application/json',
                            'X-App-Locale': strings.getLanguage(),
                        },
                        credentials: 'include',
                        body: formData,
                    });
                    const data = await response.json();
                    if (data.status === '1') {
                        resetForm();
                        setIsPrescriber(true);
                        toast.success(data.message || 'Your account has been registered.');
                        await navigate('/prescription/sign-up/set-up');
                        await mutate();
                    }
                    if (['2', '3', '0'].includes(data.status)) {
                        setErrors({
                            server: data?.message || 'server error. please try contact admin.',
                        });
                    }
                    setSubmitting(false);
                }}
            >
                {({
                    errors,
                    values,
                    touched,
                    handleChange,
                    handleBlur,
                    dirty,
                    setFieldValue,
                    setFieldTouched,
                    handleSubmit,
                    isSubmitting,
                    isValidating,
                    setFieldError,
                    submitForm,
                }) => (
                    <form className="space-y-5 py-6" onSubmit={handleSubmit}>
                        <Input
                            placeholder={strings.Email}
                            label={strings.Email}
                            type="email"
                            value={values.email}
                            name="email"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={errors.email && touched.email && errors.email}
                            required
                        />
                        <InputPassword
                            placeholder={strings.Password}
                            label={strings.Password}
                            value={values.password}
                            name="password"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={errors.password && touched.password && errors.password}
                            required
                        />
                        <InputPassword
                            placeholder={strings.Password}
                            label={strings.confirmPassword}
                            value={values.confirm_password}
                            name="confirm_password"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={errors.confirm_password && touched.confirm_password && errors.confirm_password}
                            required
                        />
                        <LanguageSelect onChange={() => {}} />
                        <div className="my-2 origin-top-left scale-75 transform transition-transform md:transform-none">
                            <ReCaptchaComponent
                                ref={captchaRef}
                                key={`${language}_${theme}`}
                                hl={language}
                                error={touched?.recaptcha && Boolean(errors.recaptcha)}
                                helperText={touched?.recaptcha && errors.recaptcha}
                                onChange={(data) => {
                                    setFieldTouched('recaptcha');
                                    setFieldValue('recaptcha', data);
                                }}
                            />
                        </div>
                        <div className="flex pb-4">
                            <Checkbox label={''} checked={isCheck} onChange={(e) => setIsCheck(e.target.checked)} />
                            <p>
                                {strings.formatString(
                                    strings.new_login_term_policy,
                                    <AnchorLink
                                        variant="underline"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        href="https://malonline.se/allmanna-villkor "
                                    >
                                        {strings.terms_of_use}
                                    </AnchorLink>,
                                    <AnchorLink
                                        variant="underline"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        href="https://malonline.se/integritetspolicy"
                                    >
                                        {strings.privacy_policy}
                                    </AnchorLink>,
                                )}
                            </p>
                        </div>
                        <InfoCard variant="error" message={errors.server} />
                        <Button fullWidth type="submit" onSubmit={submitForm} loading={isSubmitting || isValidating} disabled={!isCheck}>
                            {strings.createAnAccount}
                        </Button>
                        <p className="text-center font-medium">
                            {strings.formatString(strings.alreadyHaveAnAccount, <RouterLink to="/prescription/login">{strings.Login}</RouterLink>)}
                        </p>
                    </form>
                )}
            </Formik>
        );
    }
};

export default Signup;
