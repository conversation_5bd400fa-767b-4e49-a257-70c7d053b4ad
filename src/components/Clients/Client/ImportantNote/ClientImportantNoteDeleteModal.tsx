import Button from '@components/form/Button';
import api from '@configs/api';
import strings from '@lang/Lang';
import DeleteIcon from '@partials/Icons/Delete';
import DeleteModal from '@partials/MaterialModal/DeleteModal';
import React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';

export interface ClientImportantNoteDeleteModalProps {
    open?: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    mutate: () => Promise<any>;
    clientId: string;
}

const ClientImportantNoteDeleteModal: React.FC<ClientImportantNoteDeleteModalProps> = ({ open = false, setOpenModal, clientId, mutate }) => {
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    const navigate = useNavigate();
    return (
        <DeleteModal
            open={open}
            handleClose={() => {
                if (isSubmitting) return;
                setOpenModal(false);
            }}
            icon={<DeleteIcon />}
            text={`${strings.do_you_want_to_delete.replace(':name', strings.important_note.toLowerCase() || '')}`}
            submitButton={
                <Button
                    loading={isSubmitting}
                    onClick={async () => {
                        setIsSubmitting(true);
                        const response = await fetch(api.clientUpdate.replace(':id', clientId), {
                            method: 'POST',
                            headers: {
                                Accept: 'application/json',
                                
                                
                                'X-App-Locale': strings.getLanguage(),
                                'Content-Type': 'application/json',
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                important_note: '',
                            }),
                        });

                        const data = await response.json();

                        if (response.status === 401) {
                            navigate('/');
                        }

                        if (data.status === '1') {
                            await mutate();
                            toast.success(data.message);
                            setOpenModal(false);
                        } else {
                            toast.error(data.message || 'server error, please contact admin.');
                        }
                        setIsSubmitting(false);
                        setOpenModal(false);
                    }}
                >
                    {strings.Delete}
                </Button>
            }
        />
    );
};

export default ClientImportantNoteDeleteModal;
