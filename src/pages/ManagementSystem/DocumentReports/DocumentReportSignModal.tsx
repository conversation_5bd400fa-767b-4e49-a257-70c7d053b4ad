import MaterialSignturePad from '@partials/SignaturePad/MaterialSignaturePad';
import { useFormikContext } from 'formik';
import { useRef } from 'react';
import SignaturePad from 'signature_pad';
import { DynamicQuestionaryValuesExtra } from './DocumentReportsModal';
import Modal from '@partials/MaterialModal/Modal';
import strings from '@lang/Lang';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Button from '@components/form/Button';

export interface DocumentReportSignModalProps {
    openModal: boolean;
    onClose: () => void;
}

const DocumentReportSignModal: React.FC<DocumentReportSignModalProps> = ({ openModal, onClose }) => {
    const canvasRef = useRef<SignaturePad>(null);
    const { values, errors, touched, setFieldValue, setFieldTouched, handleSubmit, isSubmitting } = useFormikContext<DynamicQuestionaryValuesExtra>();

    const handleModelClose = async () => {
        await setFieldValue('signature', '');
        canvasRef.current?.clear();
        onClose();
    };

    return (
        <Modal
            open={openModal}
            title={strings.Sign}
            handleClose={handleModelClose}
            cancelButton={<CancelButton disabled={!!isSubmitting} onClick={handleModelClose}></CancelButton>}
            submitButton={
                <Button
                    loading={!!isSubmitting}
                    disabled={!!isSubmitting}
                    onClick={() => {
                        handleSubmit();
                        onClose();
                    }}
                >
                    {strings.Submit}
                </Button>
            }
            closeOnBackdropClick={false}
        >
            <div className="p-4">
                <MaterialSignturePad
                    ref={canvasRef}
                    onEnd={async () => {
                        if (!canvasRef || !canvasRef.current) return;
                        await setFieldValue('signature', canvasRef.current.toDataURL());
                        setFieldTouched('signature', true);
                    }}
                    error={touched?.signature && Boolean(errors.signature)}
                    helperText={touched?.signature && errors.signature}
                    onClear={async () => {
                        await setFieldValue('signature', '');
                        await setFieldTouched('signature', true);
                        canvasRef.current?.clear();
                    }}
                />
            </div>
        </Modal>
    );
};

export default DocumentReportSignModal;
