import ViewAllButton from '@components/form/ViewAllButton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { GiftPaginatedResponse } from '@interface/common';
import pos_strings from '@lang/pos/Lang';
import EmptyData from '@partials/Error/EmptyData';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Table from '@partials/Table/PageTable';
import ClientGiftCardListItem from './ClientGiftCardListItem';
import { Refund } from '@interface/model/refund';
import { useNavigate } from 'react-router';

const ClientGiftCard = () => {
    const { clientId } = useClient();
    const { data, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<GiftPaginatedResponse, Error>(
        api.pos.giftCard.list({ client_id: clientId }),
        {
            limit: 3,
        },
    );
    const navigate = useNavigate();

    if (loading) {
        return <SectionLoading />;
    }

    return (
        <>
            <Table>
                <Table.Head>
                    <Table.ThSort
                        sort={orderBy === 'gift_code' && orderDirection}
                        onClick={() => {
                            handleOrder('gift_code');
                        }}
                        children={pos_strings.gift_card.card_number}
                    />
                    <Table.ThSort
                        sort={orderBy === 'status' && orderDirection}
                        onClick={() => {
                            handleOrder('status');
                        }}
                        children={pos_strings.gift_card.status}
                    />
                    <Table.ThSort
                        sort={orderBy === 'expired_at' && orderDirection}
                        onClick={() => {
                            handleOrder('expired_at');
                        }}
                        children={pos_strings.gift_card.validity}
                    />
                    <Table.ThSort
                        sort={orderBy === 'current_value' && orderDirection}
                        onClick={() => {
                            handleOrder('current_value');
                        }}
                        children={`${pos_strings.gift_card.remaining}`}
                    />
                    <Table.Th children={pos_strings.action} className="text-right" />
                </Table.Head>
                <Table.Body>
                    {data?.data.map((gift) => (
                        <ClientGiftCardListItem
                            key={gift.id}
                            giftItem={gift}
                            onViewClick={() => {
                                if (!gift.payable) return;

                                if (gift.payable_type === 'App\\CompanyReceipt') {
                                    navigate(`receipt/${gift.payable.id}/detail`);
                                }
                                if (gift.payable_type === 'App\\CompanyReceiptRefund') {
                                    const refund = gift.payable as Refund;
                                    navigate(`receipt/${refund.receipt_id}/detail?refund_id=${refund.id}`);
                                }
                            }}
                        />
                    ))}
                    {data?.data.length === 0 ? (
                        <EmptyData cardHeight="h-[20vh]" />
                    ) : (
                        <tr>
                            <td colSpan={10}>{data?.data.length !== 0 && <ViewAllButton to={`/clients/${clientId}/giftCards`} />}</td>
                        </tr>
                    )}
                </Table.Body>
            </Table>
        </>
    );
};
export default ClientGiftCard;
