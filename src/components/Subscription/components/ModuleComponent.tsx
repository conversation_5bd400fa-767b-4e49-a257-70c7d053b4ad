import Button from '@components/form/Button';
import IconButton from '@components/form/IconButton';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import AddIcon from '@partials/Icons/Add';
import CheckIcon from '@partials/Icons/Check';
import ChevronIcon from '@partials/Icons/Chevron';
import DeleteIcon from '@partials/Icons/Delete';
import MinusIcon from '@partials/Icons/Minus';
import cx from 'classix';
import React, { ReactNode, useEffect } from 'react';
import { useLocation } from 'react-router';

export interface ModuleComponentProps {
    name: string;
    free_trial_text?: string;
    price: ReactNode;
    price_description: ReactNode;
    features: (React.ReactNode | string)[];
    onAddRemoveClick: (value: 'monthly' | 'yearly') => void;
    checked?: boolean;
    joinWaitlist?: ReactNode;
    slider?: {
        max: number;
        value: number;
        increase?: () => void;
        decrease?: () => void;
    };
    endDate?: string;
    children?: ReactNode;
    activePlan?: boolean;
    onTypeClick: (value: 'monthly' | 'yearly') => void;
    type: 'monthly' | 'yearly';
    activeType?: 'monthly' | 'yearly';
    removeCode?: boolean;
}

const ModuleComponent: React.FC<ModuleComponentProps> = ({
    name,
    free_trial_text,
    price,
    price_description,
    features,
    slider,
    onAddRemoveClick,
    checked = false,
    endDate,
    children,
    activePlan,
    joinWaitlist,
    onTypeClick,
    type,
    removeCode,
    activeType,
}) => {
    const [showDeatail, setShowDetail] = React.useState(checked);
    useEffect(() => {
        setShowDetail(checked);
    }, [checked]);

    const { pathname } = useLocation();
    const subscription = pathname === '/subscription';

    return (
        <div className="w-full space-y-5 rounded-lg bg-lightPurple/45 p-2 dark:bg-darkGray/80 md:p-6">
            {!subscription && (
                <div className="flex">
                    <div className="space-y-1">
                        <div className="flex justify-center">
                            <div className="flex rounded-md bg-lightPurple px-1 py-1 dark:bg-black/50">
                                <div
                                    className={cx(
                                        'flex justify-center rounded-[3px] font-normal',
                                        type === 'monthly'
                                            ? 'bg-white text-primary dark:bg-gray-800 dark:text-primaryLight'
                                            : 'text-gray-500 dark:text-white',
                                    )}
                                >
                                    <button className="px-7 py-1 text-center text-sm" onClick={() => onTypeClick('monthly')}>
                                        {strings.Monthly}
                                    </button>
                                </div>
                                <div
                                    className={cx(
                                        'flex justify-center rounded-[3px] px-3 font-normal',
                                        type === 'yearly'
                                            ? 'bg-white text-primary dark:bg-gray-800 dark:text-primaryLight'
                                            : 'text-gray-500 dark:text-white',
                                    )}
                                >
                                    <button className={cx('text-center text-sm', removeCode && 'px-6')} onClick={() => onTypeClick('yearly')}>
                                        {removeCode ? strings.Yearly : strings.yearly_save}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            <div className="flex justify-between gap-2">
                <div>
                    <div className="flex flex-wrap items-center gap-1 md:gap-2">
                        <p className="text-xl font-semibold">{name}</p>
                        {activePlan && activeType === type && <p className="text-success">({strings.Active})</p>}
                        {free_trial_text && <p className="text-base font-medium text-primary dark:text-primaryLight">({free_trial_text})</p>}
                    </div>
                    <div className="flex flex-wrap items-center">
                        {price && <p className="pr-2 text-lg font-medium">{price}</p>}
                        {price_description && <p className="text-sm text-gray-500 dark:text-gray-400">{price_description}</p>}
                    </div>
                    {endDate && (
                        <p className="text-xs text-red-500">
                            {strings.end_date}: {endDate}
                        </p>
                    )}
                </div>
                <div>
                    {!joinWaitlist &&
                        (slider?.decrease && slider?.increase && checked ? (
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    {slider?.value != 1 ? (
                                        <IconButton onClick={() => onAddRemoveClick(type)}>
                                            <DeleteIcon className="text-warning" />
                                        </IconButton>
                                    ) : (
                                        ''
                                    )}
                                    <div className="flex w-28 items-center justify-between gap-3 rounded-md border border-primary px-2 py-1 text-center dark:border-gray-600">
                                        {slider?.decrease ? (
                                            <button onClick={slider?.decrease}>
                                                {slider?.value == 1 ? <DeleteIcon className="text-lg" /> : <MinusIcon className="text-base" />}
                                            </button>
                                        ) : (
                                            <></>
                                        )}
                                        <p className="text-lg font-semibold">{slider?.value}</p>
                                        {slider?.increase ? (
                                            <button onClick={slider?.increase}>
                                                <AddIcon className="text-lg" />
                                            </button>
                                        ) : (
                                            <></>
                                        )}
                                    </div>
                                </div>
                                <p className="text-right text-sm text-gray-500 dark:text-gray-400">{strings.select_users}</p>
                            </div>
                        ) : (
                            <div className="flex w-full items-center gap-2">
                                {checked && (
                                    <div className="flex justify-center">
                                        <IconButton onClick={() => onAddRemoveClick(type)} name={strings.Delete}>
                                            <DeleteIcon className="text-warning" />
                                        </IconButton>
                                    </div>
                                )}
                                <Button
                                    size="small"
                                    variant={checked ? 'ghost' : 'filled'}
                                    className={cx('w-full dark:bg-gray-700', !checked && 'px-5')}
                                    onClick={() => (checked ? undefined : onAddRemoveClick(type))}
                                >
                                    {checked ? pos_strings.added : strings.add}
                                </Button>
                            </div>
                        ))}
                    {joinWaitlist}
                </div>
            </div>
            <div className="space-y-2">
                <p
                    className="flex cursor-pointer items-center gap-1 text-sm text-primary dark:text-primaryLight"
                    onClick={() => setShowDetail(!showDeatail)}
                >
                    {strings.show_details}
                    <ChevronIcon side={showDeatail ? 'top' : 'bottom'} />
                </p>
                {showDeatail && (
                    <div className="space-y-6">
                        <div className="grid grid-cols-1 content-center gap-4 md:grid-cols-2">
                            {features.map((feature, index) => (
                                <div className="flex items-center space-x-2" key={index}>
                                    <CheckIcon className="rounded-full bg-primary/10 p-0.5 text-primary dark:border dark:border-gray-600 dark:bg-transparent dark:text-white" />
                                    <p className="whitespace-pre-wrap text-sm">{feature}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
            {children}
        </div>
    );
};

export default ModuleComponent;
