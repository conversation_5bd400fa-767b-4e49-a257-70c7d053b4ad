import strings from '@lang/Lang';

type QuestionType = {
    text: string;
    value: string;
    visible2Client?: boolean;
};

export const questionTypes: QuestionType[] = [
    {
        text: strings.yes_no,
        value: 'yes_no',
    },
    {
        text: strings.yes_no_textbox,
        value: 'yes_no_textbox',
    },
    {
        text: strings.textbox,
        value: 'textbox',
    },
    {
        text: strings.file_upload,
        value: 'file_upload',
    },
    {
        text: 'Rich Text',
        value: 'html_editor',
        visible2Client: false,
    },
];

export const questionTypeVisible2Client = questionTypes.filter((v) => v.visible2Client !== false);
