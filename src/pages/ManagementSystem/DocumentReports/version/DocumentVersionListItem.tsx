import { generateUserFullName } from '@/helper';
import IconButton from '@components/form/IconButton';
import LoadingIcon from '@icons/Loading';
import { DocumentVersion } from '@interface/model/document';
import strings from '@lang/Lang';
import DownloadIcon from '@partials/Icons/Download';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';

export interface DocumentVersionListItemProps {
    version: DocumentVersion;
    onView: (document: DocumentVersion) => void;
    onDownload: (document: DocumentVersion) => void;
    isDownloading?: boolean;
}

const DocumentVersionListItem: React.FC<DocumentVersionListItemProps> = ({ version, onView, onDownload, isDownloading }) => {
    const { renderDate } = useHours();

    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className="alternate-tr-desktop hidden rounded-none md:table-row">{desktopListItem()}</tr>
        </>
    );

    function mobileListItem() {
        return (
            <Table.Td>
                <div className="space-y-2">
                    <div className="flex justify-between">
                        <span className="">
                            {strings.version}: {version.version}
                        </span>
                        <span>{renderDate(version.created_at)}</span>
                    </div>
                    <div className="break-all">
                        {strings.Signed_by}: {generateUserFullName(version.signed_by)}
                    </div>
                    <div className="flex space-x-1.5">
                        <IconButton onClick={() => onView(version)} name={strings.View} children={<EyeIcon />} />
                        <IconButton onClick={() => onDownload(version)} name={strings.download}>
                            {isDownloading ? <LoadingIcon /> : <DownloadIcon />}
                        </IconButton>
                    </div>
                </div>
            </Table.Td>
        );
    }

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <div className="break-all">{version.version}</div>
                </Table.Td>
                <Table.Td>
                    <div className="break-all">{renderDate(version.created_at)}</div>
                </Table.Td>
                <Table.Td>
                    <div className="break-all">{generateUserFullName(version.signed_by)}</div>
                </Table.Td>
                <Table.Td className="p-2">
                    <div className="flex justify-end space-x-1.5">
                        <IconButton onClick={() => onView(version)} name={strings.View} children={<EyeIcon />} />
                        <IconButton onClick={() => onDownload(version)} name={strings.download}>
                            {isDownloading ? <LoadingIcon /> : <DownloadIcon />}
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }
};

export default DocumentVersionListItem;
