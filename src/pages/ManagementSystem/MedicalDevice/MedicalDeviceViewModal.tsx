import { getUrlExtension } from '@/helper';
import IconButton from '@components/form/IconButton';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import { MedicalDeviceManagement } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import DownloadIcon from '@partials/Icons/Download';
import Modal from '@partials/MaterialModal/Modal';
import ViewModalTextItem from '@partials/ViewModal/ViewModalTextItem';
import { useState } from 'react';
import MedicalDeviceFileViewModal from './FileViewModal';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import EyeIcon from '@partials/Icons/Eye';

export interface MedicalDeviceViewModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    device: MedicalDeviceManagement;
}

export interface ViewFileModel {
    name: string;
    url: string;
}

const MedicalDeviceViewModal: React.FC<MedicalDeviceViewModalProps> = ({ openModal, setOpenModal, device }) => {
    const handleModelClose = async () => {
        setOpenModal(false);
    };
    const [downloading, setDownloading] = useState<string | undefined>();
    const [fileViewOpen, setFileViewOpen] = useState<boolean>(false);
    const [viewFile, setViewFile] = useState<ViewFileModel>();

    return (
        <>
            <ModalSuspense>
                {fileViewOpen && !!viewFile && (
                    <MedicalDeviceFileViewModal
                        openModal={fileViewOpen}
                        onClose={() => {
                            setViewFile(undefined);
                            setFileViewOpen(false);
                        }}
                        file_name={viewFile.name}
                        file={viewFile.url}
                    />
                )}
            </ModalSuspense>
            <Modal open={fileViewOpen ? false : openModal} title={strings.medical_device} handleClose={handleModelClose} closeOnBackdropClick={false}>
                <div className="p-4">
                    <ViewModalTextItem title={strings.device_name} value={device.product_name} showIfEmpty />
                    <ViewModalTextItem title={strings.model} value={device.model} showIfEmpty />
                    <ViewModalTextItem title={strings.brand} value={device.brand} showIfEmpty />
                    <ViewModalTextItem title={strings.serial_number} value={device.serial_number} showIfEmpty />
                    <ViewModalTextItem title={strings.supplier} value={device.supplier} showIfEmpty />
                    {/* <ViewModalTextItem title={strings.performed_maintenance} value={device.performed_maintenance} showIfEmpty />
                    <ViewModalTextItem title={strings.upcoming_maintenance} value={device.upcoming_maintenance} showIfEmpty /> */}
                    <ViewModalTextItem
                        title={strings.compliance_declaration_check_performed}
                        value={device.compliance_declared ? strings.Yes : strings.No}
                        showIfEmpty
                    />
                    {!!device.upload_manual?.length && (
                        <div className="flex w-full items-center gap-x-3 py-2">
                            <p className="flex-grow break-all text-sm uppercase text-primary dark:text-primaryLight">{strings.upload_manuals}</p>
                            <div className="flex">
                                <IconButton
                                    className="focus:outline-none"
                                    onClick={() => {
                                        setFileViewOpen(true);
                                        setViewFile({
                                            name: strings.upload_manuals,
                                            url: api.storageUrl(device.upload_manual ?? ''),
                                        });
                                    }}
                                    children={<EyeIcon />}
                                />
                                <IconButton
                                    className="focus:outline-none"
                                    onClick={() => onDownloadClick(device.upload_manual ?? '', 'upload_manuals')}
                                >
                                    {downloading === 'upload_manuals' ? <LoadingIcon /> : <DownloadIcon />}
                                </IconButton>
                            </div>
                        </div>
                    )}
                    {!!device.supplier_agreement?.length && (
                        <div className="flex w-full items-center gap-x-3 py-2">
                            <p className="flex-grow break-all text-sm uppercase text-primary dark:text-primaryLight">{strings.supplier_agreements}</p>
                            <div className="flex">
                                <IconButton
                                    className="focus:outline-none"
                                    onClick={() => {
                                        setFileViewOpen(true);
                                        setViewFile({
                                            name: strings.supplier_agreements,
                                            url: api.storageUrl(device.supplier_agreement ?? ''),
                                        });
                                    }}
                                    children={<EyeIcon />}
                                />
                                <IconButton
                                    className="focus:outline-none"
                                    onClick={() => onDownloadClick(device.supplier_agreement ?? '', '.supplier_agreement')}
                                >
                                    {downloading === '.supplier_agreement' ? <LoadingIcon /> : <DownloadIcon />}
                                </IconButton>
                            </div>
                        </div>
                    )}
                </div>
            </Modal>
        </>
    );

    async function onDownloadClick(file: string, name: string) {
        setDownloading(name);

        const response = await fetch(api.storageUrl(file));

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);

        a.target = '_blank';

        const ext = getUrlExtension(file) ?? '';

        a.setAttribute('download', `${name}.${ext}`);
        a.click();

        setDownloading(undefined);
    }
};

export default MedicalDeviceViewModal;
