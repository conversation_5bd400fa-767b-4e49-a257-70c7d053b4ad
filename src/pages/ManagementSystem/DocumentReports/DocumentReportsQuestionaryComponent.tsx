import api from '@configs/api';
import { AdminQuestionaryQuestionResponse, SMSPrescriptionTemplate } from '@interface/common';
import NoDataText from '@partials/Error/NoDataText';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import DynamicQuestionaryComponent from '@partials/Qestionary/DynamicQuestionaryComponent';
import useSWR from 'swr';

export interface DocumentReportsQuestionaryComponentProps {
    selectedQuestionary: SMSPrescriptionTemplate;
}

const DocumentReportsQuestionaryComponent: React.FC<DocumentReportsQuestionaryComponentProps> = ({ selectedQuestionary }) => {
    const {
        data: questionnaireQuestionData,
        error,
        isLoading,
    } = useSWR<AdminQuestionaryQuestionResponse, Error>(api.generalTemplateQuestionList(selectedQuestionary?.id));

    if (isLoading) return <SectionLoading />;
    if (!questionnaireQuestionData?.data.length) return <NoDataText />;
    return <DynamicQuestionaryComponent />;
};

export default DocumentReportsQuestionaryComponent;
