import { useHours } from '@provider/TimeProvider';
import * as React from 'react';
import { Log } from '../../../../interfaces/model/log';

export interface ClientLogListItemProps {
    log: Log;
}

const DocumentReportsLogListItem: React.FC<ClientLogListItemProps> = ({ log }) => {
    const { renderDateTime } = useHours();
    return (
        <tr className="border-b dark:border-gray-700">
            <td>
                <div className="py-2 pl-3">
                    <p className="">{log.description}</p>
                </div>
            </td>
            <td>
                <p>{renderDateTime(log.created_at, { utc: true, isShowLocal: true })}</p>
            </td>
        </tr>
    );
};

export default DocumentReportsLogListItem;
