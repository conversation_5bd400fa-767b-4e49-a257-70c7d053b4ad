import * as React from 'react';
import { SVGProps } from 'react';
const RoboChatIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="1em" height="1em" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M34.0844 15.125H26.5625V12.875C26.5625 12.6429 26.4703 12.4204 26.3062 12.2563C26.1421 12.0922 25.9196 12 25.6875 12H24.3125C24.0804 12 23.8579 12.0922 23.6938 12.2563C23.5297 12.4204 23.4375 12.6429 23.4375 12.875V15.125H15.9156C14.347 15.1258 12.8428 15.7493 11.7336 16.8586C10.6243 17.9678 10.0008 19.472 10 21.0406V24.5656C10 28.4436 11.5405 32.1627 14.2826 34.9049C17.0248 37.647 20.7439 39.1875 24.6219 39.1875H25.6875C25.9196 39.1875 26.1421 39.0953 26.3062 38.9312C26.4703 38.7671 26.5625 38.5446 26.5625 38.3125V36.9375C26.5625 36.7054 26.4703 36.4829 26.3062 36.3188C26.1421 36.1547 25.9196 36.0625 25.6875 36.0625H24.6219C21.5727 36.0625 18.6484 34.8512 16.4924 32.6951C14.3363 30.5391 13.125 27.6148 13.125 24.5656V21.0406C13.125 20.3005 13.419 19.5907 13.9424 19.0674C14.4657 18.544 15.1755 18.25 15.9156 18.25H34.0844C34.8245 18.25 35.5343 18.544 36.0576 19.0674C36.581 19.5907 36.875 20.3005 36.875 21.0406V24.8125C36.873 25.6699 36.7681 26.5239 36.5625 27.3562C36.508 27.5707 36.5371 27.7979 36.6439 27.9917C36.7506 28.1855 36.9271 28.3315 37.1375 28.4L38.45 28.8281C38.5634 28.8657 38.6833 28.8797 38.8022 28.869C38.9212 28.8584 39.0367 28.8234 39.1417 28.7663C39.2466 28.7091 39.3386 28.6311 39.4121 28.5369C39.4855 28.4427 39.5389 28.3344 39.5688 28.2188C39.8529 27.1056 39.9978 25.9614 40 24.8125V21.0406C39.9992 19.472 39.3757 17.9678 38.2664 16.8586C37.1572 15.7493 35.653 15.1258 34.0844 15.125Z"
            fill="white"
        />
        <path
            d="M20 28.25C21.3807 28.25 22.5 27.1307 22.5 25.75C22.5 24.3693 21.3807 23.25 20 23.25C18.6193 23.25 17.5 24.3693 17.5 25.75C17.5 27.1307 18.6193 28.25 20 28.25Z"
            fill="white"
        />
        <path
            d="M30 28.25C31.3807 28.25 32.5 27.1307 32.5 25.75C32.5 24.3693 31.3807 23.25 30 23.25C28.6193 23.25 27.5 24.3693 27.5 25.75C27.5 27.1307 28.6193 28.25 30 28.25Z"
            fill="white"
        />
        <path
            d="M39.9156 37.2492L42.7469 38.3648C42.8052 38.3879 42.8552 38.4279 42.8904 38.4797C42.9257 38.5315 42.9445 38.5928 42.9445 38.6555C42.9445 38.7181 42.9257 38.7794 42.8904 38.8312C42.8552 38.883 42.8052 38.923 42.7469 38.9461L39.9156 40.0617C39.4144 40.2599 38.9592 40.5587 38.5781 40.9398C38.197 41.3209 37.8982 41.7761 37.7 42.2773L36.5844 45.0898C36.5613 45.1481 36.5213 45.1981 36.4695 45.2334C36.4177 45.2686 36.3564 45.2875 36.2938 45.2875C36.2311 45.2875 36.1699 45.2686 36.118 45.2334C36.0662 45.1981 36.0262 45.1481 36.0031 45.0898L34.8875 42.2773C34.6898 41.7758 34.3911 41.3204 34.01 40.9392C33.6288 40.5581 33.1734 40.2594 32.6719 40.0617L29.8594 38.9461C29.8011 38.923 29.7511 38.883 29.7158 38.8312C29.6806 38.7794 29.6617 38.7181 29.6617 38.6555C29.6617 38.5928 29.6806 38.5315 29.7158 38.4797C29.7511 38.4279 29.8011 38.3879 29.8594 38.3648L32.6719 37.2492C33.1734 37.0515 33.6288 36.7528 34.01 36.3717C34.3911 35.9905 34.6898 35.5351 34.8875 35.0336L36.0031 32.2211C36.0262 32.1628 36.0662 32.1128 36.118 32.0775C36.1699 32.0423 36.2311 32.0234 36.2938 32.0234C36.3564 32.0234 36.4177 32.0423 36.4695 32.0775C36.5213 32.1128 36.5613 32.1628 36.5844 32.2211L37.7 35.0336C37.8982 35.5348 38.197 35.99 38.5781 36.3711C38.9592 36.7522 39.4144 37.051 39.9156 37.2492Z"
            fill="white"
        />
    </svg>
);
export default RoboChatIcon;
