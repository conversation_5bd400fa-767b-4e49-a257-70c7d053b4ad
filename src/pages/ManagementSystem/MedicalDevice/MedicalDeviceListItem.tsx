import IconButton from '@components/form/IconButton';
import LogIcon from '@icons/Log';
import { MedicalDeviceManagement } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import DeleteIcon from '@partials/Icons/Delete';
import EditIcon from '@partials/Icons/Edit';
import EyeIcon from '@partials/Icons/Eye';
import PowerIcon from '@partials/Icons/Power';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import * as React from 'react';
import { useNavigate } from 'react-router';

export interface MedicalDeviceListItemProps {
    onEditClick?: (data: MedicalDeviceManagement) => void;
    onViewClick?: (data: MedicalDeviceManagement) => void;
    onDeleteClick?: (data: MedicalDeviceManagement) => void;
    onMaintenanceClick?: () => void;
    medicalDevice: MedicalDeviceManagement;
}

const MedicalDeviceListItem: React.FC<MedicalDeviceListItemProps> = ({
    onEditClick = () => {},
    medicalDevice,
    onViewClick = () => {},
    onDeleteClick = () => {},
    onMaintenanceClick = () => {},
}) => {
    const navigate = useNavigate();

    const editClick = () => onEditClick(medicalDevice);
    const viewClick = () => onViewClick(medicalDevice);
    const deleteClick = () => onDeleteClick(medicalDevice);
    function onLogClick() {
        navigate(`/management-system/medical-device/${medicalDevice.id}/logs`);
    }
    const { renderDate } = useHours();

    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className="alternate-tr-desktop hidden md:table-row">{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <p className={cx('line-clamp-2 max-w-2xl overflow-ellipsis break-all', medicalDevice?.deleted_at && 'text-mediumGray')}>
                        {medicalDevice.product_name}
                    </p>
                </Table.Td>
                <Table.Td>
                   {renderDate(medicalDevice.created_at)}
                </Table.Td>
                <Table.Td className="cursor-pointer p-4 font-medium text-primary underline dark:text-primaryLight" onClick={onMaintenanceClick}>
                    {medicalDevice.maintenances_count}
                </Table.Td>
                <Table.Td className="p-2">
                    <div className="flex justify-end space-x-1.5">
                        <IconButton onClick={editClick} name={strings.edit}>
                            <EditIcon />
                        </IconButton>
                        <IconButton onClick={viewClick} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                        <IconButton onClick={onLogClick}>
                            <LogIcon />
                        </IconButton>
                        <IconButton onClick={deleteClick} name={!medicalDevice.deleted_at ? strings.InActive : strings.Active}>
                            <PowerIcon slash={!medicalDevice.deleted_at} />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }
    function mobileListItem() {
        return (
            <>
                <Table.Td>
                    <div className="flex justify-between gap-4">
                        <p className="line-clamp-2 max-w-64 overflow-ellipsis break-all">{medicalDevice.product_name}</p>
                        <p>{renderDate(medicalDevice.created_at)}</p>
                    </div>
                    <div className="flex gap-2">
                        {strings.maintenance_doc} :
                        <p className="cursor-pointer font-medium text-primary underline dark:text-primaryLight" onClick={onMaintenanceClick}>
                            {medicalDevice.maintenances_count}
                        </p>
                    </div>
                    <div className="-ml-2">
                        <IconButton onClick={editClick}>
                            <EditIcon />
                        </IconButton>
                        <IconButton onClick={viewClick}>
                            <EyeIcon />
                        </IconButton>
                        <IconButton onClick={onLogClick}>
                            <LogIcon />
                        </IconButton>
                        <IconButton onClick={deleteClick} name={!medicalDevice.deleted_at ? strings.InActive : strings.Active}>
                            <PowerIcon slash={!medicalDevice.deleted_at} />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }
};

export default MedicalDeviceListItem;
