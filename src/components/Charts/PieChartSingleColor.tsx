import { customRound } from '@/helper';
import cx from 'classix';
import { CSSProperties, useCallback, useState } from 'react';
import { Cell, Pie, PieChart, ResponsiveContainer, Sector, Tooltip } from 'recharts';
import { PieSectorDataItem } from 'recharts/types/polar/Pie';

export interface PieChartProps {
    data: PieChartData[];
    color: 'violet' | 'orange' | 'blue';
}

export const chartColors = {
    primary: '#5551CE',
    violet: '#C893FD',
    blue: '#8995FF',
    blueLight: '#D6DAFF',
    violetLight: '#EAD6FF',
    orange: '#FFB156',
    orangeLight: '#FFE2C0',
};

export type PieChartData = { name: string; value: number };

const colors: {
    [key in PieChartProps['color']]: { color: string; lightColor: string };
} = {
    violet: { color: chartColors.violet, lightColor: chartColors.violetLight },
    blue: { color: chartColors.blue, lightColor: chartColors.blueLight },
    orange: { color: chartColors.orange, lightColor: chartColors.orangeLight },
};

type MouseEvent = (data: any, index: number, e: React.MouseEvent) => void;

const PieChartSingleColorComponent: React.FC<PieChartProps> = ({ data, color: selectedColor }) => {
    const [activeIndex, setActiveIndex] = useState<number>();

    const color = colors[selectedColor];

    const activeShape = useCallback((props: PieSectorDataItem) => {
        return (
            <g>
                <text x={props.cx} y={props.cy} dy={0} textAnchor="middle" className="fill-black dark:fill-white">
                    {props.payload.name}
                </text>
                <text x={props.cx} y={props.cy} dy={16} textAnchor="middle" className="fill-black dark:fill-white">
                    {customRound(props.payload.value, 0)}
                </text>
                <Sector {...props} outerRadius={(props.outerRadius ?? 0) + 3} />
            </g>
        );
    }, []);

    const onMouseEnter: MouseEvent = useCallback((_, index) => setActiveIndex(index), []);
    const onMouseLeave: MouseEvent = useCallback(() => setActiveIndex(undefined), []);

    return (
        <div className="flex h-full w-full flex-col">
            <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                    <Pie
                        dataKey="value"
                        data={data}
                        cx="50%"
                        cy="50%"
                        innerRadius={58}
                        outerRadius={100}
                        startAngle={180}
                        endAngle={-180}
                        activeShape={activeShape}
                        onMouseEnter={onMouseEnter}
                        onMouseLeave={onMouseLeave}
                        stroke="none"
                    >
                        {data.map((entry, index) => {
                            return <Cell key={index} fill={index === 0 ? color.color : color.lightColor} />;
                        })}
                    </Pie>
                </PieChart>
            </ResponsiveContainer>
            <div className="mx-auto w-full max-w-64 space-y-1">
                {data.map((entry, index) => {
                    const selected = activeIndex === index;
                    return (
                        <div key={entry.name} className={cx('flex items-center justify-between', selected && 'font-medium')}>
                            <div className="flex items-center space-x-2">
                                <div
                                    className={cx('size-5 rounded-md duration-150', selected ? 'scale-125' : '')}
                                    style={{ backgroundColor: index === 0 ? color.color : color.lightColor }}
                                ></div>
                                <span>{entry.name}</span>
                            </div>
                            <span>{customRound(entry.value, 0)}</span>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default PieChartSingleColorComponent;
