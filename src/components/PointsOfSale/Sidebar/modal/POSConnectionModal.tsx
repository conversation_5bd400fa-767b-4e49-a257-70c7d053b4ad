import Button from '@components/form/Button';
import pos_strings from '@lang/pos/Lang';
import FortnoxIcon from '@partials/Icons/Fortnox';
import InfrasecIcon from '@partials/Icons/Infrasec';
import VivaWalletIcon from '@partials/Icons/VivaWallet';
import Modal from '@partials/MaterialModal/Modal';
import React, { useState } from 'react';
export interface VivaWalletConnectionProps {
    open?: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    vivaConnected?: boolean;
    infrasecConnected?: boolean;
    fortnoxConnected?: boolean;
    onClickViva?: () => Promise<void>;
    onClickInfrasec?: () => void;
    onClickFortnox?: () => Promise<void>;
}

const POSConnectionModal: React.FC<VivaWalletConnectionProps> = ({
    open,
    setOpen,
    infrasecConnected = false,
    vivaConnected = false,
    fortnoxConnected = false,
    onClickInfrasec,
    onClickViva,
    onClickFortnox,
}) => {
    const [loading, setLoading] = useState(false);

    return (
        <Modal
            open={open}
            handleClose={() => {
                setOpen(false);
            }}
            noScroll
            title="Connect"
        >
            <div className="flex w-full flex-col items-center justify-start gap-4 divide-y-[1px] divide-lightPurple p-6 dark:divide-dimGray">
                <div className="flex w-full items-center justify-start gap-6">
                    <InfrasecIcon className="" fontSize={44} />
                    <div className="flex flex-col items-start justify-center gap-1">
                        <p className="text-base font-medium">{`${pos_strings.infrasec.infrasec}`}</p>
                        <p className="text-sm font-normal text-mediumGray">{`${pos_strings.infrasec.for_tax_compliance}`}</p>
                    </div>
                    <div className="flex flex-1 items-center justify-end">
                        <Button variant={infrasecConnected ? 'ghost' : 'filled'} onClick={() => {
                            if(infrasecConnected) return;
                            if(onClickInfrasec) onClickInfrasec();
                        }} className="!py-2">
                            {infrasecConnected ? pos_strings.infrasec.connected : pos_strings.infrasec.connect}
                        </Button>
                    </div>
                </div>
                <div className="flex w-full items-center justify-start gap-6 pt-4">
                    <VivaWalletIcon className="" fontSize={44} />
                    <div className="flex flex-col items-start justify-center gap-1">
                        <p className="text-base font-medium">{`${pos_strings.viva.viva_wallet}`}</p>
                        <p className="text-sm font-normal text-mediumGray">{`${pos_strings.viva.for_payment_solutions}`}</p>
                    </div>
                    <div className="flex flex-1 items-center justify-end">
                        <Button
                            loading={loading}
                            variant={vivaConnected ? 'ghost' : 'filled'}
                            onClick={async () => {
                                if(vivaConnected) return;
                                if (onClickViva) {
                                    try {
                                        setLoading(true);
                                        await onClickViva();
                                        setLoading(false);
                                    } catch (error) {
                                        setLoading(false);
                                    }
                                }
                            }}
                            className="!py-2"
                        >
                            {vivaConnected ? pos_strings.viva.connected : pos_strings.viva.connect}
                        </Button>
                    </div>
                </div>
                <div className="flex w-full items-center justify-start gap-6 pt-4">
                    <FortnoxIcon className="" fontSize={44} />
                    <div className="flex flex-col items-start justify-center gap-1">
                        <p className="text-base font-medium">{`${pos_strings.fortnox.fortnox}`}</p>
                        <p className="text-sm font-normal text-mediumGray">{`${pos_strings.fortnox.for_invoice_solutions}`}</p>
                    </div>
                    <div className="flex flex-1 items-center justify-end">
                        <Button
                            loading={loading}
                            variant={fortnoxConnected ? 'ghost' : 'filled'}
                            onClick={async () => {
                                if(fortnoxConnected) return;
                                if (onClickFortnox) {
                                    try {
                                        setLoading(true);
                                        await onClickFortnox();
                                        setLoading(false);
                                    } catch (error) {
                                        setLoading(false);
                                    }
                                }
                            }}
                            className="!py-2"
                        >
                            {fortnoxConnected ? pos_strings.fortnox.connected : pos_strings.fortnox.connect}
                        </Button>
                    </div>
                </div>
            </div>
        </Modal>
    );
};
export default POSConnectionModal;
