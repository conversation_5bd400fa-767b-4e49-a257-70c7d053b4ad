import api from '@configs/api';
import strings from '@lang/Lang';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { FormikProps, useFormik } from 'formik';
import React, { createContext, lazy, PropsWithChildren, useContext } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
const CreateCampaigns = lazy(() => import('@components/SMS/Marketing/CreateCampaignsModal'));
const SMSDetailsModal = lazy(() => import('@components/SMS/Marketing/SMSDetailsModal'));
const SMSSendModal = lazy(() => import('@components/SMS/Marketing/SMSSendModal'));
const SMSDetailsClientListViewModal = lazy(() => import('@components/SMS/Marketing/SMSDetailsClientListViewModal'));

export type TargetType = 'all_clients' | 'new_clients' | 'birthday' | 'custom_segments' | undefined;
export type ConditionType = 'joined' | 'age' | 'upcoming_birthday' | 'booking' | undefined;
export interface IMarketingValues {
    client_type: TargetType;
    service: number[];
    message_name: string;
    description: string;
    template_id?: number;
    server?: string;
}
export interface SMSSendModalProps {}
interface MarketingContext {
    formik: FormikProps<IMarketingValues>;
    detailsModal: boolean;
    setDetailsModal: React.Dispatch<React.SetStateAction<boolean>>;
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    openCampaignsModal: boolean;
    setOpenCampaignsModal: React.Dispatch<React.SetStateAction<boolean>>;
    filterCondition: condition[];
    setFilterCondition: React.Dispatch<React.SetStateAction<condition[]>>;
    openClientListModal: boolean;
    setOpenClientListModal: React.Dispatch<React.SetStateAction<boolean>>;
}
export interface condition {
    type: ConditionType;
    value: string;
    key: string;
    error?: string;
}
const SMSMarketingContext = createContext<MarketingContext>({} as MarketingContext);

const SMSMarketingProvider: React.FC<PropsWithChildren<SMSSendModalProps>> = ({ children }) => {
    const defualtValue = {
        type: undefined,
        value: '',
        key: '',
    };
    const [detailsModal, setDetailsModal] = React.useState(false);
    const [openModal, setOpenModal] = React.useState(false);
    const [openCampaignsModal, setOpenCampaignsModal] = React.useState(false);
    const [openClientListModal, setOpenClientListModal] = React.useState(false);
    const [filterCondition, setFilterCondition] = React.useState<condition[]>([defualtValue]);
    const navigate = useNavigate();
    const formik = useFormik<IMarketingValues>({
        initialValues: {
            client_type: undefined,
            message_name: '',
            service: [],
            description: '',
            template_id: 0,
        },
        enableReinitialize: true,
        validate: (values) => validateSMSMarketing(values),
        onSubmit: async (values, { setSubmitting, setFieldError, resetForm }) => {
            const formData = new FormData();
            formData.set('name', values.message_name);
            formData.set('content', values?.description);
            filterCondition
                .filter((v) => v.type)
                .forEach((item, index) => {
                    if (item.type === 'booking') {
                        formData.set(`client_filter[${index}][type]`, 'booking');
                        formData.set(`client_filter[${index}][key]`, item?.key?.endsWith('s') ? item.key.slice(0, -1) : item.key);
                        formData.set(`client_filter[${index}][value][date]`, item.value);
                        values.service.forEach((element, ind) => {
                            formData.set(`client_filter[${index}][value][services][${ind}]`, element.toString());
                        });
                    } else {
                        formData.set(`client_filter[${index}][type]`, item?.type || '');
                        formData.set(`client_filter[${index}][key]`, item?.key?.endsWith('s') ? item.key.slice(0, -1) : item.key);
                        formData.set(`client_filter[${index}][value]`, item.value);
                    }
                });
            formData.set(`client_filter[${filterCondition.length + 1}][type]`, 'has_mobile_number');
            formData.set(`client_filter[${filterCondition.length + 1}][key]`, 'day');
            formData.set(`client_filter[${filterCondition.length + 1}][value]`, '1');

            const response = await fetch(api.sms.marketing.store, {
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    
                    
                    'X-App-Locale': strings.getLanguage(),
                },
                credentials: 'include',
                body: formData,
            });

            const data = await response.json();

            if (response.status === 401) {
                navigate('/');
            }

            if (data.status === '1') {
                setOpenModal(false);
                setDetailsModal(false);
                await resetForm();
                toast.success(data.message);
            } else {
                setFieldError('server', data.message || 'server error, please contact admin.');
            }
            setSubmitting(false);
        },
    });
    return (
        <SMSMarketingContext.Provider
            value={{
                formik: formik,
                detailsModal,
                setDetailsModal,
                openModal,
                setOpenModal,
                openCampaignsModal,
                setOpenCampaignsModal,
                filterCondition,
                setFilterCondition,
                openClientListModal,
                setOpenClientListModal,
            }}
        >
            <ModalSuspense>
                {detailsModal && (
                    <SMSDetailsModal
                        openModal={detailsModal}
                        handleClose={() => {
                            setDetailsModal(false);
                            setOpenCampaignsModal(true);
                        }}
                    />
                )}
                {openModal && (
                    <SMSSendModal
                        openModal={openModal}
                        handleClose={() => {
                            setOpenModal(false);
                            setDetailsModal(true);
                        }}
                    />
                )}
                {openCampaignsModal && (
                    <CreateCampaigns
                        openModal={openCampaignsModal}
                        handleClose={() => {
                            setOpenCampaignsModal(false);
                            formik.resetForm();
                            formik.setFieldValue('template_id', '');
                            setFilterCondition([defualtValue]);
                        }}
                    />
                )}
                {openClientListModal && (
                    <SMSDetailsClientListViewModal
                        openModal={openClientListModal}
                        handleClose={() => {
                            setDetailsModal(true);
                            setOpenClientListModal(false);
                        }}
                        filterCondition={filterCondition}
                        serviceIds={formik.values.service}
                    />
                )}
            </ModalSuspense>
            {children}
        </SMSMarketingContext.Provider>
    );
};

const validateSMSMarketing = (values: IMarketingValues) => {
    const errors: any = {};
    if (!values.message_name) {
        errors.message_name = strings.message_is_required;
    }
    if (!values.description) {
        errors.description = strings.description_is_required;
    }
    return errors;
};

export const useSMSMarketing = () => {
    return useContext(SMSMarketingContext);
};

export default SMSMarketingProvider;
