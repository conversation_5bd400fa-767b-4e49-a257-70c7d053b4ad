import api from '@configs/api';
import { ServiceResponse } from '@interface/common';
import { Marketing } from '@interface/model/marketing';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';
import ViewModalTextItem from '@partials/ViewModal/ViewModalTextItem';
import { useHours } from '@provider/TimeProvider';
import useSWR from 'swr';

export interface SMSMarketingViewProps {
    openModal: boolean;
    selectedMarketing?: Marketing;
    handleClose: () => void;
    setClientListModal: (value: boolean) => void;
}
const SMSMarketingView: React.FC<SMSMarketingViewProps> = ({ handleClose, openModal, selectedMarketing, setClientListModal }) => {
    const { renderDateTime } = useHours();
    const { data: servicesData } = useSWR<ServiceResponse, Error>(api.servicePractitionersAll({ userIds: [] }));
    const selectedServices = selectedMarketing?.client_filter.find((item) => item.type === 'booking')?.value;
    const servicesName =
        selectedServices?.services?.map((val: any) => servicesData?.data.find((value) => val == value.id)?.name).join(', ') ||
        strings.any.toLowerCase();

    return (
        <Modal open={openModal} title={strings.marketing} handleClose={handleClose}>
            <div className="p-4">
                <ViewModalTextItem title={strings.name_of_message} value={selectedMarketing?.name} showIfEmpty />
                <ViewModalTextItem title={strings.description} value={selectedMarketing?.content} showIfEmpty />
                <div className="mb-4">
                    <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.send_to}</p>
                    <span
                        className="cursor-pointer underline"
                        onClick={() => {
                            handleClose();
                            setClientListModal(true);
                        }}
                    >{`${selectedMarketing?.client_count} ${selectedMarketing?.client_count === '1' ? strings.client.toLowerCase() : strings.Clients.toLowerCase()}`}</span>
                </div>
                <ViewModalTextItem title={strings.credit_used} value={selectedMarketing?.credit_used} showIfEmpty />
                <ViewModalTextItem title={strings.failed} value={selectedMarketing?.failed?.toString()} showIfEmpty />
                <div className="mb-4">
                    <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.client_filter}</p>
                    {selectedMarketing?.client_filter?.map((item, index) => (
                        <div key={index} className="pt-1">
                            {item.type === 'joined' && (
                                <p>
                                    {strings.target_client_who_joined} {strings.in_last + ' ' + item.value + ' ' + item.key}
                                </p>
                            )}
                            {item.type === 'age' && (
                                <p>
                                    {strings.age} {item.key + ' ' + item.value}
                                </p>
                            )}
                            {item.type === 'upcoming_birthday' && (
                                <p>
                                    {strings.birthday} {strings.in_next + ' ' + item.value + ' ' + item.key}
                                </p>
                            )}
                            {item.type === 'booking' && (
                                <p>
                                    {strings.target_clients_who_last_booked} {servicesName} {strings.service.toLowerCase()} {strings.before}{' '}
                                    {selectedServices.date} {item.key}
                                </p>
                            )}
                        </div>
                    ))}
                    {selectedMarketing?.client_filter?.length &&
                        selectedMarketing?.client_filter?.length === 1 &&
                        strings.target_every_client_in_the_database}
                </div>
                <ViewModalTextItem title={strings.created_at} value={renderDateTime(selectedMarketing?.created_at)} showIfEmpty />
            </div>
        </Modal>
    );
};
export default SMSMarketingView;
