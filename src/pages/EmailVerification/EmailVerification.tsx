import ModalSuspense from "@partials/Loadings/ModalLoading";
import EmailVerificationModal from "./EmailVerificationModal";
import React, { useEffect } from "react";
import useAuth from "@hooks/useAuth";
import { Navigate } from "react-router";

const EmailVerification = () => {
    const {user} = useAuth();
    const [verificationModal, setVerificationModal] = React.useState(false);
    const isCompanyVerify = user?.company?.verification === 'verified';
    useEffect(() => {
        if(isCompanyVerify)return;
        setVerificationModal(true)
    }, [isCompanyVerify])
    return (
        <>
        {isCompanyVerify && <Navigate to={'/'} />}
            <ModalSuspense>
                {<EmailVerificationModal open={verificationModal} setOpen={setVerificationModal} />}
            </ModalSuspense>
        </>
    )

}

export default EmailVerification;