import Button from '@components/form/Button';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';
import { useNavigate } from 'react-router';
import image from '@images/media/buy_credit.svg';

export interface NotEnoughSMSCreditsModalProps {
    openModal: boolean;
    onClose: () => void;
}

const NotEnoughSMSCreditsModal: React.FC<NotEnoughSMSCreditsModalProps> = ({ openModal, onClose }) => {
    const navigate = useNavigate();

    return (
        <Modal open={openModal} handleClose={onClose} size={'small'} title={strings.buy_credits}>
            <div className="space-y-6 p-6 text-center">
                <img src={image} alt="no credits" className="mx-auto h-32" />
                <p className="whitespace-pre-wrap">{strings.not_enough_credits_for_video_call}</p>
                <Button onClick={() => navigate('/settings/credits')} size="small" className="mx-auto">
                    {strings.buy_credits}
                </Button>
            </div>
        </Modal>
    );
};

export default NotEnoughSMSCreditsModal;
