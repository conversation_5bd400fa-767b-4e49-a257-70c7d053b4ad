import IconButton from '@components/form/IconButton';
import { MaintenanceDocument } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import * as React from 'react';

export interface MaintenanceListItemProps {
    onViewClick?: (data: MaintenanceDocument) => void;
    maintenanceDoc: MaintenanceDocument;
}

const MaintenanceListItem: React.FC<MaintenanceListItemProps> = ({ maintenanceDoc, onViewClick = () => {} }) => {
    const viewClick = () => onViewClick(maintenanceDoc);
    const { renderDate } = useHours();

    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className="alternate-tr-desktop hidden md:table-row">{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <p className="line-clamp-2 max-w-3xl overflow-ellipsis break-all">{maintenanceDoc.title}</p>
                </Table.Td>
                <Table.Td>{renderDate(maintenanceDoc.created_at)}</Table.Td>
                <Table.Td className="p-2">
                    <div className="flex justify-end space-x-1.5">
                        <IconButton onClick={viewClick} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }
    function mobileListItem() {
        return (
            <>
                <Table.Td>
                    <div className="flex justify-between gap-4">
                    <p className="line-clamp-2 max-w-64 overflow-ellipsis break-all">{maintenanceDoc.title}</p>
                        <p>{renderDate(maintenanceDoc.created_at)}</p>
                    </div>
                    <div className="-ml-2">
                        <IconButton onClick={viewClick}>
                            <EyeIcon />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }
};

export default MaintenanceListItem;
