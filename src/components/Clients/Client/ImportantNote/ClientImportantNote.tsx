import Card from '@components/card';
import IconButton from '@components/form/IconButton';
import strings from '@lang/Lang';
import DeleteIcon from '@partials/Icons/Delete';
import EditIcon from '@partials/Icons/Edit';
import EyeIcon from '@partials/Icons/Eye';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import React, { lazy } from 'react';
import useClient from '@hooks/useClient';

export interface ClientImportantNotesProps {
    notes: string;
}

const ClientImportantNotesModal = lazy(() => import('./ClientImportantNoteModal'));
const ClientImportantNoteDeleteModal = lazy(() => import('./ClientImportantNoteDeleteModal'));
const ClientImportantNoteViewModal = lazy(() => import('./ClientImportantNoteViewModal'));
const CLientImportantNotes: React.FC<ClientImportantNotesProps> = ({ notes }) => {
    const [openModal, setOpenModal] = React.useState(false);
    const [openViewModal, setOpenViewModal] = React.useState(false);
    const [openDeleteModal, setOpenDeleteModal] = React.useState(false);
    const { mutate, client } = useClient();

    return (
        <>
            <ModalSuspense>
                {openModal && notes && (
                    <ClientImportantNotesModal openModal={openModal} mutate={mutate} setOpenModal={setOpenModal} selectedClient={client} />
                )}
                {openDeleteModal && notes && (
                    <ClientImportantNoteDeleteModal
                        open={openDeleteModal}
                        mutate={mutate}
                        setOpenModal={setOpenDeleteModal}
                        clientId={client?.id.toString() || ''}
                    />
                )}
                {openViewModal && notes && (
                    <ClientImportantNoteViewModal openModal={openViewModal} setOpenModal={setOpenViewModal} note={client?.important_note} />
                )}
            </ModalSuspense>
            <Card className="relative">
                <div className="">
                    <div className="flex items-center justify-between">
                        <p className="text-lg font-semibold text-warning"> {strings.important_note}</p>
                        <div className="space-x-1">
                            <IconButton name={strings.View} onClick={() => setOpenViewModal(true)} icon={<EyeIcon />} />
                            <IconButton name={strings.edit} onClick={() => setOpenModal(true)} icon={<EditIcon />} />
                            <IconButton name={strings.Delete} onClick={() => setOpenDeleteModal(true)} icon={<DeleteIcon />} />
                        </div>
                    </div>
                    <p className="line-clamp-4">{notes}</p>
                </div>
            </Card>
        </>
    );
};
export default CLientImportantNotes;
