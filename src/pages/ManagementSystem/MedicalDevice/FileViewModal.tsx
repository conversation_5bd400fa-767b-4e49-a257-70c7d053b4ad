import Button from '@components/form/Button';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';
import { useState } from 'react';
import { getUrlExtension } from '@/helper';

export const imageFormats = ['jpg', 'jpeg', 'jfif', 'pjpeg', 'pjp', 'png', 'webp', 'svg'];
export const videoFormats = ['mp4', 'ogg', 'webm'];

export interface FileViewModalProps {
    openModal: boolean;
    onClose: () => void;
    file: string;
    file_name: string;
}

export function viewableFile(ext: string) {
    return [...imageFormats, 'pdf', ...videoFormats].includes(ext.toLowerCase());
}
export function isImage(ext: string) {
    return imageFormats.includes(ext.toLowerCase());
}
export function isVideo(ext: string) {
    return videoFormats.includes(ext.toLowerCase());
}

async function download(fileName: string, url: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
        });

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);
        const ext = getUrlExtension(fileName) ? '' : getUrlExtension(url);
        a.setAttribute('download', `${fileName}${ext ? '.' + ext : ''}`);
        a.click();
    } catch (error) {
        console.error(error);
    }
}

const MedicalDeviceFileViewModal: React.FC<FileViewModalProps> = ({ openModal, file: file_url, onClose, file_name }) => {
    const ext = getUrlExtension(file_url) ?? '';
    const isViewable = viewableFile(ext);
    const isImg = isImage(ext);
    const isVid = isVideo(ext);
    const isPDF = ext === 'pdf';

    const [downloading, setDownloading] = useState(false);

    async function onDownloadClick() {
        setDownloading(true);
        await download(`${file_name}.${ext}`, file_url);
        setDownloading(false);
    }

    return (
        <Modal open={openModal} handleClose={onClose} title={file_name} size="large">
            <div className="overflow-hidden rounded-b-2xl">
                <div className="h-[85vh] w-full">
                    {!isViewable && (
                        <div className="flex h-full w-full select-none flex-col items-center justify-center pb-4">
                            <p className="mb-4">{strings.not_supported}</p>
                            <Button size="small" rounded loading={downloading} onClick={onDownloadClick}>
                                {strings.download}
                            </Button>
                        </div>
                    )}
                    {isViewable && isVid && (
                        <div className="h-full w-full pb-4">
                            <video controls className="h-full w-full bg-gray-100 object-contain dark:bg-dimGray">
                                <source src={file_url} />
                            </video>
                        </div>
                    )}
                    {isViewable && isImg && (
                        <div className="h-full w-full pb-4">
                            <img className="h-full w-full bg-gray-100 object-contain dark:bg-dimGray" src={file_url} alt={file_name} />
                        </div>
                    )}
                    {isViewable && isPDF && <iframe title="PDF viewer" className="h-full w-full" src={file_url}></iframe>}
                </div>
            </div>
        </Modal>
    );
};

export default MedicalDeviceFileViewModal;
