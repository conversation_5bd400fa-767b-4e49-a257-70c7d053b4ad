import Button from '@components/form/Button';
import TextArea from '@components/form/TextArea';
import api from '@configs/api';
import { Client } from '@interface/model/client';
import strings from '@lang/Lang';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import { Formik } from 'formik';
import * as React from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

export interface ClientImportantNotesModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    mutate: () => Promise<any>;
    selectedClient?: Client;
}

export interface IClientProcedureCancelValues {
    details: string;
}

const ClientImportantNotesModal: React.FC<ClientImportantNotesModalProps> = ({ openModal, setOpenModal, mutate, selectedClient }) => {
    const navigate = useNavigate();

    return (
        <Formik<IClientProcedureCancelValues>
            initialValues={{
                details: selectedClient?.important_note || '',
            }}
            enableReinitialize
            validate={() => {}}
            onSubmit={async (values, { resetForm, setSubmitting }) => {
                const formData = new FormData();
                formData.append('important_note', values.details);
                const response = await fetch(api.clientUpdate.replace(':id', selectedClient?.id.toString() || ''), {
                    method: 'POST',
                    headers: {
                        Accept: 'application/json',
                        
                        
                        'X-App-Locale': strings.getLanguage(),
                    },
                    credentials: 'include',
                    body: formData,
                });

                const data = await response.json();

                if (response.status === 401) {
                    navigate('/');
                }

                if (data.status === '1') {
                    await mutate();
                    resetForm();
                    toast.success(data.message);
                } else {
                    toast.error(data.message || 'server error, please contact admin.');
                }
                setSubmitting(false);
                setOpenModal(false);
            }}
        >
            {({ errors, touched, setFieldValue, values, setFieldTouched, dirty, handleSubmit, isSubmitting, isValidating, resetForm }) => {
                const handleModelClose = async () => {
                    if (isSubmitting || isValidating) return;
                    setOpenModal(false);
                    await resetForm();
                };

                return (
                    <Modal
                        open={openModal}
                        title={(selectedClient?.important_note ? strings.Update : strings.add )+ ' ' + strings.important_note}
                        handleClose={handleModelClose}
                        cancelButton={<CancelButton disabled={isSubmitting} onClick={handleModelClose} children={strings.Cancel} />}
                        submitButton={
                            <Button
                                loading={isSubmitting || isValidating}
                                type='submit'
                                onClick={() => {
                                    if (!dirty) {
                                        toast.success(strings.no_data_changed);
                                        handleModelClose();
                                        return;
                                    }
                                    handleSubmit();
                                }}
                            >
                                {strings.Submit}
                            </Button>
                        }
                    >
                        <FormikErrorFocus />
                        <div className="p-4">
                            <TextArea
                                label={strings.Details}
                                name="details"
                                rows={6}
                                value={values.details}
                                placeholder={strings.enter_details}
                                onChange={(e) => {
                                    setFieldTouched('details');
                                    setFieldValue('details', e.target.value);
                                }}
                                error={touched.details && errors.details}
                            />
                        </div>
                    </Modal>
                );
            }}
        </Formik>
    );
};

export default ClientImportantNotesModal;
