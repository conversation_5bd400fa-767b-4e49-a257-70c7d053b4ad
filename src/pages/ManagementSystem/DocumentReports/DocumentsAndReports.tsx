import Card from '@components/card';
import AddButton from '@components/form/AddButton';
import Button from '@components/form/Button';
import Pagination from '@components/form/Pagination';
import Select from '@components/form/Select';
import Heading from '@components/heading/Heading';
import Skeleton from '@components/Skeleton/Skeleton';
import api from '@configs/api';
import { FilterType } from '@hooks/useCursorPaginationSWR';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import ExportIcon from '@icons/Export';
import { UserDocumentsPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import Table from '@partials/Table/PageTable';
import { lazy, useState } from 'react';
import { toast } from 'react-toastify';
import DocumentReportsDeleteModal from './DocumentReportsDeleteModal';
import DocumentsAndReportsListItem from './DocumentsAndReportsListItem';
import { type ZipDownloadData } from './ZipDownloadPopup';

const ZipDownloadPopup = lazy(() => import('./ZipDownloadPopup'));
const DocumentReportsModal = lazy(() => import('./DocumentReportsModal'));
const DocumentViewModal = lazy(() => import('./DocumentViewModal'));

export interface DocumentsAndReportsProps {}

const DocumentsAndReports: React.FC<DocumentsAndReportsProps> = ({}) => {
    const { data, mutate, page, setPage, loading, filterData, filterType, filter, filterValue, orderBy, handleOrder, orderDirection } =
        usePaginationSWR<UserDocumentsPaginatedResponse, Error>(api.documents.list, {
            filter: 'deleted_at',
            filterType: '=',
            filterValue: null,
        });
    const statusFilter = () => [
        {
            text: strings.All,
            key: 'all',
            filter: 'withTrashed',
            filterType: '=',
            filterValue: null,
        },
        {
            text: strings.Active,
            key: 'active',
            filter: 'deleted_at',
            filterType: '=',
            filterValue: null,
        },
        {
            text: strings.InActive,
            key: 'inactive',
            filter: 'onlyTrashed',
            filterType: '!=',
            filterValue: null,
        },
    ];
    const [openModal, setOpenModal] = useState(false);
    const [openViewModal, setOpenViewModal] = useState(false);
    const [openDeleteModal, setOpenDeleteModal] = useState(false);
    const [selectedDocumentId, setSelectedQuestionary] = useState<number>();
    const [download, setDownload] = useState<ZipDownloadData>();
    const [downloadModalOpen, setDownloadModalOpen] = useState(false);
    const [downloadLoading, setDownloadLoading] = useState(false);

    return (
        <>
            <ModalSuspense>
                {openModal && (
                    <DocumentReportsModal
                        openModal={openModal}
                        onClose={() => {
                            setOpenModal(false);
                            setSelectedQuestionary(undefined);
                        }}
                        mutate={mutate}
                        selectedDocumentId={selectedDocumentId}
                    />
                )}
                {selectedDocumentId && openViewModal && (
                    <DocumentViewModal
                        openModal={openViewModal}
                        onClose={() => {
                            setOpenViewModal(false);
                            setSelectedQuestionary(undefined);
                        }}
                        selectedDocumentId={selectedDocumentId}
                    />
                )}
                {selectedDocumentId && openDeleteModal && (
                    <DocumentReportsDeleteModal
                        open={openDeleteModal}
                        mutate={mutate}
                        handleClose={() => {
                            setOpenDeleteModal(false);
                            setSelectedQuestionary(undefined);
                        }}
                        selectedDocument={data?.data.find((document) => document.id === selectedDocumentId)}
                    />
                )}
                {downloadModalOpen && (
                    <ZipDownloadPopup openModal={downloadModalOpen} setOpenModal={setDownloadModalOpen} data={download} refetch={onDownloadClick} />
                )}
            </ModalSuspense>
            <div className="">
                <Card className="space-y-4">
                    <div className="flex flex-col items-start justify-between gap-2 lg:flex-row lg:items-center">
                        <Heading text={strings.document_reports} variant="subTitle" />
                        <div className="flex items-center space-x-4">
                            <Button size="normal" onClick={() => onDownloadClick()} loading={downloadLoading}>
                                <ExportIcon className="mr-2" />
                                <p>{strings.export}</p>
                            </Button>
                            <div className="min-w-24">
                                <Select
                                    value={
                                        statusFilter().find(
                                            (oFilter) =>
                                                oFilter.filter === filter && oFilter.filterType === filterType && oFilter.filterValue === filterValue,
                                        )?.key
                                    }
                                    onChange={(val) => {
                                        const selectedFilter = statusFilter().find((oFilter) => oFilter.key === val);
                                        if (selectedFilter) {
                                            filterData(selectedFilter.filter, selectedFilter.filterType as FilterType, selectedFilter.filterValue);
                                        }
                                    }}
                                    displayValue={(val) => statusFilter().find((oFilter) => oFilter.key === val)?.text}
                                >
                                    {statusFilter().map((filter) => (
                                        <Select.Option value={filter.key} key={`status_filter_${filter.key}`}>
                                            {filter.text}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </div>
                            <AddButton size="normal" onClick={() => setOpenModal(true)} />
                        </div>
                    </div>
                    <Table>
                        <Table.Head>
                            <Table.ThSort sort={orderBy === 'title' && orderDirection} onClick={() => handleOrder('title')}>
                                {strings.TITLE}
                            </Table.ThSort>
                            <Table.ThSort sort={orderBy === 'created_at' && orderDirection} onClick={() => handleOrder('created_at')}>
                                {strings.added_on}
                            </Table.ThSort>
                            <Table.ThSort sort={orderBy === 'process' && orderDirection} onClick={() => handleOrder('process')}>
                                {strings.process}
                            </Table.ThSort>
                            <Table.Th>{strings.versions}</Table.Th>
                            <Table.Th>{strings.Signed_by}</Table.Th>
                            <Table.Th children={strings.Actions} className="text-right" />
                        </Table.Head>
                        <Table.Body>
                            {!loading && !data?.data.length && <EmptyData cardHeight="!h-[60vh]" />}
                            {data?.data.map((questionnaire) => (
                                <DocumentsAndReportsListItem
                                    document={questionnaire}
                                    key={questionnaire.id}
                                    onEdit={(document) => {
                                        setSelectedQuestionary(document.id);
                                        setOpenModal(true);
                                    }}
                                    onView={(document) => {
                                        setSelectedQuestionary(document.id);
                                        setOpenViewModal(true);
                                    }}
                                    onDelete={(document) => {
                                        setSelectedQuestionary(document.id);
                                        setOpenDeleteModal(true);
                                    }}
                                />
                            ))}
                            {loading && <QuestionnairesSkeleton limit={10} />}
                        </Table.Body>
                    </Table>
                    <Pagination
                        pageSize={data?.per_page ?? 0}
                        totalCount={data?.total ?? 0}
                        currentPage={page}
                        onPageChange={(page) => setPage(page)}
                    />
                </Card>
            </div>
        </>
    );

    async function onDownloadClick(force: boolean = false) {
        setDownloadLoading(true);
        const response = await fetch(api.documents.download(force), {
            method: 'POST',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                'X-App-Locale': strings.getLanguage(),
            },
            credentials: 'include',
        });
        const json = await response.json();
        setDownloadLoading(false);

        // If ZIP file exists in s3
        if (json?.data) {
            setDownload({ id: -1, message: json.message, file: json.data });
            setDownloadModalOpen(true);
            return;
        }
        // ZIP generating or in queue
        if (response.status === 208) {
            setDownload({ id: -1, message: json.message });
            setDownloadModalOpen(true);
            return;
        }
        // ZIP request added in queue
        if (response.status === 200) {
            setDownload({ id: -1, message: json.message });
            setDownloadModalOpen(true);
            return;
        } else {
            toast.error(json.message || 'server error, please contact admin.');
        }
        setDownload(undefined);
    }
};

function QuestionnairesSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}

export default DocumentsAndReports;
