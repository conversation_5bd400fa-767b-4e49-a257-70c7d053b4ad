import useTheme from '@hooks/useTheme';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, Tooltip, <PERSON>A<PERSON>s, YAxis } from 'recharts';
import { chartColors } from './PieChartSingleColor';

export interface PieChartProps {
    data: { [key in string]: any }[];
    dataLabel?: [ChartDataLabelType, ChartDataLabelType];
    color?: 'primary' | 'orange';
}

type ChartDataLabelType = {
    dataKey: string;
    label: string;
};

const BarMultiChartComponent: React.FC<PieChartProps> = ({ data, dataLabel }) => {
    const { isDark } = useTheme();

    const [l1, l2] = dataLabel ?? [];

    return (
        <ResponsiveContainer width="100%" height={350}>
            <BarChart data={data} barGap={0}>
                <CartesianGrid strokeDasharray="6 4" vertical={false} opacity={isDark ? 0.5 : 1} />
                <XAxis tickLine={false} dataKey="name" />
                <YAxis width={undefined} tickLine={false} />
                <Tooltip
                    cursor={{ fill: isDark ? '#FFFFFF0D' : '#0000000D' }}
                    labelClassName="dark:text-black"
                    wrapperClassName="rounded-xl border-0"
                />
                <Bar dataKey={l1?.dataKey ?? 'value'} name={l1?.label} fill={chartColors.primary} radius={[4, 4, 0, 0]} />
                <Bar dataKey={l2?.dataKey ?? 'value2'} name={l2?.label} fill={chartColors.violet} radius={[4, 4, 0, 0]} />
            </BarChart>
        </ResponsiveContainer>
    );
};

export default BarMultiChartComponent;
