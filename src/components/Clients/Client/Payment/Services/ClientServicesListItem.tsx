import Badge from '@components/form/Badge';
import { ChipVarient } from '@components/form/Chip';
import IconButton from '@components/form/IconButton';
import useAuth from '@hooks/useAuth';
import { ReceiptItem } from '@interface/model/receipt_item';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import * as React from 'react';

export interface ClientServicesListItemProps {
    service: ReceiptItem;
    onViewClick?: () => void;
}

const ClientServicesListItem: React.FC<ClientServicesListItemProps> = ({ service, onViewClick = () => {} }) => {
    const { withCurrency } = useAuth();
    const { renderDateTime } = useHours();

    return (
        <>
            <tr className={`alternate-tr-mobile md:hidden`}>{mobileListItem()}</tr>
            <tr className={`alternate-tr-desktop hidden md:table-row`}>{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>{service?.receipt?.viva_receipt_id ? `#${service?.receipt?.viva_receipt_id}` : ''}</Table.Td>
                <Table.Td>{service?.name}</Table.Td>
                <Table.Td>{renderDateTime(service?.created_at)}</Table.Td>
                <Table.Td>
                    <p>{service?.selling_price ? withCurrency(service?.selling_price) : ''}</p>
                </Table.Td>
                <Table.Td>
                    <p>{service?.quantity}</p>
                </Table.Td>
                <Table.Td>
                    <p>{service?.total ? withCurrency(service?.total) : ''}</p>
                </Table.Td>
                <Table.Td>
                    <div className="flex items-center justify-start gap-2">
                        <Badge
                            varient={
                                cx(
                                    (service?.status || service?.receipt?.status) === 'PAID' && 'success',
                                    ((service?.status || service?.receipt?.status) === 'CANCELLED' ||
                                        (service?.status || service?.receipt?.status) === 'ABORTED') &&
                                        'error',
                                    (service?.status || service?.receipt?.status) === 'PROCESSING' && 'info',
                                    (service?.status === 'REFUNDED' || service?.status === 'PARTIALLY_REFUNDED') && 'warning',
                                    !service?.status &&
                                        (service?.receipt?.status === 'REFUNDED' || service?.receipt?.status === 'PARTIALLY_REFUNDED') &&
                                        'success',
                                ) as ChipVarient
                            }
                        >
                            {pos_strings.getString(
                                `receipt.status.${
                                    service?.status
                                        ? service?.status?.toLowerCase()
                                        : service?.receipt?.status === 'REFUNDED' || service?.receipt?.status === 'PARTIALLY_REFUNDED'
                                          ? 'paid'
                                          : service?.receipt?.status?.toLowerCase()
                                }`,
                            )}
                        </Badge>
                    </div>
                </Table.Td>

                <Table.Td className="p-2">
                    <div className="flex items-center justify-end space-x-1.5">
                        <IconButton onClick={onViewClick} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }

    function mobileListItem() {
        return (
            <Table.Td>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.order_id} :`}</span>
                    <span className="ml-1">{service?.receipt?.viva_receipt_id ? `#${service?.receipt?.viva_receipt_id}` : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.service_name} :`}</span>
                    <span className="ml-1">{service?.name}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.DateTime} :`}</span>
                    <span className="ml-1">{renderDateTime(service?.created_at)}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.price} :`}</span>
                    <span className="ml-1">{service?.selling_price ? `${withCurrency(service?.selling_price ?? 0)}` : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.qty} :`}</span>
                    <span className="ml-1">{service?.quantity}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.total_price} :`}</span>
                    <span className="ml-1">{service?.total ? `${withCurrency(service?.total ?? 0)}` : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.status} :`}</span>
                    <span className="ml-2 flex items-center justify-start gap-2">
                        <Badge
                            varient={
                                cx(
                                    (service?.status || service?.receipt?.status) === 'PAID' && 'success',
                                    ((service?.status || service?.receipt?.status) === 'CANCELLED' ||
                                        (service?.status || service?.receipt?.status) === 'ABORTED') &&
                                        'error',
                                    (service?.status || service?.receipt?.status) === 'PROCESSING' && 'info',
                                    (service?.status === 'REFUNDED' || service?.status === 'PARTIALLY_REFUNDED') && 'warning',
                                    !service?.status &&
                                        (service?.receipt?.status === 'REFUNDED' || service?.receipt?.status === 'PARTIALLY_REFUNDED') &&
                                        'success',
                                ) as ChipVarient
                            }
                        >
                            {pos_strings.getString(
                                `receipt.status.${
                                    service?.status
                                        ? service?.status?.toLowerCase()
                                        : service?.receipt?.status === 'REFUNDED' || service?.receipt?.status === 'PARTIALLY_REFUNDED'
                                          ? 'paid'
                                          : service?.receipt?.status?.toLowerCase()
                                }`,
                            )}
                        </Badge>
                    </span>
                </p>
                <div className="flex w-full items-center justify-between gap-2">
                    <div className="flex items-center gap-2">
                        <IconButton onClick={onViewClick} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                    </div>
                </div>
            </Table.Td>
        );
    }
};

export default ClientServicesListItem;
