import AddButton from '@components/form/AddButton';
import IconButton from '@components/form/IconButton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { ClientFileBatchResponse } from '@interface/common';
import { File } from '@interface/model/File';
import EyeIcon from '@partials/Icons/Eye';
import { useHours } from '@provider/TimeProvider';
import * as React from 'react';
import useSWR from 'swr';
import { generateUserFullName } from '../../../../helper';
import { GeneralNote } from '../../../../interfaces/model/generalNote';
import strings from '../../../../lang/Lang';
import Modal from '../../../../partials/MaterialModal/Modal';
import ViewModalTextItem from '../../../../partials/ViewModal/ViewModalTextItem';
import ClientFileBatchAddPicturesModal from '../Media/ClientFileBatchAddPicturesModal';
import ClientMediaFileBatchView from '../Media/ClientMediaFileBatchView';

const ClientGeneralNoteFileViewModal = React.lazy(() => import('./ClientGeneralNoteFileViewModal'));

export interface ClientGeneralNoteViewModalProps {
    selectedGeneralNote?: GeneralNote;
    openModal?: boolean;
    handleClose?: () => void;
}

export interface FileModel {
    file: File;
    fileName: string;
}
const ClientGeneralNoteViewModal: React.FC<ClientGeneralNoteViewModalProps> = ({
    selectedGeneralNote,
    openModal = false,
    handleClose = () => {},
}) => {
    const { renderDate, renderDateTime } = useHours();
    const { clientId } = useClient();
    const { data, mutate } = useSWR<ClientFileBatchResponse>(api.clientFileBatch.list(clientId, selectedGeneralNote?.id, 'note'));

    const [viewModalOpen, setViewModalOpen] = React.useState(false);
    const [selectedFile, setSelectedFile] = React.useState<FileModel>();
    const [openImageAddModal, setOpenImageAddModal] = React.useState(false);

    return (
        <>
            {openImageAddModal && (
                <ClientFileBatchAddPicturesModal
                    openModal={openImageAddModal}
                    setOpenModal={setOpenImageAddModal}
                    onSuccess={mutate}
                    signable_id={selectedGeneralNote?.id ?? 0}
                    signable_type="note"
                    allTypeFiles
                />
            )}
            <Modal open={openImageAddModal ? false : openModal} title={strings.general_note} handleClose={handleClose} size="large">
                {viewModalOpen && selectedFile && (
                    <ClientGeneralNoteFileViewModal
                        file={selectedFile?.file}
                        fileName={selectedFile.fileName}
                        onClose={() => {
                            setViewModalOpen(false);
                            setSelectedFile(undefined);
                        }}
                        openModal={viewModalOpen}
                    />
                )}
                <div className="p-4">
                    <ViewModalTextItem title={strings.TITLE} value={selectedGeneralNote?.title} />
                    <ViewModalTextItem
                        title={strings.created_at}
                        value={renderDateTime(selectedGeneralNote?.created_at, { utc: true, isShowLocal: true })}
                    />
                    {selectedGeneralNote?.files?.length ? (
                        <div className="mb-4">
                            <p className="uppercase text-primary dark:text-primaryLight">{strings.files}</p>
                            {selectedGeneralNote.files.map((fl, index) => {
                                const name = selectedGeneralNote?.filenames?.length
                                    ? selectedGeneralNote.filenames[index]
                                    : selectedGeneralNote?.filename
                                      ? selectedGeneralNote.filename
                                      : '-';
                                return (
                                    <div key={index} className={`flex items-center py-0.5 dark:border-gray-700 ${index !== 0 && 'border-t'}`}>
                                        <p className="flex-grow break-all" style={{ whiteSpace: 'pre-wrap' }}>
                                            {name}
                                        </p>
                                        <IconButton
                                            onClick={() => {
                                                if (!(selectedGeneralNote?.files?.length && selectedGeneralNote?.files[index])) return;
                                                setSelectedFile({ fileName: name, file: selectedGeneralNote.files[index] });
                                                setViewModalOpen(true);
                                            }}
                                            children={<EyeIcon />}
                                        />
                                    </div>
                                );
                            })}
                        </div>
                    ) : (
                        <ViewModalTextItem title={strings.file} value={selectedGeneralNote?.filename} />
                    )}
                    <ViewModalTextItem
                        title={strings.Signed_at}
                        show={!!selectedGeneralNote?.signed_at}
                        value={renderDateTime(selectedGeneralNote?.signed_at, { utc: true, isShowLocal: true })}
                    />
                    <ViewModalTextItem
                        title={strings.Signed_by}
                        show={!!selectedGeneralNote?.signed_at}
                        value={generateUserFullName(selectedGeneralNote?.signed_by)}
                    />
                    <ViewModalTextItem
                        title={strings.NOTES}
                        value={`${selectedGeneralNote?.notes_html ? (selectedGeneralNote?.notes_html ?? '') : (selectedGeneralNote?.notes ?? '')}`}
                        html={true}
                    />
                    <ViewModalTextItem
                        title={strings.Sign}
                        show={!!selectedGeneralNote?.signed_at}
                        value={`${import.meta.env.REACT_APP_STORAGE_PATH}/${selectedGeneralNote?.sign}`}
                        image
                    />
                    <div className="mb-4">
                        <div className="mb-4 flex items-center justify-between">
                            <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.files}</p>
                            <AddButton
                                onClick={() => {
                                    setOpenImageAddModal(true);
                                }}
                            />
                        </div>
                        {data?.data.map((batch) => <ClientMediaFileBatchView key={batch.id} batch={batch} asFile />)}
                    </div>
                    {selectedGeneralNote?.prescriptions?.length !== 0 && (
                        <div className="mb-4">
                            <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.prescriptions}</p>
                            <ol className="divide-y dark:divide-gray-700">
                                {selectedGeneralNote?.prescriptions?.map((v) => (
                                    <li className="py-1" key={v.id}>
                                        <p className="inline-flex items-center">
                                            <span className="font-medium">{v.title}</span>
                                            {/* <span className="ml-2 text-xs text-gray-700 dark:text-gray-300">
                                                {strings.end_date}: {renderDate(v.expire_at)}
                                            </span> */}
                                        </p>
                                    </li>
                                ))}
                            </ol>
                        </div>
                    )}
                </div>
            </Modal>
        </>
    );
};

export default ClientGeneralNoteViewModal;
