import strings from '@lang/Lang';
import TimePeriod from './TimePeriod';

export interface LOCProps {
    value: string;
    onChange: (value: string) => void;
    error?: string;
}
const LOC: React.FC<LOCProps> = ({ onChange, value, error }) => {
    return (
        <div>
            <div className="flex items-center space-x-2 rounded-lg bg-grayishBlue px-3 py-1.5 dark:bg-darkGray">
                <p>{strings.target_clients_who_last_signed_the_LoC_in_the_last}</p>
                <div className="w-52">
                    <TimePeriod onChange={onChange} value={value} />
                </div>
            </div>
            <p className="text-[10px] text-red-500 first-letter:capitalize">{error}</p>
        </div>
    );
};
export default LOC;
