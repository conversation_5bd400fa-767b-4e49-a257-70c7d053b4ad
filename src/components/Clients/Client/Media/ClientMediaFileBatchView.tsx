import { generateUserFullName } from '@/helper';
import IconButton from '@components/form/IconButton';
import Image from '@components/form/Image';
import api from '@configs/api';
import { ClientFileBatch } from '@interface/model/file_batch';
import strings from '@lang/Lang';
import EyeIcon from '@partials/Icons/Eye';
import { useHours } from '@provider/TimeProvider';
import { lazy, useState } from 'react';
import { FileModel } from '../GeneralNotes/ClientGeneralNoteViewModal';
import ModalSuspense from '@partials/Loadings/ModalLoading';
const ClientGeneralNoteFileViewModal = lazy(() => import('../GeneralNotes/ClientGeneralNoteFileViewModal'));

export interface ClientMediaFileBatchViewProps {
    batch: ClientFileBatch;
    asFile?: boolean;
}

const ClientMediaFileBatchView: React.FC<ClientMediaFileBatchViewProps> = ({ batch, asFile }) => {
    const { renderDateTime } = useHours();
    const [viewModalOpen, setViewModalOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState<FileModel>();

    return (
        <>
            <ModalSuspense>
                {viewModalOpen && selectedFile && (
                    <ClientGeneralNoteFileViewModal
                        file={selectedFile?.file}
                        fileName={selectedFile.fileName}
                        onClose={() => {
                            setViewModalOpen(false);
                            setSelectedFile(undefined);
                        }}
                        openModal={viewModalOpen}
                    />
                )}
            </ModalSuspense>
            <div className="mt-4 space-y-3 border-t pt-4 dark:border-gray-700">
                <p className="inline-flex rounded bg-primary/10 px-3 py-1 text-sm text-dimGray dark:bg-primaryLight/10 dark:text-primaryLight">
                    {strings.formatString(
                        strings.added_by_xx_on_xx,
                        generateUserFullName(batch.signed_by),
                        renderDateTime(batch.created_at, { utc: true, isShowLocal: true }),
                    )}
                </p>
                <div className="">
                    <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.VerifiedSign}</p>
                    <Image
                        src={api.storageUrl(batch?.sign_path)}
                        alt={batch?.sign_path}
                        className="mb-2 rounded border object-contain dark:border-dimGray dark:bg-white"
                    />
                </div>
                <div className="space-y-2">
                    {batch?.description ? (
                        <div>
                            <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.description}</p>
                            <p className="break-words">{batch?.description}</p>
                        </div>
                    ) : (
                        ''
                    )}
                    {batch.files.length > 0 && (
                        <>
                            <p className="text-sm uppercase text-primary dark:text-primaryLight">{asFile ? strings.files : strings.Pictures}</p>
                            <div className="">
                                {asFile &&
                                    batch.files.map((file, index) => {
                                        const name = batch?.filenames?.length ? batch.filenames[index] : file?.filename ?? '-';

                                        return (
                                            <div
                                                key={index}
                                                className={`flex w-full items-center py-0.5 dark:border-gray-700 ${index !== 0 && 'border-t'}`}
                                            >
                                                <p className="flex-grow break-all" style={{ whiteSpace: 'pre-wrap' }}>
                                                    {name}
                                                </p>
                                                <IconButton
                                                    onClick={() => {
                                                        setSelectedFile({ fileName: name, file: batch.files[index] });
                                                        setViewModalOpen(true);
                                                    }}
                                                    children={<EyeIcon />}
                                                />
                                            </div>
                                        );
                                    })}
                                {!asFile &&
                                    batch.files.map((file) => (
                                        <Image
                                            key={file.id}
                                            src={file.url}
                                            alt={file.filename}
                                            className="mb-2 w-full rounded border dark:border-dimGray dark:bg-gray-500"
                                        />
                                    ))}
                            </div>
                        </>
                    )}
                </div>
            </div>
        </>
    );
};

export default ClientMediaFileBatchView;
