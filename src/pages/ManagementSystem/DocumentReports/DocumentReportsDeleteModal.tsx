import Button from '@components/form/Button';
import { UserDocument } from '@interface/model/document';
import PowerIcon from '@partials/Icons/Power';
import * as React from 'react';
import { toast } from 'react-toastify';
import api from '../../../configs/api';
import strings from '../../../lang/Lang';
import DeleteModal from '../../../partials/MaterialModal/DeleteModal';

export interface DocumentReportsDeleteModalProps {
    open?: boolean;
    handleClose?: () => void;
    selectedDocument?: UserDocument;
    mutate: () => Promise<any>;
}

const DocumentReportsDeleteModal: React.FC<DocumentReportsDeleteModalProps> = ({ open = false, handleClose = () => {}, selectedDocument, mutate }) => {
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    
    return (
        <DeleteModal
            open={open}
            handleClose={() => {
                if (isSubmitting) return;
                handleClose();
            }}
            icon={<PowerIcon slash={!selectedDocument?.deleted_at} />}
            text={`${selectedDocument?.deleted_at ? strings.RESTORE_CLIENT_1 : strings.INACTIVATE_CLIENT_1}?`}
            submitButton={
                <Button
                    loading={isSubmitting}
                    onClick={async () => {
                        setIsSubmitting(true);
                        const response = await fetch(
                            selectedDocument?.deleted_at
                                ? api.documents.restore(selectedDocument.id)
                                : api.documents.delete(selectedDocument?.id),
                            {
                                method:'POST',
                                headers: {
                                    Accept: 'application/json',
                                    
                                    'X-App-Locale': strings.getLanguage(),
                                    'Content-Type': 'application/json',
                                },
                                credentials: 'include',
                            },
                        );

                        const data = await response.json();

                        if (data.status === '1') {
                            await mutate();
                            toast.success(data.message);
                            handleClose();
                        } else {
                            toast.error(data.message || 'server error, please contact admin.');
                        }
                        setIsSubmitting(false);
                        handleClose();
                    }}
                >
                    {selectedDocument?.deleted_at ? strings.activate : strings.inactivate}
                </Button>
            }
        />
    );
};

export default DocumentReportsDeleteModal;
