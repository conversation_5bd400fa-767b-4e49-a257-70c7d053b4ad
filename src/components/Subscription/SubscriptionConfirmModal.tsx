import Button from '@components/form/Button';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';

export interface SubscriptionConfirmProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    platform: string[];
}
const SubscriptionConfirmModal: React.FC<SubscriptionConfirmProps> = ({ openModal, platform, setOpenModal }) => {
    const handleClose = () => {
        setOpenModal(false);
    };

    return (
        <Modal open={openModal} size={platform.length > 3 ? 'large' : 'medium'} handleClose={handleClose}>
            <div className="space-y-4 p-8 text-center">
                <div className="flex justify-center text-8xl">
                    <svg width="1em" height="1em" viewBox="0 0 85 85" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M77.9163 42.5002C77.9163 62.0602 62.0598 77.9168 42.4997 77.9168C22.9396 77.9168 7.08301 62.0602 7.08301 42.5002C7.08301 22.9401 22.9396 7.0835 42.4997 7.0835C62.0598 7.0835 77.9163 22.9401 77.9163 42.5002ZM56.7738 31.7677C57.8111 32.8051 57.8111 34.4869 56.7738 35.5242L39.0654 53.2326C38.0281 54.2699 36.3463 54.2699 35.3089 53.2326L28.2256 46.1493C27.1883 45.1119 27.1883 43.4301 28.2256 42.3927C29.2629 41.3554 30.9448 41.3554 31.9821 42.3927L37.1872 47.5978L45.1022 39.6828L53.0173 31.7677C54.0546 30.7304 55.7364 30.7304 56.7738 31.7677Z"
                            fill="#5551CE"
                        />
                    </svg>
                </div>
                <h3 className="text-2xl font-bold">{strings.successful_subscribed}</h3>
                <div className="flex justify-center space-x-4 px-12">
                    {platform.flatMap((item, index) => (
                        <p className="font-medium" key={index}>
                            {item}
                        </p>
                    ))}
                </div>
                <div className="flex justify-center pt-4">
                    <Button size="big" onClick={handleClose}>
                        {strings.Okay}
                    </Button>
                </div>
            </div>
        </Modal>
    );
};
export default SubscriptionConfirmModal;
