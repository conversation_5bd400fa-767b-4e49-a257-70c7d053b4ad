import { getUrlExtension } from '@/helper';
import LoadingIcon from '@icons/Loading';
import DownloadIcon from '@partials/Icons/Download';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import * as React from 'react';
import IconButton from '../form/IconButton';
import { LegalDocument } from './Documents';
import strings from '@lang/Lang';

export interface DocumentListItemProps {
    document: LegalDocument;
    onDownloadClick?: (data: LegalDocument) => Promise<void>;
}

async function download(fileName: string, url: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
        });

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);
        const ext = getUrlExtension(fileName) ? '' : getUrlExtension(url);
        a.setAttribute('download', `${fileName}${ext ? '.' + ext : ''}`);
        a.click();
    } catch (error) {
        console.error(error);
    }
}

const DocumentListItem: React.FC<DocumentListItemProps> = ({ document, onDownloadClick = () => {} }) => {
    const { renderDate } = useHours();
    const [loading, setLoading] = React.useState(false);

    const handleDownloadClick = async () => {
        setLoading(true);
        await download(`${document.title}.pdf`, document.url);
        setLoading(false);
    };

    return (
        <tr className="alternate-tr-desktop">
            <Table.Td>
                <p>{document.title}</p>
            </Table.Td>
            <Table.Td>{renderDate(document.created_at)}</Table.Td>
            <Table.Td className="p-2">
                <div className="flex justify-end space-x-1.5">
                    <IconButton name={strings.download} onClick={handleDownloadClick} children={loading ? <LoadingIcon /> : <DownloadIcon />} />
                </div>
            </Table.Td>
        </tr>
    );
};

export default DocumentListItem;
