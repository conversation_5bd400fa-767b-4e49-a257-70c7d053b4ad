import api from '@configs/api';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from '@headlessui/react';
import { SettingResponse } from '@interface/common';
import strings from '@lang/Lang';
import CrossIcon from '@partials/Icons/Cross';
import useSettings from '@provider/SettingsProvider';
import cx from 'classix';
import dayjs from 'dayjs';
import duration, { DurationUnitType } from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import { TimeSlot } from '../Classic/hooks/useCalendarTimeSlots';

export interface CalendarSlotIntervalSelectProps {
    selected: number;
    onChange: (value: number) => void;
}

const static_time_slots: TimeSlotStatic[] = [
    { id: 0, interval: 15, height: 30 },
    { id: 1, interval: 30, height: 45, splits: [0, 15] },
    { id: 2, interval: 60, height: 60, splits: [0, 15, 30, 45] },
];

export interface TimeSlotStatic {
    id: number;
    interval: number;
    type?: 'custom';
    name?: string;
    height?: number;
    splits?: number[];
}

dayjs.extend(duration);
dayjs.extend(relativeTime);

const CalendarSlotIntervalSelect: React.FC<CalendarSlotIntervalSelectProps> = ({ selected, onChange }) => {
    const [minute, setMinute] = useState('20');
    const [isLoading, setIsLoading] = useState(false);

    const { getSettingValue, saveSetting, mutate, data } = useSettings();

    const value = getSettingValue('CALENDAR_CUSTOM_TIME_SLOTS');

    const CALENDAR_CUSTOM_TIME_SLOTS: TimeSlot[] = useMemo(() => {
        try {
            return value ? JSON.parse(value) : [];
        } catch (error) {
            return [];
        }
    }, [value]);

    async function saveNewSlot() {
        const intMinute = parseInt(minute);
        if (!intMinute) {
            toast.error('Please enter a valid number.');
            return;
        }
        if (static_time_slots.map((v) => v.interval).includes(intMinute) || CALENDAR_CUSTOM_TIME_SLOTS.map((v) => v.interval).includes(intMinute)) {
            toast.error('Time slot already exists.');
            return;
        }
        if (intMinute < 5) {
            toast.error('Minimum 5 minutes required.');
            return;
        }
        if (intMinute > 120) {
            toast.error('Maximum limit is 120 minutes.');
            return;
        }

        const newArr = CALENDAR_CUSTOM_TIME_SLOTS.filter((v) => v.interval !== intMinute);

        const id = new Date().getTime();

        const newSlots: TimeSlotStatic[] = [...newArr, { id, interval: intMinute, type: 'custom' }];

        setIsLoading(true);
        await saveSetting({
            key: 'CALENDAR_CUSTOM_TIME_SLOTS',
            value: JSON.stringify(newSlots),
        });
        setMinute('');
        setIsLoading(false);

        const newData = data?.data.filter((v) => v.key !== 'CALENDAR_CUSTOM_TIME_SLOTS') ?? [];
        mutate({ ...data, data: [...newData, { key: 'CALENDAR_CUSTOM_TIME_SLOTS', value: JSON.stringify(newSlots) }] }, false);
    }

    async function removeSlot(id: number) {
        const newArr = CALENDAR_CUSTOM_TIME_SLOTS.filter((v) => v.id !== id);

        setIsLoading(true);
        await saveSetting({
            key: 'CALENDAR_CUSTOM_TIME_SLOTS',
            value: JSON.stringify(newArr),
        });
        setMinute('');
        setIsLoading(false);

        const newData = data?.data.filter((v) => v.key !== 'CALENDAR_CUSTOM_TIME_SLOTS') ?? [];
        mutate({ ...data, data: [...newData, { key: 'CALENDAR_CUSTOM_TIME_SLOTS', value: JSON.stringify(newArr) }] }, false);
    }

    const timeSlots = useCalendarIntervalTimeSlots();

    const selectedTimeSlot = useMemo(() => {
        return timeSlots.find((slot) => slot.id === selected);
    }, [selected, timeSlots]);

    return (
        <Listbox
            as="div"
            className="relative dark:text-white"
            value={selectedTimeSlot}
            onChange={(v: any) => {
                onChange(v.id);
            }}
        >
            <ListboxButton
                className={cx(
                    'form-select relative block w-full rounded-lg border-lightPurple bg-white py-1.5 text-left placeholder:text-mediumGray hover:border-mediumGray focus:border-primary focus:ring-1 disabled:cursor-not-allowed disabled:text-gray-500 dark:border-gray-700 dark:bg-dimGray dark:text-white dark:placeholder:text-gray-600 dark:hover:border-slate-700 dark:focus:border-primaryLight',
                )}
            >
                {selectedTimeSlot?.name || 'Select'}
            </ListboxButton>
            <ListboxOptions
                className={cx(
                    'z-50 w-56 rounded-lg bg-white text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:border dark:border-gray-600 dark:bg-black sm:text-sm',
                    'transition duration-200 data-[closed]:-translate-y-1 data-[closed]:opacity-0',
                )}
                transition
                anchor={{ to: 'bottom', gap: 4 }}
            >
                <div className="soft-searchbar max-h-60 flex-grow overflow-y-auto">
                    {timeSlots.map((slot, index) => {
                        return (
                            <ListboxOption
                                key={index}
                                value={slot}
                                className={({ focus }) =>
                                    cx(
                                        'relative flex cursor-default select-none pr-3',
                                        focus ? 'bg-purpleGray text-primary dark:bg-dimGray dark:text-white' : 'text-gray-900 dark:text-white',
                                    )
                                }
                            >
                                {({ selected, focus }) => (
                                    <div
                                        className={cx(
                                            'flex w-full justify-between space-x-1 pl-3',
                                            selected ? 'font-semibold text-primary dark:text-primaryLight' : '',
                                        )}
                                    >
                                        <p className="py-2">{slot.name}</p>
                                        {slot.type === 'custom' && focus && !selected && (
                                            <button
                                                className="p-1"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    removeSlot(slot.id);
                                                }}
                                            >
                                                <CrossIcon className="h-4 w-4 text-gray-500 dark:text-white" />
                                            </button>
                                        )}
                                    </div>
                                )}
                            </ListboxOption>
                        );
                    })}
                </div>
                <div className="grid w-full grid-cols-3 divide-x border-t dark:divide-gray-700 dark:border-gray-700">
                    <input
                        className="w-auto rounded-bl-[4px] bg-transparent px-2 py-2 text-center outline-none focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary"
                        disabled={isLoading}
                        value={minute}
                        onChange={(e) => setMinute(e.currentTarget.value)}
                        placeholder="5-120"
                    />
                    <div className="flex items-center justify-center text-center">{strings.minutes}</div>
                    <button disabled={isLoading} className="hover:bg-primary/10 active:bg-primary/20" onClick={saveNewSlot}>
                        {strings.add}
                    </button>
                </div>
            </ListboxOptions>
        </Listbox>
    );
};

export const useCalendarIntervalTimeSlots = (): TimeSlot[] => {
    const { data } = useSWR<SettingResponse, Error>(api.setting);

    const value = useMemo(() => data?.data.find((setting) => setting.key === 'CALENDAR_CUSTOM_TIME_SLOTS')?.value, [data?.data]);

    const CALENDAR_CUSTOM_TIME_SLOTS: TimeSlot[] = useMemo(() => {
        try {
            return value ? JSON.parse(value) : [];
        } catch (error) {
            return [];
        }
    }, [value]);

    const timeSlots = useMemo(() => {
        return [...static_time_slots, ...CALENDAR_CUSTOM_TIME_SLOTS].sort((a, b) => (a.interval > b.interval ? 1 : -1)).map(generateTimeSlot);
    }, [CALENDAR_CUSTOM_TIME_SLOTS]);

    return timeSlots;
};

export function generateTimeSlot(slot: TimeSlotStatic) {
    return {
        ...slot,
        height: slot.height ?? 60,
        name: renderTimeName(slot.interval),
        endtime: dayjs()
            .endOf('day')
            .subtract(slot.interval - 1, 'minute'),
        period: 'minutes' as DurationUnitType,
    };
}

function renderTimeName(minute: number) {
    const dr = dayjs.duration(minute, 'minute');
    const k = [dr.hours(), dr.minutes()];
    if (k[0] === 0) {
        return k[1] + ' ' + strings.minutes;
    }
    if (k[1] === 0) {
        return k[0] + ' ' + strings.hour;
    }
    return k[0] + ' ' + strings.hour + ' ' + k[1] + ' ' + strings.minutes;
}

export default CalendarSlotIntervalSelect;
