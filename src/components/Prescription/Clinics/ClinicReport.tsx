import api from '@configs/api';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { ClinicReportResponse } from '@interface/common';
import strings from '@lang/Lang';
import Table from '@partials/Table/PageTable';
import * as React from 'react';
import PrescriptionClinicReportListItem from './ClinicReportListItem';
import { useParams } from 'react-router';
import Skeleton from '@components/Skeleton/Skeleton';
import ViewAllButton from '@components/form/ViewAllButton';
import EmptyData from '@partials/Error/EmptyData';

export interface PrescriptionClinicReportProps {}

const PrescriptionClinicReport: React.FC<PrescriptionClinicReportProps> = () => {
    const { clinicId }: { clinicId?: string } = useParams();

    const { data, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<ClinicReportResponse, Error>(api.clinicReportList(clinicId), {
        limit: 8,
    });

    return (
        <div>
            <Table>
                <Table.Head>
                    <Table.ThSort
                        sort={orderBy === 'start_date' && orderDirection}
                        onClick={() => handleOrder('start_date')}
                        children={strings.Monthly}
                    />
                    <Table.ThSort
                        sort={orderBy === 'signed_prescription_count' && orderDirection}
                        onClick={() => handleOrder('signed_prescription_count')}
                        children={strings.signed_prescription}
                    />
                    <Table.Th children={strings.amount} />
                    <Table.Th children={strings.status} />
                    <Table.Th children={strings.Actions} className="text-right" />
                </Table.Head>
                <Table.Body>
                    {!loading &&
                        (data?.data.length ? (
                            data?.data?.map((clinic, index) => <PrescriptionClinicReportListItem key={index} clinic={clinic} />)
                        ) : (
                            <></>
                        ))}
                    {loading && <ClinicReportSkeleton limit={8} />}
                    {!loading && data?.data.length !== 0 && (
                        <tr>
                            <td colSpan={5}>
                                <ViewAllButton to={`/clinics/${clinicId}/report`} />
                            </td>
                        </tr>
                    )}
                    {!loading && !data?.data.length && <EmptyData cardHeight="!h-[40vh]" />}
                </Table.Body>
            </Table>
        </div>
    );
};
function ClinicReportSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={index}>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>

                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>

                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default PrescriptionClinicReport;
