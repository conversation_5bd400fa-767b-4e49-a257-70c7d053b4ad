import { timeZone } from '@/helper';
import IconButton from '@components/form/IconButton';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import { MeridiqBilling } from '@interface/model/MeridiqBilling';
import DownloadIcon from '@partials/Icons/Download';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import React, { FC, ReactNode } from 'react';
import strings from '../../../lang/Lang';

export interface MeridiqBillingListItemProps {
    meridiqBillData: MeridiqBilling;
}

const MeridiqBillingListItem: FC<MeridiqBillingListItemProps> = ({ meridiqBillData }) => {
    const { renderDate } = useHours();
    const [loading, setLoading] = React.useState<boolean>(false);

    let statusHtml: ReactNode | undefined;
    switch (meridiqBillData.payment_status) {
        case 'UPCOMING':
            statusHtml = <span className="text-sm uppercase text-gray-500 dark:text-gray-400">{strings.InvoiceStatusUpcoming}</span>;
            break;
        case 'PAID':
            statusHtml = <span className="text-sm uppercase text-primary dark:text-primaryLight">{strings.InvoiceStatusPaid}</span>;
            break;
        case 'UNPAID':
            statusHtml = <span className="text-sm uppercase text-primary dark:text-primaryLight">{strings.unpaid}</span>;
            break;
    }
    async function downloadReceipt(id: number) {
        const response = await fetch(api.meridiqBillingReceipt(id), {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'X-App-Locale': strings.getLanguage(),
                'X-Time-Zone': timeZone(),
            },
            credentials: 'include',
        });

        const data = await response.blob();
        const url = window.URL.createObjectURL(data);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Invoice.pdf`;
        document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
        a.click();
        a.remove();
        setLoading(false);
    }

    const isPaid = meridiqBillData.payment_status === 'PAID';
    const earning = Math.trunc(parseInt(meridiqBillData.earning || ''));
    const vat = Math.trunc(parseInt(meridiqBillData.invoice_vat || ''));

    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className="alternate-tr-desktop hidden md:table-row">{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <p className="whitespace-nowrap">{renderDate(meridiqBillData.end_date, false, 'MMMM DD, YYYY')}</p>
                </Table.Td>
                <Table.Td>
                    <p className="whitespace-nowrap">
                        {earning} {Number.isInteger(vat) ? `(VAT: ${vat})` : ''}
                    </p>
                </Table.Td>
                <Table.Td>
                    <p>{statusHtml}</p>
                </Table.Td>
                <Table.Td>
                    {isPaid ? (
                        <IconButton
                            disabled={loading}
                            onClick={() => {
                                setLoading(true);
                                downloadReceipt(meridiqBillData.id);
                            }}
                        >
                            {loading ? <LoadingIcon /> : <DownloadIcon />}
                        </IconButton>
                    ) : (
                        <>-</>
                    )}
                </Table.Td>
            </>
        );
    }

    function mobileListItem() {
        return (
            <>
                <Table.Td>
                    <div className="flex items-center justify-between space-x-3">
                        <div className="space-y-1">
                            <p className="whitespace-nowrap">{renderDate(meridiqBillData.end_date, false, 'MMMM DD, YYYY')}</p>
                            <p className="whitespace-nowrap">
                                {earning} {Number.isInteger(vat) ? `(VAT: ${vat})` : ''}
                            </p>
                            <p>{statusHtml}</p>
                        </div>
                        {isPaid ? (
                            <IconButton
                                disabled={loading}
                                onClick={() => {
                                    setLoading(true);
                                    downloadReceipt(meridiqBillData.id);
                                }}
                            >
                                {loading ? <LoadingIcon /> : <DownloadIcon />}
                            </IconButton>
                        ) : (
                            <></>
                        )}
                    </div>
                </Table.Td>
            </>
        );
    }
};

export default MeridiqBillingListItem;
