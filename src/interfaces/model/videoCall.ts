export interface Videocall {
  id: number;
  company_id: number;
  callable_type: string;
  callable_id: number;
  privacy: string;
  not_before?: any;
  expires: string;
  eject_at_room_exp: boolean;
  max_participants: number;
  started_at?: any;
  ended_at?: any;
  credit_used: string;
  key: string;
  created_at: string;
  updated_at: string;
  members: Member[];
  owner_member?: Member;
}

export interface Member {
  id: number;
  video_call_id: number;
  memberable_type: string;
  memberable_id: number;
  not_before?: any;
  expires: string;
  eject_at_room_exp: boolean;
  is_owner: boolean;
  user_name: string;
  user_id: string;
  token: string;
  key: string;
  started_at?: any;
  ended_at?: any;
  notified_at?: any;
  notify_before_mins: number;
  created_at: string;
  updated_at: string;
  url: string;
}