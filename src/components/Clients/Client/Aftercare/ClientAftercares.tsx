import Pagination from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import React, { lazy } from 'react';
import { useParams } from 'react-router';
import api from '../../../../configs/api';
import { usePaginationSWR } from '../../../../hooks/usePaginationSWR';
import { ClientLogsPaginatedResponse } from '../../../../interfaces/common';
import strings from '../../../../lang/Lang';
import Card from '../../../../partials/Paper/PagePaper';
import Table from '../../../../partials/Table/PageTable';
import BackToClientDashboard from '../sections/BackToClientDashboard';
import ClientAftercareListItem from './ClientAftercareListItem';
import Skeleton from '@components/Skeleton/Skeleton';
import Input from '@components/form/Input';
import SearchIcon from '@icons/Search';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { Videocall } from '@interface/model/videoCall';
import EmptyData from '@partials/Error/EmptyData';

const VideoCallDeleteModal = lazy(() => import('./VideoCallCancelModal'));

export interface ClientLogsProps {}

const ClientAftercares: React.FC<ClientLogsProps> = () => {
    const { clientId }: { clientId?: string } = useParams();

    const [isDeleteOpen, setIsDeleteOpen] = React.useState(false);
    const [videoCall, setVideoCall] = React.useState<Videocall>();

    const { data, page, mutate, setPage, orderBy, orderDirection, handleOrder, loading, search, setSearch } = usePaginationSWR<
        ClientLogsPaginatedResponse,
        Error
    >(api.clientAftercares(clientId));

    return (
        <>
            <BackToClientDashboard />
            <ModalSuspense>
                {isDeleteOpen && videoCall && (
                    <VideoCallDeleteModal handleClose={() => setIsDeleteOpen(false)} mutate={mutate} video_call={videoCall} open={isDeleteOpen} />
                )}
            </ModalSuspense>
            <Card className="space-y-4">
                <div className="flex flex-wrap items-center justify-between space-y-2 md:space-y-0">
                    <Heading text={strings.AfterCare} variant="bigTitle" />
                    <Input
                        placeholder={strings.Search}
                        value={search || ''}
                        icon={<SearchIcon className="text-mediumGray" />}
                        onChange={(event) => setSearch(event.target.value)}
                    />
                </div>
                <Table>
                    <Table.Head>
                        <Table.Th>{strings.Details}</Table.Th>
                        <Table.ThSort
                            sort={orderBy === 'created_at' && orderDirection}
                            onClick={() => handleOrder('created_at')}
                            children={strings.DateTime}
                            className="text-right"
                        />
                        <Table.Th>{strings.Actions}</Table.Th>
                    </Table.Head>
                    <Table.Body>
                        {data?.data.map((log) => (
                            <ClientAftercareListItem
                                key={log.id}
                                log={log}
                                onDelete={(v) => {
                                    setVideoCall(v);
                                    setIsDeleteOpen(true);
                                }}
                            />
                        ))}
                        {loading && <ClientAftercareSkeleton limit={10} />}
                        {!loading && !data?.data.length && <EmptyData cardHeight="!h-[61vh]" />}
                    </Table.Body>
                </Table>
                <Pagination pageSize={data?.per_page ?? 0} totalCount={data?.total ?? 0} currentPage={page} onPageChange={(page) => setPage(page)} />
            </Card>
        </>
    );
};
function ClientAftercareSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                        </td>
                        <td colSpan={2} className="p-2">
                            <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default ClientAftercares;
