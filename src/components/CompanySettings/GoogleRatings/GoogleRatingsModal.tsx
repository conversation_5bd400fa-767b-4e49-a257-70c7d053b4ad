import { renderAddress } from '@/helper';
import Avatar from '@components/avatar/Avatar';
import Autocomplete from '@components/form/Autocomplete';
import Button from '@components/form/Button';
import StarRating from '@components/form/StarRating';
import api from '@configs/api';
import useDebounce from '@hooks/useDebounce';
import { Company } from '@interface/model/company';
import GoogleRatingsDetails, { PlaceDetails, PlaceNameaddress, RatingDetails, ReviewsDetails } from '@interface/model/googleRating';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import MapPinIcon from '@partials/Icons/MapPin';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

export interface GoogleRatingsModalProps {
    openModal: boolean;
    handleModalClose: () => void;
    googleRatingsDetails?: GoogleRatingsDetails;
    company?: Company;
    mutate: () => Promise<any>;
}

const GoogleRatingsModal: React.FC<GoogleRatingsModalProps> = ({ openModal, handleModalClose, googleRatingsDetails, company, mutate }) => {
    const [ratingsDetails, setRatingsDetails] = useState<RatingDetails | undefined>(undefined);
    const [placeDetails, setPlaceDetails] = useState<PlaceDetails | undefined>(undefined);
    const [search, setSearch] = useState('');
    const [loading, setLoading] = useState(false);
    const debouncedSearch = useDebounce(search, 360);

    useEffect(() => {
        if (search && debouncedSearch) {
            googlePlacesList();
        } else {
            setRatingsDetails(undefined);
        }
    }, [debouncedSearch]);
    useEffect(() => {
        if (googleRatingsDetails) {
            setPlaceDetails({
                ...placeDetails,
                rating: parseFloat(googleRatingsDetails?.rating ?? '0'),
                name: company?.company_name,
                address: renderAddress(company),
                ratingCount: parseFloat(googleRatingsDetails?.user_rating_count || ''),
                placeUri: googleRatingsDetails?.google_maps_links?.placeUri,
                reviewsUri: googleRatingsDetails?.google_maps_links?.reviewsUri,
                reviews: googleRatingsDetails?.reviews,
            });
        }
    }, [googleRatingsDetails]);
    useEffect(() => {
        if (ratingsDetails?.sessionToken && ratingsDetails?.placeId) {
            googlePlacesDetails();
        }
    }, [ratingsDetails]);

    const googlePlacesList = async () => {
        const formData = new FormData();
        formData.set('input', search);
        if (ratingsDetails?.sessionToken) {
            formData.set('sessionToken', ratingsDetails?.sessionToken || '');
        }
        const response = await fetch(api.googlePlaces.list, {
            method: 'POST',
            headers: {
                Accept: 'application/json',

                'X-App-Locale': strings.getLanguage(),
            },
            credentials: 'include',
            body: formData,
        });

        const data = await response.json();
        if (data.status === '1') {
            setRatingsDetails({
                placeId: '',
                sessionToken: data.data.sessionToken,
                suggestions: data?.data?.suggestions?.map((val: any) => {
                    return {
                        name: val?.placePrediction?.text.text,
                        placeId: val?.placePrediction.placeId,
                    };
                }),
            });
        } else {
            toast.error(data.message || 'server error, please contact admin.');
        }
    };
    const googlePlacesDetails = async (text?: string) => {
        const formData = new FormData();
        formData.set('place_id', placeDetails?.placeId || ratingsDetails?.placeId || '');
        if (text) {
            setLoading(true);
            formData.set('save', text || '');
        } else {
            formData.set('sessionToken', ratingsDetails?.sessionToken || '');
        }
        const response = await fetch(api.googlePlaces.store, {
            method: 'POST',
            headers: {
                Accept: 'application/json',

                'X-App-Locale': strings.getLanguage(),
            },
            credentials: 'include',
            body: formData,
        });

        const data = await response.json();

        if (data.status === '1') {
            if (text) {
                handleModalClose();
                setLoading(false);
                mutate();
            } else {
                setPlaceDetails({
                    ...placeDetails,
                    rating: data?.data?.rating,
                    name: data?.data?.displayName?.text,
                    address: data?.data?.formattedAddress,
                    ratingCount: data?.data?.userRatingCount,
                    placeUri: data?.data?.googleMapsLinks?.placeUri,
                    reviewsUri: data?.data?.googleMapsLinks?.reviewsUri,
                    reviews: data?.data?.reviews,
                });
                setRatingsDetails(undefined);
            }
        } else {
            toast.error(data.message || 'server error, please contact admin.');
        }
    };

    return (
        <Modal
            open={openModal}
            handleClose={handleModalClose}
            title={strings.google_ratings}
            cancelButton={
                <CancelButton
                    onClick={() => {
                        setRatingsDetails(undefined);
                        setPlaceDetails(undefined);
                        handleModalClose();
                    }}
                />
            }
            submitButton={
                <Button onClick={() => googlePlacesDetails('1')} loading={loading} disabled={!placeDetails?.placeId || !placeDetails?.rating}>
                    {strings.Submit}
                </Button>
            }
        >
            <div className="space-y-6 p-5">
                <Autocomplete<PlaceNameaddress>
                    value={undefined}
                    // value={ratingsDetails?.find((place) => place.id === ratingsDetails?.id)}
                    inputProps={{ label: strings.Text, placeholder: strings.Search }}
                    renderOption={(place) => place?.name ?? ''}
                    displayValue={(place) => place?.name ?? ''}
                    filteredValues={(query) => ratingsDetails?.suggestions ?? []}
                    options={search && ratingsDetails?.suggestions ? ratingsDetails?.suggestions : []}
                    onChange={async (place) => {
                        if (!place) return;
                        setRatingsDetails({
                            ...ratingsDetails,
                            placeId: place?.placeId,
                        });
                        setPlaceDetails({
                            ...placeDetails,
                            placeId: place?.placeId,
                            sessionToken: ratingsDetails?.sessionToken,
                        });
                    }}
                    onSearchChange={(value) => {
                        setSearch(value);
                    }}
                />
                {!placeDetails?.name && (
                    <div className="flex justify-center py-2">
                        <table>
                            <tbody>
                                <EmptyData />
                            </tbody>
                        </table>
                    </div>
                )}
                {placeDetails?.name && (
                    <div className="space-y-4">
                        <p className="text-lg font-semibold">{placeDetails?.name ?? ''}</p>
                        <div className="flex items-center gap-2">
                            <p>{(placeDetails?.rating ?? 0).toFixed(1)}</p>
                            <StarRating rating={placeDetails?.rating ?? 0} size="small" />
                            <a className="text-primary dark:text-primaryLight" href={placeDetails?.reviewsUri} target="_blank" rel="noreferrer">
                                ({(placeDetails?.ratingCount ?? 0) + ' ' + strings.reviews.toLowerCase()})
                            </a>
                        </div>
                        <div className="flex gap-2">
                            <div>
                                <MapPinIcon className="mt-1 text-lg" />
                            </div>
                            <p>{placeDetails?.address ?? ''} </p>
                        </div>
                        <div>
                            <a className="text-primary dark:text-primaryLight" href={placeDetails?.placeUri} target="_blank" rel="noreferrer">
                                {strings.View_on_google_maps}
                            </a>
                        </div>
                        <hr className="-mx-5" />
                        <div>
                            <p className="font-medium">{strings.reviews}</p>
                            <div className="space-y-3 divide-y-2 dark:divide-gray-600">
                                {placeDetails?.reviews?.map((review) => {
                                    return (
                                        <div className="flex gap-3 pt-3">
                                            <Avatar className="h-9 w-9" />
                                            <div className="space-y-1">
                                                <p className="font-semibold">{review?.authorAttribution?.displayName}</p>
                                                <div className="flex gap-2">
                                                    <p>{(review?.rating ?? 0).toFixed(1)}</p>
                                                    <StarRating rating={review?.rating || 0} size="small" />
                                                    <span>{review?.relativePublishTimeDescription}</span>
                                                </div>
                                                <p>{review?.originalText?.text}</p>
                                            </div>
                                        </div>
                                    );
                                })}
                                {!placeDetails?.reviews?.length && <p className="py-3 text-mediumGray">{strings.no_data}</p>}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </Modal>
    );
};
export default GoogleRatingsModal;
