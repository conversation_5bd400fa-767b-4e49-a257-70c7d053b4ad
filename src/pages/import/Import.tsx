import Heading from '@components/heading/Heading';
import { FC } from 'react';
import strings from '@lang/Lang';
import ImportHeader from '@components/import/ImportHeader';
import ImportProvider, { useImport } from '@provider/ImportProvider';
import Button from '@components/form/Button';
import InfoCard from '@components/form/InfoCard';
import Select from '@components/form/Select';
import ImportedViewClients from '@components/import/ImportedViewClients';
import ImportedViewBookings from '@components/import/ImportedViewBookings';
import useAuth from '@hooks/useAuth';
import ImportLockText from '@partials/Error/ImportLockText';
import Card from '@components/card';
import useSWR from 'swr';
import { SettingResponse } from '@interface/common';
import api from '@configs/api';

export interface ImportProps {}
export interface ImportFieldValue {
    name: string;
    field: string;
    required?: boolean;
    unique?: boolean;
}

export interface ImportSampleData {
    heading: string[];
    values?: (null | number | string)[];
}
const Import: FC<ImportProps> = () => {
    const { user } = useAuth();
    const { data: settingData } = useSWR<SettingResponse, Error>(api.setting);
    function findValue(key: string) {
        return !!settingData?.data.find((setting) => setting.key === key && setting.value === '1');
    }
    const requiredPersonalID = findValue(api.registrationPortal.requiredPersonalID);
    const requiredEmail = findValue(api.registrationPortal.requiredEmail);
    const requiredCpr = findValue(api.registrationPortal.requiredCpr);
    const requiredPhone = findValue(api.registrationPortal.requiredPhone);

    const clientImportFields: ImportFieldValue[] = [
        { name: strings.Firstname, field: 'first_name', required: true },
        { name: strings.Lastname, field: 'last_name' },
        { name: strings.Email, field: 'email', required: requiredEmail, unique: true },
        { name: strings.PersonalID, field: 'personal_id', required: requiredPersonalID, unique: true },
        { name: strings.PhoneNumber, field: 'phone_number', required: requiredPhone },
        { name: strings.danishId, field: 'danish_id', required: requiredCpr, unique: true },
    ];

    const bookingImportFields: ImportFieldValue[] = [
        { name: strings.interval, field: 'interval', required: true },
        { name: strings.Date, field: 'date', required: true },
        { name: strings.status, field: 'status', required: true },
        { name: strings.service_name, field: 'service_name', required: true },
        { name: strings.service_price, field: 'price', required: true },
        { name: strings.service_duration, field: 'duration', required: true },
        { name: strings.client_first_name, field: 'client_first_name', required: true },
        { name: strings.client_last_name, field: 'client_last_name' },
        { name: strings.client_mobile_number, field: 'client_phone_number' },
        { name: strings.client_email, field: 'client_email', required: true },
        { name: strings.practitioner_name, field: 'practitioner_name' },
        { name: strings.practitioner_email, field: 'practitioner_email' },
        { name: strings.client_personal_id, field: 'client_personal_number' },
        { name: strings.special_request, field: 'special_request' },
    ];
    const { values, setFieldValue, setFieldTouched, isSubmitting, resetForm, submitForm, errors, touched } = useImport();

    const isImporting = values.file && values.type && values.sample_data?.values.length;
    if (user?.company?.verification !== 'verified') {
        return (
            <div>
                <ImportLockText />
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {!isImporting && (
                <div className="grid grid-flow-row grid-cols-1 gap-5">
                    <div>
                        <Heading text={strings.client} />
                        <ImportHeader type="clients" fields={clientImportFields} />
                        {values.success?.data.type === 'clients' && (
                            <>
                                {touched.server && !!errors.server && (
                                    <div className="mt-4">
                                        <InfoCard variant="error" message={errors.server} />
                                    </div>
                                )}
                                <div className="mt-4 space-y-3">
                                    <InfoCard variant="success" message={values.success?.message} />
                                    <ImportedViewClients />
                                </div>
                            </>
                        )}
                    </div>
                    <hr />
                    <div>
                        <Heading text={strings.booking} />
                        <ImportHeader type="bookings" fields={bookingImportFields} />
                        {values.success?.data.type === 'bookings' && (
                            <>
                                {touched.server && !!errors.server && (
                                    <div className="mt-4">
                                        <InfoCard variant="error" message={errors.server} />
                                    </div>
                                )}
                                {!!values.success?.message && (
                                    <div className="mt-4 space-y-3">
                                        <InfoCard variant="success" message={values.success?.message} />
                                        {<ImportedViewBookings />}
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            )}

            {isImporting && (
                <div>
                    <div className="flex justify-between">
                        <Heading text={(values.type === 'clients' ? strings.client : strings.booking) + ' ' + strings.import} variant="subTitle" />
                        <div className="flex justify-end gap-4">
                            <Button variant="ghost" size="small" onClick={() => resetForm()}>
                                {strings.Cancel}
                            </Button>
                            <Button
                                onClick={() => {
                                    setFieldValue('server', '');
                                    submitForm();
                                }}
                                loading={isSubmitting}
                            >
                                {strings.Submit}
                            </Button>
                        </div>
                    </div>
                    <div className="gutter-stable soft-searchbar flex h-full min-h-[24rem] overflow-x-auto overflow-y-hidden pt-1">
                        <table className="mt-4 self-start">
                            <thead>
                                <tr>
                                    {values.sample_data?.values[0].map((v, index) => {
                                        const prevArr = values.selected_fields ?? [];
                                        const hasValue = prevArr.some((fs) => fs.index === index);
                                        const value = hasValue ? prevArr.find((a) => a.index === index)?.field : '-';
                                        const fields = values.fields ?? [];
                                        const fieldsList = fields.filter(
                                            (f) =>
                                                !prevArr.some((v) => f.field === v.field) ||
                                                prevArr.some((d) => d.index === index && d.field === f.field),
                                        );
                                        return (
                                            <td key={index} className="border-r px-4 pb-4 dark:border-gray-600">
                                                <Select
                                                    displayValue={(v) => (v === '-' ? strings.Select : fields.find((f) => f.field === v)?.name)}
                                                    value={value}
                                                    onChange={(val) => {
                                                        setFieldTouched('selected_fields');

                                                        if (val === '-') {
                                                            setFieldValue(
                                                                'selected_fields',
                                                                prevArr.filter((v) => v.index !== index),
                                                            );
                                                            return;
                                                        }

                                                        if (hasValue) {
                                                            setFieldValue(
                                                                'selected_fields',
                                                                prevArr.map((v) => (v.index === index ? { field: val, index } : v)),
                                                            );
                                                        } else {
                                                            setFieldValue('selected_fields', [...prevArr, { field: val, index }]);
                                                        }
                                                    }}
                                                    className="w-60"
                                                >
                                                    <Select.Option value={'-'}>{strings.Select}</Select.Option>
                                                    {fieldsList.map((f, i) => (
                                                        <Select.Option value={f.field} key={i}>
                                                            <span className="flex items-center space-x-1">
                                                                <span>{f.name}</span>
                                                                {f.required && <span className="text-xs text-red-500">({strings.Required})</span>}
                                                                {f.unique && <span className="text-xs text-red-500">({strings.unique})</span>}
                                                            </span>
                                                        </Select.Option>
                                                    ))}
                                                </Select>
                                            </td>
                                        );
                                    })}
                                </tr>
                            </thead>
                            <tbody>
                                {!!values.sample_data?.values.length &&
                                    values.sample_data.values.map((samples, index) => {
                                        return (
                                            <tr key={index} className="border-t dark:border-gray-600">
                                                {samples.map((v, indx) => (
                                                    <td key={indx} className="border-r px-4 py-2 dark:border-gray-600">
                                                        {v}
                                                    </td>
                                                ))}
                                            </tr>
                                        );
                                    })}
                            </tbody>
                        </table>
                    </div>

                    <p className="mt-1">
                        {strings.formatString(strings.showing_out_of, values.sample_data?.values.length ?? 0, values.sample_data?.count ?? 0)}
                    </p>
                </div>
            )}
            {touched.server && !!errors.server && (
                <div className="mt-4">
                    <InfoCard variant="error" message={errors.server} />
                </div>
            )}
        </div>
    );
};
const ImportWithProvider: React.FC = () => {
    return (
        <ImportProvider>
            <Import />
        </ImportProvider>
    );
};

export default ImportWithProvider;
