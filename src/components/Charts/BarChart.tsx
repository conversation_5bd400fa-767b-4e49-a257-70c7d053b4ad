import useTheme from '@hooks/useTheme';
import { Bar, <PERSON><PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { chartColors } from './PieChartSingleColor';

export interface PieChartProps {
    data: BarChartData[];
    labelName?: string;
    color?: 'primary' | 'orange';
}

export type BarChartData = { name: string; value: number };

const colors = {
    primary: chartColors.primary,
    orange: chartColors.orange,
};

const BarChartComponent: React.FC<PieChartProps> = ({ data, labelName, color = 'primary' }) => {
    const { isDark } = useTheme();

    return (
        <ResponsiveContainer width="100%" height={350}>
            <BarChart data={data}>
                <CartesianGrid strokeDasharray="6 4" vertical={false} opacity={isDark ? 0.5 : 1} />
                <XAxis tickLine={false} dataKey="name" />
                <YAxis width={undefined} dataKey="value" tickLine={false} />
                <Tooltip
                    cursor={{ fill: isDark ? '#FFFFFF0D' : '#0000000D' }}
                    labelClassName="dark:text-black"
                    wrapperClassName="rounded-xl border-0"
                />
                <Bar dataKey="value" name={labelName} fill={colors[color]} radius={[4, 4, 0, 0]} />
            </BarChart>
        </ResponsiveContainer>
    );
};

export default BarChartComponent;
