import { generateUserFullName } from '@/helper';
import IconButton from '@components/form/IconButton';
import ListIcon from '@icons/List';
import LogIcon from '@icons/Log';
import { UserDocument } from '@interface/model/document';
import strings from '@lang/Lang';
import EditIcon from '@partials/Icons/Edit';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import { useNavigate } from 'react-router';
import { processOptions } from './DocumentReportsModal';
import PowerIcon from '@partials/Icons/Power';

export interface DocumentsAndReportsListItemProps {
    document: UserDocument;
    onView: (document: UserDocument) => void;
    onDelete: (document: UserDocument) => void;
    onEdit: (document: UserDocument) => void;
}

const DocumentsAndReportsListItem: React.FC<DocumentsAndReportsListItemProps> = ({ document, onView, onEdit, onDelete }) => {
    const { renderDate } = useHours();
    const navigate = useNavigate();
    function onLogClick() {
        navigate(`/management-system/documents-and-reports/${document.id}/logs`);
    }

    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className="alternate-tr-desktop hidden rounded-none md:table-row">{desktopListItem()}</tr>
        </>
    );

    function mobileListItem() {
        return (
            <Table.Td>
                <div className="space-y-2">
                    <p
                        className={cx(
                            'line-clamp-2 max-w-2xl overflow-ellipsis break-all text-base font-medium',
                            document?.deleted_at && 'text-mediumGray',
                        )}
                    >
                        {document.title}
                    </p>
                    <div className="flex justify-between">
                        <div className={cx(document?.deleted_at && 'text-mediumGray')}>{renderDate(document.created_at)}</div>
                        <div className={cx(document?.deleted_at && 'text-mediumGray')}>
                            {strings.versions}: {document.version_count}
                        </div>
                    </div>
                    <div className={cx('flex justify-between', document?.deleted_at && 'text-mediumGray')}>
                        <span>
                            {strings.Signed_by}: {generateUserFullName(document.signed_by)}
                        </span>
                        <span>{RenderProcess(document.process ?? '')}</span>
                    </div>
                    <div className="flex space-x-1.5">
                        <IconButton onClick={() => navigate(`/management-system/documents-and-reports/${document?.id}/versions`)} name={strings.list}>
                            <ListIcon />
                        </IconButton>
                        <IconButton onClick={() => onView(document)} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                        <IconButton onClick={() => onEdit(document)} name={strings.edit}>
                            <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => onDelete(document)}>
                            <PowerIcon slash={!document.deleted_at} />
                        </IconButton>
                    </div>
                </div>
            </Table.Td>
        );
    }

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <p className={cx('line-clamp-2 max-w-2xl overflow-ellipsis break-all', document?.deleted_at && 'text-mediumGray')}>
                        {document.title}
                    </p>
                </Table.Td>
                <Table.Td>
                    <div className={cx(document?.deleted_at && 'text-mediumGray')}>{renderDate(document.created_at)}</div>
                </Table.Td>
                <Table.Td>
                    <div className={cx(document?.deleted_at && 'text-mediumGray')}>{RenderProcess(document.process ?? '')}</div>
                </Table.Td>
                <Table.Td>
                    <div className={cx(document?.deleted_at && 'text-mediumGray')}>{document.version_count}</div>
                </Table.Td>
                <Table.Td>
                    <div className={cx(document?.deleted_at && 'text-mediumGray')}>{generateUserFullName(document.signed_by)}</div>
                </Table.Td>
                <Table.Td className="p-2">
                    <div className="flex justify-end space-x-1.5">
                        <IconButton onClick={() => navigate(`/management-system/documents-and-reports/${document?.id}/versions`)} name={strings.list}>
                            <ListIcon />
                        </IconButton>
                        <IconButton onClick={() => onView(document)} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                        <IconButton onClick={onLogClick} name={strings.logs}>
                            <LogIcon />
                        </IconButton>
                        <IconButton onClick={() => onEdit(document)} name={strings.edit}>
                            <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => onDelete(document)} name={!document.deleted_at ? strings.InActive : strings.Active}>
                            <PowerIcon slash={!document.deleted_at} />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }
};

function RenderProcess(value: string) {
    return (
        <span
            className={cx(
                'select-none whitespace-pre-wrap rounded-md px-2 py-1 text-sm text-white',
                value === 'PATIENT_SAFETY' && 'bg-orange-400 dark:bg-orange-500',
                value === 'DEVIATION_MGMT' && 'bg-blue-400 dark:bg-blue-500',
                value === 'THE_BUSINESS' && 'bg-green-400 dark:bg-green-500',
            )}
        >
            {processOptions().find((option) => option.value === value)?.label}
        </span>
    );
}

export default DocumentsAndReportsListItem;
