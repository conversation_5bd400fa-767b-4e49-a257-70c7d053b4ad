import CalendarSelect from '@components/Calendar/Custom/CalendarSelect';
import ScheduleAutocompleteMultiple from '@components/Calendar/Schedule/SelectTemplate/ScheduleAutocompleteMultiple';
import Card from '@components/card';
import Button from '@components/form/Button';
import Select from '@components/form/Select';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import useLocalStorage from '@hooks/useLocalStorage';
import useSessionStorage from '@hooks/useSessionStorage';
import ExportIcon from '@icons/Export';
import { CompanyUserResponse } from '@interface/common';
import strings from '@lang/Lang';
import dayjs from 'dayjs';
import { FC, createContext, useContext, useState, PropsWithChildren } from 'react';
import { useForm } from 'react-hook-form';
import useSWR from 'swr';
import { useHours } from './TimeProvider';

interface IReportsContext {
    startDate: string;
    endDate: string;
    selectedPractitioners: number[];
    period: 'day' | 'week' | 'month';
}

const ReportsContext = createContext<IReportsContext>({
    endDate: '',
    startDate: '',
    selectedPractitioners: [],
    period: 'day',
});

interface IReportsProvider {
    title: string;
    section?: 'record' | 'booking' | 'pos';
}
interface FormData {
    selectPractitioner: number[];
    timeFilter: IReportsContext['period'];
    startDate: string;
    endDate: string;
}

const ReportsProvider: FC<PropsWithChildren<IReportsProvider>> = ({ children, title, section }) => {
    const {
        userType: { isUser },
        user,
    } = useAuth();

    const { storedValue: initialStartDate, setStorageValue: setStartDate } = useLocalStorage<string>(
        'reports_start_date',
        dayjs(new Date()).subtract(30, 'days').format('YYYY-MM-DD'),
    );
    const { storedValue: initialEndDate, setStorageValue: setEndDate } = useLocalStorage<string>(
        'reports_end_date',
        dayjs(new Date()).format('YYYY-MM-DD'),
    );
    const { storedValue: initialTimeFrame, setStorageValue: setTimeFrame } = useLocalStorage<IReportsContext['period']>('reports_time_frame', 'week');

    const { storedValue: initialUserIds, setStorageValue: setInitialUserIds } = useSessionStorage<number[]>('reports_initial_user_ids', []);

    const {
        register,
        handleSubmit,
        setValue,
        watch,
        getValues,
        formState: { errors },
    } = useForm<FormData>({
        defaultValues: {
            selectPractitioner: user && isUser ? [user.id] : (initialUserIds ?? []),
            timeFilter: initialTimeFrame,
            startDate: initialStartDate,
            endDate: initialEndDate,
        },
    });

    const { data: usersData } = useSWR<CompanyUserResponse, Error>(!user ? null : isUser ? null : api.userAllFilter({ filter: 'all' }));

    const { dateFormat, parseDate, renderDate } = useHours();

    const startDate = getValues('startDate');
    const endDate = getValues('endDate');
    const selectPractitioner = getValues('selectPractitioner');
    const timeFilter = watch('timeFilter');

    const [dashError, setDashError] = useState<boolean>(false);
    const [isDownloading, setIsDownloading] = useState(false);

    return (
        <ReportsContext.Provider value={{ startDate, endDate, selectedPractitioners: selectPractitioner, period: timeFilter ?? 'week' }}>
            <Card className="mb-4">
                <div className="flex flex-wrap justify-between gap-4">
                    <Heading text={title} />
                    <Button size="small" loading={isDownloading} onClick={onDownloadClick}>
                        <ExportIcon className="mr-2" />
                        {strings.export}
                    </Button>
                </div>
                <div className="grid grid-cols-2 gap-4 pt-5 md:grid-cols-4">
                    <ScheduleAutocompleteMultiple
                        label={strings.team_member}
                        customText={strings.everyone}
                        value={selectPractitioner}
                        options={usersData?.data || []}
                        disabled={isUser ? true : false}
                        userLength={2}
                        image={true}
                        onChange={async (list) => {
                            setValue('selectPractitioner', list);
                            if (list.toString() != initialUserIds?.toString()) setInitialUserIds(list);
                        }}
                    />
                    <Select
                        value={timeFilter}
                        displayValue={(val) => (val === 'day' ? strings.Daily : val === 'week' ? strings.Weekly : strings.Monthly)}
                        label={strings.period}
                        onChange={(val) => {
                            setValue('timeFilter', val as FormData['timeFilter']);
                            setTimeFrame(val as FormData['timeFilter']);
                        }}
                    >
                        <Select.Option value={'day'}>{strings.Daily}</Select.Option>
                        <Select.Option value={'week'}>{strings.Weekly}</Select.Option>
                        <Select.Option value={'month'}>{strings.Monthly}</Select.Option>
                    </Select>
                    <CalendarSelect
                        selectedDate={startDate && renderDate(startDate)}
                        onChange={(date) => {
                            if (endDate && endDate >= parseDate(date).format('YYYY-MM-DD')) {
                                setValue('startDate', parseDate(date).format('YYYY-MM-DD'));
                                setStartDate(parseDate(date).format('YYYY-MM-DD'));
                                setDashError(false);
                            } else {
                                setDashError(true);
                            }
                        }}
                        inputProps={{
                            label: strings.start_date,
                            placeholder: strings.Date,
                        }}
                        error={dashError && strings.start_date_end_date_message}
                        maxToday
                    />
                    <CalendarSelect
                        selectedDate={endDate && parseDate(endDate).isValid() ? parseDate(endDate).format(dateFormat) : renderDate(endDate)}
                        onChange={(date) => {
                            if (startDate && startDate <= parseDate(date).format('YYYY-MM-DD')) {
                                setValue('endDate', parseDate(date).format('YYYY-MM-DD'));
                                setEndDate(parseDate(date).format('YYYY-MM-DD'));
                                setDashError(false);
                            } else {
                                setDashError(true);
                            }
                        }}
                        alignRight
                        inputProps={{
                            label: strings.end_date,
                            placeholder: strings.Date,
                        }}
                    />
                </div>
            </Card>
            {children}
        </ReportsContext.Provider>
    );

    async function onDownloadClick() {
        if (isDownloading) return;
        setIsDownloading(true);
        const fileName = `${[section, strings.reports, startDate, endDate, timeFilter].join('_').toLowerCase()}.xlsx`;
        await download(fileName, api.reports.export(section ?? 'record', startDate, endDate, timeFilter, selectPractitioner));
        setIsDownloading(false);
    }
};

async function download(fileName: string, url: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            credentials: 'include',
            headers: {
                'X-App-Locale': strings.getLanguage() ?? 'en',
            },
        });

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);
        a.setAttribute('download', fileName);
        a.click();
    } catch (error) {
        console.error(error);
    }
}

export const useReports = () => useContext(ReportsContext);

export default ReportsProvider;
