import IconButton from '@components/form/IconButton';
import SignIcon from '@icons/Sign';
import strings from '@lang/Lang';
import { useHours } from '@provider/TimeProvider';
import * as React from 'react';
import { useParams } from 'react-router';
import useSWR from 'swr';
import api from '../../../../configs/api';
import { aDownload, generateClientFullName, generateUserFullName, timeZone } from '../../../../helper';
import { ClientResponse } from '../../../../interfaces/common';
import { ClientLetterOfConsent } from '../../../../interfaces/model/clientLetterOfConsent';
import DeleteIcon from '../../../../partials/Icons/Delete';
import EyeIcon from '../../../../partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import CancelCircleIcon from '@partials/Icons/CancelCircle';
import NoteIcon from '@icons/Note';
import LoadingIcon from '@icons/Loading';
import DownloadIcon from '@partials/Icons/Download';

export interface ClientLetterOfConsentListItemProps {
    consent: ClientLetterOfConsent;
    onDeleteClick?: (data: ClientLetterOfConsent) => void;
    onViewClick?: (data: ClientLetterOfConsent) => void;
    onSignClick?: (data: ClientLetterOfConsent) => void;
    onCancelClick?: (data: ClientLetterOfConsent) => void;
    onNoteViewClick?: (data: ClientLetterOfConsent) => void;
}
async function download(consent: ClientLetterOfConsent) {
    const response = await fetch(api.clientDownloadDataPdf(consent.client_id), {
        method: 'POST',
        headers: {
            Accept: 'application/json',
            
            'Content-Type': 'application/json',
            'X-App-Locale': strings.getLanguage(),
            'X-Time-Zone': timeZone(),
        },
        credentials: 'include',
        body: JSON.stringify({ id: consent.id, type: `App\\ClientLetterOfConsent` }),
    });

    const data = await response.blob();
    await aDownload('client_consent', window.URL.createObjectURL(data));
}
const ClientLetterOfConsentListItem: React.FC<ClientLetterOfConsentListItemProps> = ({
    consent,
    onDeleteClick = () => {},
    onViewClick = () => {},
    onSignClick = () => {},
    onCancelClick = () => {},
    onNoteViewClick = () => {},
}) => {
    const { clientId }: { clientId?: string } = useParams();
    const [downloading, setDownloading] = React.useState(false);

    const { data } = useSWR<ClientResponse, Error>(api.clientSingle(clientId));
    const { renderDateTime, renderDate } = useHours();
    const onDownloadClick = async (data: ClientLetterOfConsent) => {
        if (downloading) return;
        setDownloading(true);
        await download(data);
        setDownloading(false);
    };
    return (
        <>
            <tr className={`alternate-tr-mobile md:hidden ${consent?.is_cancelled ? 'text-mediumGray' : ''}`}>{mobileListItem()}</tr>
            <tr className={`alternate-tr-desktop hidden md:table-row ${consent?.is_cancelled ? 'text-mediumGray' : ''}`}>{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <button
                        className="w-full"
                        onClick={() => {
                            onViewClick(consent);
                        }}
                    >
                        <div className="flex break-all">
                            <span className="">{consent.consent_title || '-'}</span>
                        </div>
                    </button>
                </Table.Td>
                <Table.Td>
                    <div className="flex break-all">
                        <span className="">{renderDateTime(consent?.created_at, { utc: true, isShowLocal: true }) || '-'}</span>
                    </div>
                </Table.Td>
                <Table.Td>
                    <div>{generateUserFullName(consent?.verified_signed_by)}</div>
                </Table.Td>
                <Table.Td>
                    <p className={`text-sm uppercase ${consent?.verified_signed_by ? '' : 'text-lightRed'}`}>
                        {consent?.verified_signed_by ? strings.Signed : strings.unsigned}
                    </p>
                </Table.Td>
                <Table.Td className="p-2">
                    <div className="flex justify-end space-x-1.5">
                        {!consent?.verified_signed_at && (
                            <IconButton onClick={() => onSignClick(consent)} children={<SignIcon />} name={strings.Sign} />
                        )}
                        {consent?.verified_signed_at && !consent?.is_cancelled ? (
                            <IconButton onClick={() => onCancelClick(consent)} name={strings.CANCEL}>
                                <CancelCircleIcon />
                            </IconButton>
                        ) : (
                            <></>
                        )}
                        {consent?.is_cancelled ? (
                            <IconButton children={<NoteIcon />} name={strings.note} onClick={() => onNoteViewClick(consent)} />
                        ) : (
                            <></>
                        )}
                        <IconButton onClick={() => onViewClick(consent)} children={<EyeIcon />} name={strings.View} />
                        {!consent.verified_sign && (
                            <IconButton onClick={() => onDeleteClick(consent)} children={<DeleteIcon />} name={strings.Delete} />
                        )}
                        {consent?.verified_sign ? (
                            <IconButton
                                onClick={() => onDownloadClick(consent)}
                                disabled={downloading}
                                children={downloading ? <LoadingIcon /> : <DownloadIcon />}
                                name={strings.download}
                            />
                        ) : (
                            <></>
                        )}
                    </div>
                </Table.Td>
            </>
        );
    }
    function mobileListItem() {
        return (
            <Table.Td>
                <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                        <p>{consent.consent_title || '-'}</p>
                        <p>{renderDate(consent?.created_at) || '-'}</p>
                    </div>
                    <p className="flex justify-between text-xs">
                        <span className="text-gray-500 dark:text-gray-400">
                            {strings.Signed_by} {generateClientFullName(data?.data)}
                        </span>
                        <span className={`uppercase ${consent?.verified_signed_by ? '' : 'text-lightRed'}`}>
                            {consent?.verified_signed_by ? strings.Signed : strings.unsigned}
                        </span>
                    </p>
                    <div className="-ml-2 space-x-0.5">
                        {!consent?.verified_signed_at && <IconButton onClick={() => onSignClick(consent)} children={<SignIcon />} />}
                        {consent?.verified_signed_at && !consent?.is_cancelled ? (
                            <IconButton onClick={() => onCancelClick(consent)}>
                                <CancelCircleIcon />
                            </IconButton>
                        ) : (
                            <></>
                        )}
                        {consent?.cancel_note ? <IconButton children={<NoteIcon />} onClick={() => onNoteViewClick(consent)} /> : <></>}
                        <IconButton onClick={() => onViewClick(consent)} children={<EyeIcon />} />
                        {!consent.verified_sign && <IconButton onClick={() => onDeleteClick(consent)} children={<DeleteIcon />} />}
                        {consent?.verified_sign ? (
                            <IconButton
                                onClick={() => onDownloadClick(consent)}
                                disabled={downloading}
                                children={downloading ? <LoadingIcon /> : <DownloadIcon />}
                                name={strings.download}
                            />
                        ) : (
                            <></>
                        )}
                    </div>
                </div>
            </Table.Td>
        );
    }
};

export default ClientLetterOfConsentListItem;
