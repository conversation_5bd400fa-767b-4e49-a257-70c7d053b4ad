import Heading from '@components/heading/Heading';
import api from '@configs/api';
import { Service } from '@interface/model/service';
import strings from '@lang/Lang';
import Table from '@partials/Table/PageTable';
import * as React from 'react';

import Pagination from '@components/form/Pagination';
import Skeleton from '@components/Skeleton/Skeleton';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { CommonModelPaginatedResponse } from '@interface/common';
import EmptyData from '@partials/Error/EmptyData';
import DocumentListItem from './DocumentListItem';

export interface ServicProps {}

export interface LegalDocument {
    id: number;
    company_id: number;
    type: string;
    language: string;
    signed_at?: any;
    title: string;
    description?: any;
    file_path: string;
    created_at: string;
    updated_at: string;
    url: string;
}

const Documents: React.FC<ServicProps> = () => {
    const [openModal, setOpenModal] = React.useState(false);
    const [selectedService, setSelectedService] = React.useState<Service>();

    const { error, data, mutate, page, orderBy, setPage, loading, orderDirection, filter, filterData, orderData, handleOrder, search, setSearch } =
        usePaginationSWR<CommonModelPaginatedResponse<LegalDocument>, Error>(api.legalDocuments.list, {});

    return (
        <div>
            <div className="mb-4">
                <Heading text={strings.documents} />
            </div>

            <Table>
                <Table.Head>
                    <Table.Th children={strings.TITLE} />
                    <Table.Th>{strings.added_on}</Table.Th>
                    <Table.Th children={strings.Actions} className="text-right" />
                </Table.Head>
                <Table.Body>
                    {loading ? <Documentskeleton limit={10} /> : <></>}
                    {!loading && !data?.data.length && <EmptyData cardHeight="h-[65vh]" />}
                    {data?.data.map((document) => <DocumentListItem key={document.id} document={document} />)}
                </Table.Body>
            </Table>
            <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
        </div>
    );
};

function Documentskeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={`loading_${index}`}>
                        <td className="p-2">
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}

export default Documents;
