import Card, { CardProps } from '@components/card';
import Button from '@components/form/Button';
import Heading from '@components/heading/Heading';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { CommonModelPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Table from '@partials/Table/PageTable';
import { useState } from 'react';
import ReportTableViewAllModal from './ReportTableViewAllModal';

export interface ReportTableComponentProps<T> extends CardProps {
    api: string;
    title: string;
    headings: string[];
    renderColumn: (item: T, colIndex: number, rowIndex: number, url?: string | null) => React.ReactNode[];
}

const LIMIT = 5;
export function ReportTableComponent<T>({ api, title, headings, renderColumn, ...props }: ReportTableComponentProps<T>) {
    const { data, loading, url } = usePaginationSWR<CommonModelPaginatedResponse<T>, Error>(api, { limit: LIMIT });

    const [open, setOpen] = useState(false);

    const noMoreData = (data?.data?.length ?? 5) < LIMIT;

    return (
        <>
            {open && (
                <ReportTableViewAllModal<T>
                    openModal={open}
                    onClose={() => setOpen(false)}
                    headings={headings}
                    api={api}
                    renderColumn={renderColumn}
                    title={title}
                />
            )}
            <Card {...props}>
                <Heading text={title} className="mb-3" />
                {loading ? (
                    <SectionLoading />
                ) : (
                    <Table>
                        <Table.Head>
                            {headings.map((heading, index) => (
                                <Table.Td key={index}>{heading}</Table.Td>
                            ))}
                        </Table.Head>
                        <Table.Body>
                            {!loading && !data?.data?.length && <EmptyData cardHeight="h-80" />}
                            {data?.data?.map((item, index) => (
                                <tr key={index}>
                                    {headings.map((heading, colIndex) => (
                                        <Table.Td key={colIndex}>{renderColumn(item, colIndex, index, url)?.at(colIndex)}</Table.Td>
                                    ))}
                                </tr>
                            ))}
                            {data?.data?.length !== 0 && !noMoreData && (
                                <tr>
                                    <td colSpan={6}>
                                        <Button size="small" variant="ghost" className="mx-auto my-2" onClick={() => setOpen(true)}>
                                            {strings.ViewAll}
                                        </Button>
                                    </td>
                                </tr>
                            )}
                        </Table.Body>
                    </Table>
                )}
            </Card>
        </>
    );
}

export default ReportTableComponent;
