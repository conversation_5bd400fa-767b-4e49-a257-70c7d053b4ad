import Error from '@components/form/Error';
import TextArea from '@components/form/TextArea';
import { FastField, getIn } from 'formik';
import api from '../../configs/api';
import strings from '../../lang/Lang';
import QuestionYesNo from '../Portal/QuestionYesNo';
import { ClientQuestionary } from '@interface/model/clientQuestionary';
import { useMemo, useState } from 'react';
import IconButton from '@components/form/IconButton';
import EyeIcon from '@partials/Icons/Eye';
import LoadingIcon from '@icons/Loading';
import DownloadIcon from '@components/Clients/Client/BeforeAfterImage/components/icons/Download';
import { downloadFile } from '@/helper';
import { File } from '@interface/model/File';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import FileViewModal from '@components/File/FileViewModal';

export interface DynamicQuestionaryComponentProps {
    questionaryData: ClientQuestionary;
}

const DynamicQuestionaryViewComponent: React.FC<DynamicQuestionaryComponentProps> = ({ questionaryData }) => {
    const [downloading, setDownloading] = useState<string | undefined>();
    const [viewFile, setViewFile] = useState<[File, string]>();
    const [viewFileModalOpen, setViewFileModalOpen] = useState<boolean>(false);

    const questions = questionaryData?.questions;

    const filesMap = useMemo(() => {
        const m = new Map();

        questionaryData.files?.map((f) => {
            m.set(f.id, f);
        });

        return m;
    }, [questionaryData.id]);

    return (
        <>
            <ModalSuspense>
                {viewFileModalOpen && viewFile?.at(0) && (
                    <FileViewModal
                        openModal={viewFileModalOpen}
                        onClose={() => {
                            setViewFile(undefined);
                            setViewFileModalOpen(false);
                        }}
                        file={viewFile[0]}
                        file_name={viewFile[1]}
                    />
                )}
            </ModalSuspense>
            <div className="grid grid-flow-row grid-cols-1 gap-4 divide-y p-4 dark:divide-gray-800">
                {questions?.map((question, index) => {
                    const views: React.ReactNode[] = [];

                    const isYesNo = question.type === 'yes_no_textbox' || question.type === 'yes_no';
                    const hasTextarea = question.type === 'yes_no_textbox' || question.type === 'textbox';
                    const isFileUpload = question.type === 'file_upload';

                    const answer = questionaryData.formatted_response.at(index);

                    views.push(
                        <div className="flex flex-col px-0 py-2 md:flex-row md:px-0">
                            <p className="flex-grow font-medium">
                                <span>{index + 1}.&nbsp;</span>
                                {question.question}
                            </p>
                        </div>,
                    );

                    if (isYesNo && answer?.value) {
                        views.push(<p className="pl-5">{answer?.value == 'yes' ? strings.Yes : strings.No}</p>);
                    }

                    if (hasTextarea) {
                        views.push(<p className="pl-5">{typeof answer === 'string' ? answer : answer?.text}</p>);
                    }

                    if (isFileUpload) {
                        if (!!answer?.files?.length) {
                            views.push(
                                <div className="max-w-xl divide-y rounded-lg border dark:divide-gray-800 dark:border-gray-800">
                                    {answer?.files?.map((fileObj, i) => {
                                        const file = filesMap.get(fileObj.file_id);

                                        const amIdownloading = downloading === `download_${file.id}`;
                                        return (
                                            <div key={i} className="flex w-full items-center gap-x-3 py-1 pl-3 pr-2">
                                                <p className="flex-grow break-all first-letter:uppercase">{fileObj.file_name}</p>
                                                <IconButton
                                                    className="focus:outline-none"
                                                    onClick={() => {
                                                        setViewFile([file, fileObj.file_name]);
                                                        setViewFileModalOpen(true);
                                                    }}
                                                >
                                                    <EyeIcon />
                                                </IconButton>
                                                <IconButton className="focus:outline-none" onClick={() => onDownloadClick(file, fileObj.file_name)}>
                                                    {amIdownloading ? <LoadingIcon /> : <DownloadIcon />}
                                                </IconButton>
                                            </div>
                                        );
                                    })}
                                </div>,
                            );
                        }
                    }

                    return (
                        <div key={index} className="w-full">
                            {views}
                        </div>
                    );
                })}
            </div>
        </>
    );

    async function onDownloadClick(file: File, file_name: string) {
        setDownloading(`download_${file.id}`);

        const response = await fetch(file.url);

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);

        a.target = '_blank';

        a.setAttribute('download', file_name);
        a.click();

        setDownloading(undefined);
    }
};

export default DynamicQuestionaryViewComponent;
