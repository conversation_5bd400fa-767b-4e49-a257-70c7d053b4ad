import { Client } from "./client";
import { SubscriptionPlan } from "./plan";
import { Service } from "./service";
import { Setting } from "./setting";
import { Subscription } from "./subscription";
import { User } from "./user";

export interface Company {
    id: number
    encrypted_id: string
    company_name: string
    first_name: string
    last_name: string
    date_of_birth: string
    profile_photo: string
    language_id: number
    email: string
    mobile_number: string
    street_address: string
    zip_code: string
    city: string
    state: string
    country: string
    email_verified_at?: string | null
    password: string
    created_at: string
    updated_at?: string | null
    deleted_at?: string | null
    is_blocked: boolean
    is_read_only: boolean
    is_cancelled: boolean
    is_subscribed: boolean
    has_pending_payment: boolean
    storage_usage: string
    card_brand?: string | null
    card_last_four?: string | null
    subscriptions: Subscription[]
    unit: Unit,
    users_count: string
    clients_count: string
    procedures_count: string
    last_login_at: string
    theme?: string,
    cover_image: string;
    lead: CompanyLead,
    users?: User[],
    settings?: Setting[]
    is_black_text: string,
    country_code?: string,
    timezone: null | string,
    is_booking_on: boolean,
    is_record_on: boolean,
    is_management_on: boolean,
    pending_prescription?: number,
    signed_prescription?: number
    pivot?: pivot,
    free_trail_end_date?: string,
    free_trail_start_date?: string,
    is_free_trail_finished?: boolean,
    is_free_trail_booking_finished?:boolean,
    is_free_trail_cancelled?: number,
    user_count?: string,
    is_yearly?: number,
    free_trail_type?: string,
    free_trail_quantity?: number,
    free_trail_coupon?: string,
    free_trail_plan_id?: string,
    platforms?: platForm[],
    organization_number?: string,
    viva_account_id?: string,
    viva_merchant_id?: string,
    ccu_register_id?: string,
    connected_to_fortnox?: boolean,
    active_subscription?: Subscription
    pos_plan?: SubscriptionPlan
    record_plan?: SubscriptionPlan
    management_plan?: SubscriptionPlan
    booking_plan?: SubscriptionPlan
    has_system?: boolean
    is_pos_on: boolean
    verification?: string
    verified_for_sms?: number
}

export interface CompanyLead {
    id: number;
    company_id: number;
    notes: string;
    status: CompanyLeadStatus;
    contacted: boolean;
    created_at: string;
    updated_at: string;
}

export interface CompanyUnavailableTime {
    id: number;
    user_id: number;
    note: string;
    type: "AVAILABLE" | "NOT_AVAILABLE" | "PRIVATE_SLOT_NON_BLOCKING" | "PRIVATE_SLOT_BLOCKING"
    start_at: string;
    end_at: string;
    user: User,
    is_for_all_day: number;
    is_for_all_services: number;
    created_at: string;
    updated_at: string;
    color: string;
    description: string;
    services?: Service[];
    clients?: Client[]
    client?: Client
}

export interface CompanyBusinessTime {
    id: number;
    week_day: string;
    start_time: string;
    end_time: string;
    created_at: string;
    updated_at: string;
    type?: "OPEN" | "CLOSE"
}

export type CompanyLeadStatus = 'win' | 'lost' | 'not_decided'

export type Unit = "eur" | "usd" | "sek" | "gbp";
export interface pivot {
    user_id?: number,
    company_id?: number,
    id?: number,
    price?: string,
    invite_status?: string,
}

export interface platForm {
    company_id?: number;
    id: number;
    platform: "PRESCRIPTION" | "RECORD_SYSTEM" | "QUALITY_MANAGEMENT_ACTIVATION" | "QUALITY_MANAGEMENT_SYSTEM" | "BOOKING_SYSTEM";
    license_agreement?: string;
}