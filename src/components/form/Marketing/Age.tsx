import strings from '@lang/Lang';
import Select from '../Select';
import Input from '../Input';
import cx from 'classix';

export interface AgeProps {
    value: string;
    inputValue: string;
    onChange: (value: string) => void;
    onChangeInput: (value: string) => void;
    error?: string;
}
const Age: React.FC<AgeProps> = ({ onChange, onChangeInput, value = 'above', inputValue, error }) => {
    const ageCondition = [
        {
            text: strings.above,
            key: 'above',
        },
        {
            text: strings.below,
            key: 'below',
        },
        {
            text: strings.equal,
            key: 'equal',
        },
    ];
    return (
        <div className={cx('w-full', error && 'pb-2')}>
            <div className="flex flex-wrap gap-2 items-center  rounded-lg bg-grayishBlue px-3 py-1.5 dark:bg-darkGray">
                <p className="px-1">{strings.age}</p>
                <Select
                    placeholder={strings.age}
                    displayValue={(val) => val}
                    value={value}
                    className="!rounded-[4px] !py-1 dark:!bg-black"
                    onChange={onChange}
                    color="white"
                >
                    {ageCondition.map((time, index) => (
                        <Select.Option key={index} value={time.key}>
                            {time.text}
                        </Select.Option>
                    ))}
                </Select>
                <Input
                    className="!w-16 !rounded-[4px] !px-2 !py-1 dark:!bg-black"
                    placeholder={strings.No}
                    onChange={(e)=>onChangeInput(e.target.value.replace(/[-.]/g, ''))}
                    value={inputValue}
                    color="white"
                    type='number'
                />
            </div>
            <p className="text-[10px] text-red-500 first-letter:capitalize">{error}</p>
        </div>
    );
};
export default Age;
