import StarIcon from '@icons/Star';
import cx from 'classix';

export interface StarRatingProps {
    rating: number;
    size?: 'small' | 'normal';
}

const array = Array(5).fill('');

const StarRating: React.FC<StarRatingProps> = ({ rating, size = 'normal' }) => {
    const remaining = getDecimalPart(rating);

    const sizeClass = cx(size === 'normal' && 'size-5', size === 'small' && 'size-3');

    return (
        <div className="flex items-center gap-x-0.5">
            {array.map((_, index) => {
                const rated = index < rating;
                const last = index === Math.floor(rating);
                return (
                    <span className="relative" key={index}>
                        <StarIcon key={index} className={cx(sizeClass, rated && !last ? 'text-[#FBBC05]' : 'text-mediumGray/50')} />
                        {!!remaining && !!last && (
                            <span className="absolute top-0 inset-0 overflow-hidden" style={last ? { width: `${remaining * 100}%` } : undefined}>
                                <StarIcon key={index} className={cx('text-[#FBBC05]', sizeClass)} />
                            </span>
                        )}
                    </span>
                );
            })}
        </div>
    );
};

function getDecimalPart(num: number) {
    const parts = num.toString().split('.');
    if (parts.length > 1) {
        return parseFloat(`0.${parts[1]}`);
    } else {
        return 0;
    }
}

export default StarRating;
