import Skeleton from '@components/Skeleton/Skeleton';
import Pagination from '@components/form/Pagination';
import api from '@configs/api';
import { FilterType, usePaginationSWR } from '@hooks/usePaginationSWR';
import { ClientPrescriptionPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import Card from '@partials/Paper/PagePaper';
import Table from '@partials/Table/PageTable';
import * as React from 'react';
import { useNavigate, useParams } from 'react-router';
import PrescriptionsListItem from '../Prescriptions/PrescriptionsListItem';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { ClientPrescription } from '@interface/model/prescription';
import BackToClinicDashboard from './BackToClinicDashboard';
import Heading from '@components/heading/Heading';
import Select from '@components/form/Select';
import Input from '@components/form/Input';
import SearchIcon from '@icons/Search';
import EmptyData from '@partials/Error/EmptyData';

const PresscriptionSignModal = React.lazy(() => import('../Prescriptions/PresscriptionSignModal'));
const PrescriptionModal = React.lazy(() => import('../Prescriptions/PrescriptionModal'));
const ClientPrescriptionViewModal = React.lazy(() => import('@components/Clients/Client/Prescription/ClientPrescriptionViewModal'));
const PrescriptionDeleteModal = React.lazy(() => import('../Prescriptions/PrescriptionDeleteModal'));
export interface ClinicPrescriptionViewProps {}

const ClinicPrescriptionView: React.FC<ClinicPrescriptionViewProps> = () => {
    const { clinicId }: { clinicId?: string } = useParams();
    const [openSignModal, setOpenSignModal] = React.useState(false);
    const [openEditModal, setOpenEditModal] = React.useState(false);
    const [openViewModal, setOpenViewModal] = React.useState(false);
    const [openDeleteModal, setOpenDeleteModal] = React.useState(false);
    const [selectedPrescription, setSelectedPrescription] = React.useState<ClientPrescription>();
    const statusFilter = [
        {
            text: strings.All,
            key: 'all',
            filter: 'ALL',
        },
        {
            text: strings.prescribed,
            key: 'prescribed',
            filter: 'PRESCRIBED',
        },
        {
            text: strings.not_prescribed,
            key: 'not_prescribed',
            filter: 'NOT_PRESCRIBED',
        },
    ];
    const { data, page, orderBy, mutate, setPage, loading, orderDirection, handleOrder, filter, filterData, search, setSearch } = usePaginationSWR<
        ClientPrescriptionPaginatedResponse,
        Error
    >(api.prescriptionList(clinicId), {
        filter: statusFilter[0].filter,
    });
    const navigate = useNavigate();

    return (
        <div>
            <ModalSuspense>
                {openSignModal && (
                    <PresscriptionSignModal
                        openModal={openSignModal}
                        setOpenModal={setOpenSignModal}
                        mutate={mutate}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openViewModal && (
                    <ClientPrescriptionViewModal
                        openModal={openViewModal}
                        handleClose={() => {
                            setOpenViewModal(false);
                            setSelectedPrescription(undefined);
                        }}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openEditModal && (
                    <PrescriptionModal
                        openModal={openEditModal}
                        setOpenModal={setOpenEditModal}
                        setSelectedPrescription={setSelectedPrescription}
                        setOpenSignModal={setOpenSignModal}
                        mutate={mutate}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openDeleteModal && (
                    <PrescriptionDeleteModal
                        open={openDeleteModal}
                        selectedPrescription={selectedPrescription}
                        mutate={mutate}
                        handleClose={() => {
                            setOpenDeleteModal(false);
                        }}
                    />
                )}
            </ModalSuspense>
            <BackToClinicDashboard />
            <Card>
                <div className="flex grid-flow-row grid-cols-7 flex-col gap-3 pb-4 md:grid">
                    <Heading text={strings.prescription} variant="bigTitle" className="col-span-4 content-center" />
                    <div className="col-span-2">
                        <Input
                            placeholder={strings.Search}
                            value={search || ''}
                            icon={<SearchIcon className="text-mediumGray" />}
                            onChange={(event) => setSearch(event.target.value)}
                        />
                    </div>
                    <Select
                        value={statusFilter.find((oFilter) => oFilter.filter === filter)?.key}
                        onChange={(val) => {
                            const selectedFilter = statusFilter.find((oFilter) => oFilter.key === val);

                            if (selectedFilter) {
                                filterData(selectedFilter.filter, '' as FilterType, null);
                            }
                        }}
                        displayValue={(val) => statusFilter.find((oFilter) => oFilter.key === val)?.text}
                    >
                        {statusFilter.map((filter) => (
                            <Select.Option value={filter.key} key={`list_filter_${filter.key}`}>
                                {filter.text}
                            </Select.Option>
                        ))}
                    </Select>
                </div>
                <Table>
                    <Table.Head>
                        <Table.ThSort sort={orderBy === 'title' && orderDirection} onClick={() => handleOrder('title')} children={strings.TITLE} />
                        <Table.Th children={strings.clinic} />
                        <Table.ThSort
                            sort={orderBy === 'client_name' && orderDirection}
                            onClick={() => handleOrder('client_name')}
                            children={strings.client}
                        />
                        <Table.ThSort
                            sort={orderBy === 'created_at' && orderDirection}
                            onClick={() => handleOrder('created_at')}
                            children={strings.DateTime}
                        />
                        <Table.Th children={strings.status} />
                        <Table.Th children={strings.Actions} className="text-right" />
                    </Table.Head>
                    <Table.Body>
                        {data?.data?.map((prescription, index) => (
                            <PrescriptionsListItem
                                key={index}
                                prescription={prescription}
                                onProfileClick={() => navigate(`/clinics/${prescription?.company?.id}/client/${prescription?.client_id}`)}
                                onViewClick={() => {
                                    setOpenViewModal(true);
                                    setSelectedPrescription(prescription);
                                }}
                                onEditClick={() => {
                                    setOpenEditModal(true);
                                    setSelectedPrescription(prescription);
                                }}
                                onSignClick={() => {
                                    setOpenSignModal(true);
                                    setSelectedPrescription(prescription);
                                }}
                                onDeleteClick={() => {
                                    setOpenDeleteModal(true);
                                    setSelectedPrescription(prescription);
                                }}
                            />
                        ))}
                        {loading ? <ClinicPrescriptionSkeleton limit={8} /> : <></>}
                        {!loading && !data?.data.length && <EmptyData cardHeight="!h-[68vh]" />}
                    </Table.Body>
                </Table>
                <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
            </Card>
        </div>
    );
};

function ClinicPrescriptionSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={index}>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>

                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>

                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>

                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mx-2 h-10 w-full cursor-wait" />
                            </div>
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default ClinicPrescriptionView;
