import { commonFetch } from '@/helper';
import api from '@configs/api';
import { CompanyResponse } from '@interface/common';
import { Company } from '@interface/model/company';
import FullPageError from '@partials/Error/FullPageError';
import FullPageLoading from '@partials/Loadings/FullPageLoading';
import { createContext, PropsWithChildren, useContext, useMemo } from 'react';
import { useParams } from 'react-router';
import useSWRImmutable from 'swr/immutable';

interface RegistrationPortalCompanyProviderProps extends PropsWithChildren {}
interface RegistrationPortalCompanyProviderContext {
    company: Company;
    companyId?: string;
}

const CompanyContext = createContext<RegistrationPortalCompanyProviderContext>({ company: {} as Company });

export const PortalCompanyProvider = ({ children }: RegistrationPortalCompanyProviderProps) => {
    const { companyId }: { companyId?: string } = useParams();
    const { data: companyData, error: companyError, isLoading } = useSWRImmutable<CompanyResponse, Error>(api.companyInfo(companyId), commonFetch);
    const company = useMemo(() => companyData?.data, [companyData?.data.encrypted_id]);

    if (isLoading) {
        return <FullPageLoading />;
    }
    if (companyError) {
        return <FullPageError code={companyError?.status || 500} message={companyError.message || 'Server error, contact admin.'} />;
    }

    if (companyData?.data?.is_blocked || companyData?.data?.is_read_only) {
        return <FullPageError message={`Your company is ${companyData?.data?.is_blocked ? 'blocked' : 'read only'}`} />;
    }

    return <CompanyContext.Provider value={{ company: company!, companyId: companyId }}>{children}</CompanyContext.Provider>;
};

export const usePortalCompany = () => {
    const context = useContext(CompanyContext);
    if (!context) {
        throw new Error('usePortalCompany must be used within a PortalCompanyProvider');
    }
    return context;
};
