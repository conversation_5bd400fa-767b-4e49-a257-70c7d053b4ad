import Error from '@components/form/Error';
import Input from '@components/form/Input';
import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions, Popover, Transition } from '@headlessui/react';
import LoadingIcon from '@icons/Loading';
import strings from '@lang/Lang';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import Label from '@components/form/Label';
import { Service } from '@interface/model/service';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import AvatarIcon from '@icons/Avatar';
import ClientsIcon from '@icons/Clients';
import cx from 'classix';
import ChevronIcon from '@partials/Icons/Chevron';

export interface MarketingSelectServiceProps {
    options: Service[];
    value: number[];
    children?: ReactNode;
    onChange: (val: number[]) => void;
    error?: string | false;
    disabled?: boolean;
    loading?: boolean;
    label?: ReactNode;
    require?: boolean;
    placeholder?: string;
    serviceLength?: number;
    color?: 'primary' | 'white';
    className?: string;
}

const MarketingSelectService = ({
    options,
    onChange,
    value,
    error,
    disabled,
    loading,
    label,
    require,
    placeholder,
    color = 'primary',
    serviceLength,
    className,
}: MarketingSelectServiceProps) => {
    const [query, setQuery] = useState('');
    const serviceListLength = serviceLength || 3;
    const [selectedValue, setSelectedValue] = useState<number[] | null>(value);

    useEffect(() => {
        if (value !== selectedValue) {
            setSelectedValue(value);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    useEffect(() => {
        if (selectedValue) {
            onChange(selectedValue);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedValue]);

    function isSelected(service_id: number) {
        return selectedValue?.includes(service_id);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const list = useMemo(() => {
        if (query === '') return options;
        return options.filter((service) => service.name.toLowerCase().replace(/\s+/g, '').includes(query.toLowerCase().replace(/\s+/g, '')));
    }, [query, options]);
    const singleServiceList = list.filter((item) => !item.category || (item.category && item.category.group_booking === 0));
    const groupServiceList = list.filter((item) => item.category && item.category.group_booking === 1);
    const singleServiceSearchList = useMemo(() => {
        if (query === '') return singleServiceList;
        return singleServiceList.filter((service) =>
            service.name.toLowerCase().replace(/\s+/g, '').includes(query.toLowerCase().replace(/\s+/g, '')),
        );
    }, [query, singleServiceList]);

    const groupServiceSearchList = useMemo(() => {
        if (query === '') return groupServiceList;
        return groupServiceList.filter((service) => service.name.toLowerCase().replace(/\s+/g, '').includes(query.toLowerCase().replace(/\s+/g, '')));
    }, [query, groupServiceList]);
    const filteredServices = options.filter((v) => isSelected(v.id)).map((v) => v.name);
    const filteredServicesTitle = filteredServices.join(', ');
    const checkedAll = !(selectedValue ?? []).length;

    function addOrRemoveService(id: number) {
        const arr = [...(selectedValue ?? [])];
        if (arr.includes(id)) {
            arr.splice(arr.indexOf(id), 1); // remove array of value
            return arr;
        }
        arr.push(id); // add array of value
        return arr;
    }
    const cxName = cx(color === 'primary' && 'border-grayishBlue bg-grayishBlue', color === 'white' && 'border-white bg-white', className);
    return (
        <div className="relative">
            <Combobox value={selectedValue} onChange={setSelectedValue} disabled={disabled} immediate>
                {({ open }) => (
                    <div className="relative">
                        <Label label={label} required={require} />
                        <div
                            className={cx(cxName, 'rounded-md')}
                            onClick={(ev) => {
                                if (query.trim().length) ev.preventDefault();
                            }}
                        >
                            {loading && (
                                <div className="absolute inset-0 flex w-10 items-center justify-center text-primary">
                                    <LoadingIcon />
                                </div>
                            )}
                            <ComboboxInput
                                className={cx(
                                    'form-input w-full cursor-pointer rounded-md !py-1 !pl-2 pr-8 hover:border-mediumGray focus:border-primary focus:ring-1 md:!py-2',
                                    error ? 'border-red-500 focus:text-black' : '',
                                    cxName,
                                )}
                                autoComplete="new-password"
                                placeholder={placeholder}
                                displayValue={(service: Service) =>
                                    selectedValue && selectedValue.length > serviceListLength
                                        ? `${selectedValue.length} ${strings.selected} `
                                        : selectedValue?.length
                                          ? filteredServicesTitle
                                          : strings.any
                                }
                                onChange={(event) => setQuery(event.target.value)}
                            />
                            <ComboboxButton className="absolute right-1 top-1/2 -translate-y-1/2 p-1 text-gray-500 sm:p-2">
                                <ChevronIcon className="text-xl" side={open ? 'top' : 'bottom'} />
                            </ComboboxButton>
                        </div>
                        <Transition
                            as="div"
                            className="soft-searchbar absolute z-50 mt-1 max-h-56 w-full overflow-auto rounded-[4px] bg-white pt-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-black sm:text-sm"
                            enter="transition-all duration-300"
                            enterFrom="top-[90%] opacity-0"
                            enterTo="top-full opacity-100"
                            leave="transition-all ease-out duration-75"
                            leaveFrom="top-full opacity-100"
                            leaveTo="top-[90%] opacity-0"
                            afterLeave={() => setQuery('')}
                        >
                            {!disabled && (
                                <ComboboxOptions>
                                    <div>
                                        <div>
                                            <ComboboxOption
                                                value={[]}
                                                className={`relative flex w-full cursor-pointer select-none py-2 pr-4 focus-within:bg-purpleGray focus-within:text-primary hover:bg-purpleGray hover:text-primary focus-within:dark:text-white dark:hover:bg-dimGray`}
                                                onClick={() => {
                                                    setSelectedValue([]);
                                                }}
                                            >
                                                <div
                                                    className={`relative flex items-center space-x-1 pl-3 transition-all ${
                                                        checkedAll ? 'font-semibold text-primary dark:text-primaryLight' : ''
                                                    }`}
                                                >
                                                    <div className="flex gap-2">
                                                        <p
                                                            className={`flex space-x-1 ${
                                                                checkedAll ? 'font-semibold text-primary dark:text-primaryLight' : ''
                                                            }`}
                                                        >
                                                            {strings.any}
                                                        </p>
                                                    </div>
                                                </div>
                                            </ComboboxOption>
                                            <p className="flex items-center gap-1 p-2 font-medium text-primary dark:text-primaryLight">
                                                <AvatarIcon className="text-lg" />
                                                {strings.single_booking}
                                            </p>
                                            <div className="soft-searchbar divide-y border-r-[1px] dark:divide-gray-700">
                                                {!singleServiceSearchList.length ? (
                                                    <div className="relative cursor-default select-none px-4 py-2 text-gray-700 dark:text-white">
                                                        {strings.no_data}
                                                    </div>
                                                ) : (
                                                    singleServiceSearchList.map((service, index) => {
                                                        const selected = isSelected(service.id);
                                                        return (
                                                            <button
                                                                key={`option_${index}`}
                                                                className={`relative flex w-full cursor-default select-none py-3 pr-4 hover:bg-purpleGray hover:text-primary dark:hover:bg-dimGray ${
                                                                    selected ? 'text-primary dark:text-white' : 'text-gray-900 dark:text-white'
                                                                }`}
                                                                onClick={() => {
                                                                    setSelectedValue(addOrRemoveService(service.id));
                                                                }}
                                                            >
                                                                <div
                                                                    className={`relative flex items-center space-x-1 pl-3 transition-all ${
                                                                        selected ? 'font-semibold text-primary dark:text-primaryLight' : ''
                                                                    }`}
                                                                >
                                                                    <Checkbox checked={selected} label="" readOnly />
                                                                    <p className="text-start">{service.name}</p>
                                                                </div>
                                                            </button>
                                                        );
                                                    })
                                                )}
                                            </div>
                                        </div>
                                        <div>
                                            <p className="flex items-center gap-1 p-2 font-medium text-primary dark:text-primaryLight">
                                                <ClientsIcon className="text-xl" />
                                                {strings.group} {strings.booking}
                                            </p>
                                            <div className="soft-searchbar divide-y pb-4 dark:divide-gray-700">
                                                {!groupServiceSearchList.length ? (
                                                    <div className="relative cursor-default select-none px-4 py-2 text-gray-700 dark:text-white">
                                                        {strings.no_data}
                                                    </div>
                                                ) : (
                                                    groupServiceSearchList.map((service, index) => {
                                                        const selected = selectedValue?.includes(service.id);
                                                        return (
                                                            <button
                                                                key={`option_${index}`}
                                                                className={`relative flex w-full cursor-default select-none py-3 pr-4 hover:bg-purpleGray hover:text-primary dark:hover:bg-dimGray ${
                                                                    selected ? 'text-primary dark:text-white' : 'text-gray-900 dark:text-white'
                                                                }`}
                                                                onClick={async () => {
                                                                    await setSelectedValue(addOrRemoveService(service.id));
                                                                }}
                                                            >
                                                                <div
                                                                    className={`relative flex items-center space-x-1 pl-3 transition-all ${
                                                                        selected ? 'font-semibold text-primary dark:text-primaryLight' : ''
                                                                    }`}
                                                                >
                                                                    <Checkbox checked={selected} label="" readOnly />
                                                                    <p className="text-start">{service.name}</p>
                                                                </div>
                                                            </button>
                                                        );
                                                    })
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </ComboboxOptions>
                            )}
                        </Transition>
                    </div>
                )}
            </Combobox>
            <Error error={error} />
        </div>
    );
};

export default MarketingSelectService;
