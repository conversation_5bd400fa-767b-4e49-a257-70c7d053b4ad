import cx from 'classix';
import { useCallback, useMemo, useState } from 'react';
import { Cell, Pie, PieChart, ResponsiveContainer, Sector, Tooltip } from 'recharts';
import { PieSectorDataItem } from 'recharts/types/polar/Pie';

export interface PieChartProps {
    data: PieChartData[];
}

export type PieChartData = { name: string; value: number; color?: string };

const COLORS = ['#50C982', '#5551CE', '#5FC3FB', '#FF69AE', '#FFB04D', '#6B2737', '#AC3931', '#3943B7'];

type MouseEvent = (data: any, index: number, e: React.MouseEvent) => void;

const PieChartComponent: React.FC<PieChartProps> = ({ data }) => {
    const [activeIndex, setActiveIndex] = useState<number>();

    const dataWithColor = useMemo(() => {
        return data.map((item, index) => {
            item.color = COLORS[index % COLORS.length];
            return item;
        });
    }, [data]);

    const activeShape = useCallback((props: PieSectorDataItem) => {
        return (
            <g>
                <text x={props.cx} y={props.cy} dy={8} textAnchor="middle" className="fill-black dark:fill-white">
                    {props.payload.name}
                </text>
                <Sector {...props} outerRadius={(props.outerRadius ?? 0) + 3} />
            </g>
        );
    }, []);

    const onMouseEnter: MouseEvent = useCallback((_, index) => setActiveIndex(index), []);
    const onMouseLeave: MouseEvent = useCallback(() => setActiveIndex(undefined), []);

    return (
        <div className="flex h-full w-full flex-col items-center justify-center md:flex-row lg:flex-col xl:flex-row">
            <ResponsiveContainer width={'100%'} height={250}>
                <PieChart>
                    <Pie
                        dataKey="value"
                        data={data}
                        cx="50%"
                        cy="50%"
                        innerRadius={58}
                        outerRadius={100}
                        cornerRadius={6}
                        startAngle={180}
                        endAngle={-180}
                        activeShape={activeShape}
                        onMouseEnter={onMouseEnter}
                        onMouseLeave={onMouseLeave}
                        stroke="none"
                        paddingAngle={1}
                    >
                        {dataWithColor.map((entry, index) => {
                            return <Cell key={index} fill={entry.color} />;
                        })}
                    </Pie>
                </PieChart>
            </ResponsiveContainer>
            <div className="w-full space-y-1">
                {dataWithColor.map((entry, index) => {
                    const selected = activeIndex === index;
                    return (
                        <div key={entry.name} className={cx('flex max-w-64 items-center justify-between transition-all', selected && 'font-medium')}>
                            <div className="flex items-center space-x-2">
                                <div
                                    className={cx('size-5 rounded-md duration-150', selected ? 'scale-125' : '')}
                                    style={{ backgroundColor: entry.color }}
                                ></div>
                                <span>{entry.name}</span>
                            </div>
                            <span>{entry.value}</span>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default PieChartComponent;
