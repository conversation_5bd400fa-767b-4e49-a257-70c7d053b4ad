import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';

export interface ClientImportantNoteViewProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    note?: string;
}
const ClientImportantNoteViewModal: React.FC<ClientImportantNoteViewProps> = ({ openModal, note, setOpenModal }) => {
    return (
        <Modal open={openModal} title={strings.important_note} handleClose={() => setOpenModal(false)}>
            <div
                className="prose whitespace-pre-wrap break-words p-6 pt-2 dark:prose-invert"
                dangerouslySetInnerHTML={{
                    __html: note || '',
                }}
            ></div>
        </Modal>
    );
};
export default ClientImportantNoteViewModal;
