const data = {
    posTermsText: 'By clicking the button, you agree to our {0}.',

    point_of_sale: 'Point of Sale',
    add_service_product: 'Add Service/Product',
    tax: 'Tax',
    net: 'Net',
    gross: 'Gross',
    export: 'Export',

    register: 'Register',

    SAVE: 'SAVE',
    total: 'Total',

    apply_discount: 'Apply Discount',
    discount: 'Discount',

    value: 'Value',

    select_service_product: 'Select Service / Product',

    items: 'Item(s)',
    added: 'Added',

    in_stock: (v: string | number) => `${v} in stock`,
    abort: `Abort`,

    action: 'Action',

    no_item_found: 'No Item Found',

    reference_note: 'Reference note',
    reference_placeholder: 'Any specific customization requests or special details about your order are welcome in the reference note.',

    download_receipt: 'Download Receipt',
    adjusted_total: 'Adjusted Total',

    paid_at: 'Paid At',
    refund_at: 'Refund At',

    other: 'Other',
    batch: 'Batch',

    print: 'Print',
    items_added: 'Item(s) Added',

    qty: 'Qty',

    hold_on: 'Hold On',
    registered_company_mismatch: 'Registered Company Mismatch',
    notice_account_registered_with: (country: string) => `We have noticed that you have registered your account with - ${country}`,
    for_now_pos_works_with: (country: string) => `For now our Point of Sale module works for ${country} country only.`,
    verify_details: 'Verify Details',

    viva: {
        viva: 'Viva',
        viva_wallet: 'Viva Wallet',
        connect: 'Connect',
        connected: 'Connected',
        merchant_id: 'Merchant ID',
        unique_assigned_to_you: 'This is the unique identifier assigned to you by Viva Wallet.',
        connect_to_vivawallet: 'Connect to Viva Wallet',
        connected_to_vivawallet: 'You are connected to Viva Wallet',
        for_payment_solutions: 'For payment solution',
        join_waitlist: 'Join waitlist',
        joined_waitlist: 'Joined waitlist.',
        join_the_waitlist: 'Join the waitlist!',
        already_joined_the_waitlist: 'Already joined the waitlist',
        successful_connected: 'Successful Connected!',
        verification_is_currently_in_progress: 'Verification ongoing',
        checkout_company_logo: 'Upload Company Logo',
        checkout_company_logo_desc: 'Add your company logo to the online payment checkout page',
    },

    fortnox: {
        fortnox: 'Fortnox',
        fortnox_invoice: 'Fortnox Invoice',
        connect: 'Connect',
        connected: 'Connected',
        connect_to_fortnox: 'Connect to Fortnox',
        connected_to_fortnox: 'You are connected to Fortnox',
        for_invoice_solutions: 'For invoice solution',
        successful_connected: 'Successfully Connected!',
        verification_is_currently_in_progress: 'Verification ongoing',
        fortnox_connect_message: 'Please connect to Fortnox to use invoice payments',
        checkout: {
            add_your_company_logo: 'Add Your Company Logo to the Online Payment Checkout',
            step_1: (url: string) =>
                `<strong>Step 1:</strong> Log into <a href="${url}" target="_blank" class='dark:text-primaryLight text-primary'>Viva</a> and select the relevant account.`,
            step_2: `<strong>Step 2:</strong> Navigate to <strong>Sales</strong> in the left navigation menu:`,
            step_3: `<strong>Step 3:</strong> Go to <strong>Online payments > Websites/Apps</strong>, as shown below:`,
            step_4: (paymentSourceId: number | string) =>
                `<strong>Step 4:</strong> Select the Description <strong>MERIDIQ</strong> (ID: ${paymentSourceId}).`,
            step_5: `<strong>Step 5:</strong> Upload the Company Logo in the designated section:`,
            step_6: `<strong>Step 6:</strong> Check all checkboxes and click <strong>Update</strong>.`,
            finish: `Now, your company logo will appear on the checkout page!`,
        },

        register_for_viva: 'Register For Viva Wallet',
        what_is_next: 'What is next...',
        verify_detail_message_1: 'Your information will be securely sent to Viva Wallet.',
        verify_detail_message_2: 'Expect a follow-up from Viva Wallet for further document verification.',
        verify_detail_message_3: 'We appreciate your cooperation in ensuring a smooth process.',
        verify_detail_message_4: 'If you have any questions, please contact us.',
    },
    swish: {
        swish: 'Swish',
        swish_qr: 'Swish QR',
        info_upload_swish_qr: 'Upload your swish qr which will be shown when paying with swish.',
    },

    ecr: {
        ecr: 'ECR',
        ecr_message: 'A unique identifier assigned to a specific electronic cash register or point-of-sale terminal',
        ecr_id: 'ECR ID',
    },

    infrasec: {
        infrasec: 'Infrasec',
        for_tax_compliance: 'For tax compliance',
        connect: 'Connect',
        connected: 'Connected',

        no_changes_allowed_organization_number: 'Organization becomes fixed upon submission; no changes permitted.',
        validation: {
            organization_number_is_required: 'Organization number is required',
            organization_number_invalid_format: 'Organization number format must be 000000-0000',
            agree_to_terms_and_conditions: 'please agree to terms and conditions',
        },

        register_for_infrasec: 'Register For Infrasec',
        what_is_next: 'What is next...',
        verify_detail_message_1: 'Your registration with Infrasec is complete.',
        verify_detail_message_2: 'You can now start using our services immediately.',
        verify_detail_message_3: 'We’re excited to have you on board!',
        verify_detail_message_4: 'If you have any questions, feel free to contact us.',
    },

    payment: {
        confirm_payment_manually: 'Confirm payment manually',

        payment_collected: 'Payment Collected',

        payment: 'Payment',

        payment_summary: 'Payment Summary',
        subtotal: 'Subtotal',
        tax: 'Tax',
        pay: (v: string) => `Pay ${v}`,

        amountHadBeenPaid: (v: string | number) => `Your payment of ${v} has been received and processed successfully.`,

        validation: {
            product_or_service_is_required: 'Product Or Service is required',
            discount_value_is_required: 'Discount value is required',
            discount_value_must_be_numeric: 'Discount value must be numeric',
            discount_value_must_be_between_1_to_100: 'Discount value must be between 1 and 100',
            discount_value_must_be_less_than: (value: string) => `Discount value must be less than ${value}`,
            add_service_or_products_before_activating_gift_card: 'please add service or product before activating gift card',
            add_service_or_products_before_activating_discount: 'please add service or product before activating discount',

            gift_card_id_is_required: 'Gift card id required',
            gift_card_value_is_required: 'Gift card value required',
            gift_card_id_greater_than_4: 'Gift card id must be greater than 4 letters',
            gift_card_value_must_be_integer: 'Must be integer',
            gift_card_value_cannot_be_greater_than: (value: string) => `Gift card value cannot be greater than ${value}`,

            please_select_a_terminal_device: 'please select a terminal device',
        },
        items: 'Items',
        qty: 'Qty.',
    },

    terminal: {
        select_terminal_device: 'Select Terminal Device',
        no_terminal_devices_found: 'No Terminal Devices Found',
        remember_my_choice: 'Remember my choice',

        pos_terminal_devices: 'POS Terminal Devices',
        nick_name: 'Nick Name',
        virtual_terminal_id: 'Virtual Terminal ID',
        virtual_terminal_id_error: 'Can not change Virtual Terminal ID',
        update_terminal: 'Update POS Terminal',
        terminal_device: 'Terminal Device',
    },

    product: {
        products: 'Products',
        products_library: 'Product Library',
        product_category: 'Product Category',
        filter_by_category: 'Filter by category',

        add_product: 'Add Product',
        update_product: 'Update Product',

        ean_upc: 'EAN/UPC',
        product_name: 'Product Name',
        product_code: 'Product Code',

        selling_price: 'Selling Price',
        base_price: 'Base Price',
        tax_information: 'Tax Information',
        stock: 'Stock',
        inventory: 'Inventory',

        out_of_stock: 'Out of stock',

        validation: {
            product_name_is_required: 'Product name is required',
            selling_price_is_required: 'Selling price is required',
            base_price_is_required: 'Base price is required',
            tax_infomation_is_required: 'Tax information is required',
            inventory_stock_is_required: 'Inventory/Stock is required',

            selling_price_less_than_base_price: 'selling price cannot be less than the base price',
            selling_price_greater: 'selling price must be greater than 0',
            base_price_greater: 'base price must be greater than 0',
        },
    },

    receipt: {
        receipt: 'Receipt',
        receipt_detail: 'Receipt Detail',
        payment_by: 'Payment by',

        paid_by_gift_card: 'Paid with Gift Card',
        paid_by_bank: 'Paid with Credit/Debit Card',
        paid_by_viva: 'Paid with Viva Wallet',
        paid_by_swish: 'Paid with Swish',

        print_receipt: 'Print Receipt',
        print_receipt_copy: 'Print',
        email_receipt_copy: 'Email',

        status: {
            paid: 'Paid',
            partially_refunded: 'Partially Refunded',
            aborted: 'Aborted',
            pending: 'Pending',
            processing: 'Processing',
            refunded: 'Refunded',
            cancelled: 'Cancelled',
        },
        transaction_type: 'Transaction Type',
    },

    refund: {
        refund: 'Refund',
        refunds: 'Refunds',
        refund_transaction: 'Refund Transaction',
        amount_to_be_refunded: 'Amount to be refunded',
        refund_amount_note: 'For a Partial refund, Enter an amount smaller than the total',
        refund_in_gift_card: 'Refund in Gift Card',
        refund_in_bank: 'Refund in Bank (Credit/Debit Card)',
        refund_in_swish: 'Refund in Swish',

        refund_error: 'Refund can not be undone, once you submit it. Are you sure you want to do it ?',

        select_payment_mode: 'Select Payment Mode',

        enter_refund_amount: 'Enter Refund Amount',
        refund_summary: 'Refund Summary',

        refund_completed: 'Refund Completed',
        refunded: 'Refunded',

        applied_discount: 'Applied Discount',

        amount_refunded: 'Amount Refunded',

        refundValue: (v: string) => `Refund ${v}`,

        amountHadBeenRefunded: (v: string | number) => `A refund of ${v} has been processed for you.`,

        validation: {
            refund_amount_required: 'Refund amount is required',
            refund_amount_must_be_integer: 'Refund amount must be integer',
            refund_amount_cannot_be_greater_than: (v: string | number) => `Refund amount cannot be greater than ${v}`,
            total_refund_amount_cannot_be_greater_than: (v: string | number) => `Total refund amount cannot be greater than ${v}`,
            expiry_date_must_be_after: (v: string | number) => `Expiry date must be after ${v}`,
        },

        status: {
            aborted: 'Aborted',
            pending: 'Pending',
            processing: 'Processing',
            refunded: 'Refunded',
            cancelled: 'Cancelled',
        },

        refund_confirmation_text: [
            'To register the refund in MERIDID please click on "Refund".',
            'To complete the actual transfer to the patient, you need to log in to the correct account depending on the payment method used:',
            `<b>Swish</b>: Log in to your <b>online bank</b>.`,
            `<b>Klarna</b>: Log in to your <b>Viva account</b>.`,
            'Then follow these steps:',
            'Locate the relevant payment.',
            'Process the refund manually.',
            'Feel free to contact us at <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><span class="text-primary dark:text-primaryLight"><EMAIL></span></a> if you need help at any step!',
        ],
    },

    gift_card: {
        gift_card_id: 'Gift Card ID',
        card_id: 'Card ID',
        Avl_bal: 'Avl. Bal.',

        available_balance: 'Available Balance',
        use: 'Use',

        gift_card: 'Gift Card',

        gift_cards: 'Gift Cards',
        issue_gift_card: 'Issue Gift Card',

        gift_card_number: 'Gift Card Number',
        initial_value: 'Initial Value',

        card_number: 'Card number',
        status: 'Status',
        validity: 'Validity',
        customer: 'Customer',
        remaining: 'Remaining / Value',
        expiry_date: 'Expiry Date',

        auto_generated: 'Auto Generated',

        validation: {
            inital_value_is_required: 'Initial value is required',
            inital_value_must_be_greater_than_0: 'Initial value must be greater than 0',
            expiry_date_is_required: 'Expiry date is required',
        },
    },

    report: {
        report: 'Report',
        zreport: 'Z Report',
        xreport: 'X Report',
        close_batch: 'Close Batch',
        closing_amount: 'Closing Amount',
    },

    config: {
        batch: 'Batch',

        z_report_email_to: 'Z report to this email',
        auto_z_report_to: 'Automatically send Z report to this email',
        auto_z_report_time: 'Automatic batch closing triggers an automatic generation of a Z-report.',
    },

    please_enable_point_of_sale_first: 'Please enable point of sale first',
    enable_point_of_sale: 'Enable Point of Sale system',
    enable_online_payment_checkbox: 'Enable online payment checkbox from pos configuration',
};

export default data;
