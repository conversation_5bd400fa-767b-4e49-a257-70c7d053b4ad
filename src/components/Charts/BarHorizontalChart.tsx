import useTheme from '@hooks/useTheme';
import { Bar, <PERSON>C<PERSON>, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { chartColors } from './PieChartSingleColor';

export interface PieChartProps {
    data: PieChartData[];
    labelName?: string;
    color?: 'primary' | 'orange';
}

export type PieChartData = { name: string; value: number };

const colors = {
    primary: chartColors.primary,
    orange: chartColors.orange,
};

const BarHorizontalChartComponent: React.FC<PieChartProps> = ({ data, labelName, color = 'primary' }) => {
    const { isDark } = useTheme();

    return (
        <ResponsiveContainer width="100%" height={150}>
            <BarChart data={data} layout="vertical">
                <CartesianGrid horizontal={false} opacity={isDark ? 0.5 : 1} strokeDasharray="6 6" />
                <XAxis type="number" tickLine={false} axisLine={false} padding={{ left: 4 }} />
                <YAxis dataKey="name" type="category" tickLine={false} axisLine={false} />
                <Tooltip
                    cursor={{ fill: isDark ? '#FFFFFF0D' : '#0000000D' }}
                    labelClassName="dark:text-black"
                    wrapperClassName="rounded-xl border-0"
                />
                <Bar dataKey="value" name={labelName} fill={colors[color]} radius={4} />
            </BarChart>
        </ResponsiveContainer>
    );
};

export default BarHorizontalChartComponent;
