import { allowTaxInfoAccess, commonFetch, handleExportReceiptData } from '@/helper';
import CalendarSelect from '@components/Calendar/Custom/CalendarSelect';
import Button from '@components/form/Button';
import ClientLazySearch from '@components/form/ClientLazySearch';
import Input from '@components/form/Input';
import Label from '@components/form/Label';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import { useLocalStorageNew } from '@hooks/useLocalStorageNew';
import { useNetworkState } from '@hooks/useNetworkState';
import { GiftCardResponse, ReceiptResponse, TerminalResponse } from '@interface/common';
import { Client } from '@interface/model/client';
import { POSPaymentMethod, ReceiptExportType } from '@interface/model/receipt';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import useSettings from '@provider/SettingsProvider';
import { useHours } from '@provider/TimeProvider';
import { Formik, isInteger } from 'formik';
import * as React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import SelectedTerminalViewComponent from '../Component/SelectedTerminalViewComponent';
import SelectSwishModal from '../Modal/SelectSwishModal';
import SelectTerminalModal from '../Modal/SelectTerminalModal';
import PaymentButtonComponent from '../Payment/Component/PaymentButtonComponent';
import PaymentSuccessModal from '../Payment/Modal/PaymentSuccessModal';
import { validateGiftCard } from './validations';
import Select from '@components/form/Select';
import countries from '@configs/countries';
import useLocalStorage from '@hooks/useLocalStorage';
import cx from 'classix';

export interface IGiftCardValues {
    terminal_id: string;
    gift_code: string;
    client_id: number;
    client?: Client;
    initial_value: string;
    tax_information: string;
    expired_at: string;
    payment_method?: POSPaymentMethod;
    server?: string;
}

export interface GiftCardModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    mutate: () => Promise<any>;
}

const receiptFetch = async (url: string, { arg }: { arg: string }) => {
    return commonFetch(url.replace(':id', arg));
};

const receiptAbortFetch = async (url: string, { arg }: { arg: string }) => {
    return commonFetch(url.replace(':id', arg), {
        method: 'DELETE',
    });
};

const GiftCardModal: React.FC<GiftCardModalProps> = ({ openModal, setOpenModal, mutate }) => {
    const { data: terminalData, isLoading: terminalLoading, mutate: reloadTerminals } = useSWR<TerminalResponse, Error>(api.pos.terminal.list);
    const { withCurrency, user } = useAuth();

    const terminals = terminalData?.data ?? [];

    var popupElement = document.getElementById('popup');
    const navigate = useNavigate();

    const { parseDate } = useHours();

    const [giftCardId, setGiftCardId] = React.useState<string>();
    const [receiptId, setReceiptId] = React.useState<string>();
    const { storedValue: taxValue, setStorageValue: setTaxValue } = useLocalStorage('gift_tax_information', '');
    const [openTerminal, setOpenTerminal] = React.useState<boolean>(false);

    const [loadingExport, setLoadingExport] = React.useState<ReceiptExportType | undefined>(undefined);

    const {
        data: receiptData,
        trigger,
        reset: resetReceipt,
        isMutating: isReceiptLoading,
    } = useSWRMutation<ReceiptResponse, Error, any, string>(api.pos.receipt.show(receiptId ?? ':id', { polling: true }), receiptFetch);

    const receipt = React.useMemo(() => (!!receiptId && receiptData?.data ? receiptData?.data : undefined), [receiptData?.data, receiptId]);

    const { trigger: abortTrigger, isMutating: loadingAbort } = useSWRMutation<ReceiptResponse, Error, any, string>(
        api.pos.payment.abort(receiptId ?? ':id'),
        receiptAbortFetch,
    );

    const isProcessing = React.useMemo(() => receipt?.status === 'PROCESSING' || receipt?.status === 'PENDING', [receipt?.status]);

    const [openPaymentSuccess, setOpenPaymentSuccess] = React.useState(false);

    const [savedTerminalId] = useLocalStorageNew<string>(`${user?.id}_${user?.company_id}_terminal_id`, '');

    const isOnline = useNetworkState();

    React.useEffect(() => {
        if (receipt?.status === 'PAID' && receiptId) {
            setOpenPaymentSuccess(true);
        }
    }, [receipt?.status, receiptId]);

    React.useEffect(() => {
        let interval: number | undefined;

        if (!receiptId || !isProcessing) {
            clearInterval(interval);
            return;
        }

        interval = setInterval(() => {
            trigger(receiptId);
        }, 6000);

        return () => {
            clearInterval(interval);
        };
    }, [isProcessing, receiptId, trigger]);

    const isFirstTimeTerminal = React.useRef(false);
    const [selectedTerminal, setSelectedTerminal] = React.useState<undefined | string>();
    const [openSwish, setOpenSwish] = React.useState<undefined | boolean>();

    const { getSettingValue } = useSettings();
    const swishQr = getSettingValue('POS_SWISH_QR');

    const hasAppliedForViva = React.useMemo(() => !!user?.company?.viva_account_id, [user?.company?.viva_account_id]);
    const hasConnectedToViva = React.useMemo(() => !!user?.company?.viva_merchant_id, [user?.company?.viva_merchant_id]);
    const hasConnectedToFortnox = React.useMemo(() => !!user?.company?.connected_to_fortnox, [user?.company?.connected_to_fortnox]);

    const vivaStatus = cx(
        !hasAppliedForViva && !hasConnectedToViva && 'pending',
        hasAppliedForViva && !hasConnectedToViva && 'in-process',
        hasAppliedForViva && hasConnectedToViva && 'connected',
    ) as 'pending' | 'in-process' | 'connected';

    const fortnoxStatus = hasConnectedToFortnox ? 'connected' : 'pending';

    React.useEffect(() => {
        if (!isFirstTimeTerminal.current && savedTerminalId && terminals.length) {
            setSelectedTerminal(terminals.find((t) => t.terminal_id === savedTerminalId)?.terminal_id ?? '');
            isFirstTimeTerminal.current = true;
        }

        if (!savedTerminalId && terminals.length) {
            isFirstTimeTerminal.current = true;
        }
    }, [savedTerminalId, terminals]);

    return (
        <Formik<IGiftCardValues>
            initialValues={{
                client_id: 0,
                expired_at: '',
                gift_code: '',
                initial_value: '',
                tax_information: taxValue || '',
                terminal_id: selectedTerminal || '',
            }}
            enableReinitialize
            isInitialValid={false}
            validate={validateGiftCard}
            onSubmit={async (values, { resetForm, setFieldError }) => {
                const formData = new FormData();

                if (giftCardId) {
                    formData.set('gift_card_id', giftCardId);
                }

                if (values.payment_method) {
                    formData.set('payment_method', values.payment_method);
                }
                if (values.tax_information) {
                    setTaxValue(values.tax_information);
                }
                formData.set('tax_information', values.tax_information);
                formData.set('client_id', values.client_id.toString());
                formData.set('expired_at', parseDate(values.expired_at).format('YYYY-MM-DD'));
                formData.set('initial_value', values.initial_value);

                if (!!values.terminal_id) {
                    formData.set('terminal_id', values.terminal_id);
                }

                const response = await fetch(api.pos.giftCard.store, {
                    method: 'POST',
                    headers: {
                        Accept: 'application/json',
                        'X-App-Locale': pos_strings.getLanguage(),
                    },
                    credentials: 'include',
                    body: formData,
                });

                const data = (await response.json()) as GiftCardResponse;

                if (response.status === 401) {
                    navigate('/');
                }

                if (data.status !== '1') {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                }

                // setDirtyReceipt(false);
                setGiftCardId(data.data.id.toString());
                setReceiptId(data.data.payable.id.toString());

                await trigger(data.data.payable.id.toString());

                await mutate();
                toast.success(data.message);
            }}
        >
            {({
                errors,
                values,
                touched,
                dirty,
                setFieldValue,
                setFieldTouched,
                submitForm,
                isSubmitting,
                isValidating,
                resetForm,
                handleBlur,
                handleChange,
                isValid,
            }) => {
                const { terminal_id, server, ...errorsWithoutTerminal } = errors ?? {};

                const handleModelClose = async () => {
                    if (isSubmitting || isValidating || isProcessing || !isOnline) return;
                    setOpenModal(false);
                    await resetForm();
                };

                if (errors.server && popupElement) {
                    popupElement.scrollTop = popupElement.scrollHeight;
                }
                const terminal = terminals.find((t) => t.terminal_id === values.terminal_id);

                return (
                    <>
                        {openSwish && (
                            <SelectSwishModal
                                open={openSwish}
                                image={swishQr ? api.storageUrl(swishQr) : undefined}
                                loading={isSubmitting}
                                handleClose={() => {
                                    if (isSubmitting) return;

                                    setOpenSwish(false);
                                }}
                                handleSubmit={async () => {
                                    await setFieldValue('payment_method', 'swish');
                                    submitForm();

                                    setOpenSwish(false);
                                }}
                                amount={withCurrency(values?.initial_value ? values?.initial_value : '0')}
                            />
                        )}
                        <Modal
                            open={openSwish ? false : openModal}
                            loading={isSubmitting}
                            title={pos_strings.gift_card.issue_gift_card}
                            handleClose={handleModelClose}
                            buttonChildren={
                                <div className="flex flex-col justify-center space-y-4 border-t p-4 dark:border-dimGray">
                                    <PaymentButtonComponent
                                        showViva={vivaStatus === 'connected'}
                                        showSwish={true}
                                        showFortnox={fortnoxStatus === 'connected'}
                                        disabled={
                                            (!!errorsWithoutTerminal && !!Object.keys(errorsWithoutTerminal).length) ||
                                            isProcessing ||
                                            loadingAbort ||
                                            isSubmitting ||
                                            isReceiptLoading ||
                                            !isValid
                                        }
                                        onClickSwish={async () => {
                                            setOpenSwish(true);
                                        }}
                                        onClickViva={async () => {
                                            await setFieldValue('payment_method', 'viva');

                                            submitForm();
                                        }}
                                        onClickFortnox={async () => {
                                            await setFieldValue('payment_method', 'fortnox_invoice');

                                            submitForm();
                                        }}
                                        onClickTerminal={async () => {
                                            reloadTerminals();

                                            setOpenTerminal(true);
                                        }}
                                        processing={isProcessing && values.payment_method ? values.payment_method : false}
                                        terminalLoading={isSubmitting || isProcessing || terminalLoading}
                                        swishLoading={(isSubmitting || isReceiptLoading) && values.payment_method === 'swish'}
                                        vivaLoading={(isSubmitting || isReceiptLoading) && values.payment_method === 'viva'}
                                        fortnoxLoading={(isSubmitting || isReceiptLoading) && values.payment_method === 'fortnox_invoice'}
                                        showTerminal={!values.terminal_id}
                                        total={withCurrency(!!values?.initial_value ? values?.initial_value : '0')}
                                    />
                                    {isProcessing && receiptId ? (
                                        <Button
                                            variant="ghost"
                                            fullWidth
                                            loading={loadingAbort || isSubmitting}
                                            onClick={async () => {
                                                await abortTrigger(receiptId);

                                                await trigger(receiptId);
                                            }}
                                            disabled={loadingAbort || isSubmitting || !isOnline}
                                        >
                                            {pos_strings.abort}
                                        </Button>
                                    ) : (
                                        <CancelButton disabled={isSubmitting || isProcessing || isValidating || !isOnline} onClick={handleModelClose}>
                                            {strings.Cancel}
                                        </CancelButton>
                                    )}
                                </div>
                            }
                        >
                            {openTerminal && (
                                <SelectTerminalModal
                                    open={openTerminal}
                                    disabled={!!(isProcessing || isSubmitting) || !isOnline}
                                    loading={isSubmitting || terminalLoading}
                                    handleClose={() => {
                                        if (isSubmitting) return;

                                        setOpenTerminal(false);
                                    }}
                                    handleSubmit={async (terminal_id) => {
                                        await setFieldValue('terminal_id', terminal_id);

                                        setOpenTerminal(false);
                                    }}
                                    terminals={terminals}
                                    selected_terminal={terminals.find((t) => {
                                        if (values.terminal_id) {
                                            return t.terminal_id === values.terminal_id;
                                        }
                                        return t.terminal_id === values.terminal_id || t.terminal_id === savedTerminalId;
                                    })}
                                    error={errors.server}
                                />
                            )}
                            {openPaymentSuccess && (
                                <PaymentSuccessModal
                                    open={openPaymentSuccess}
                                    amount={withCurrency(receipt?.paid_amount_formatted ?? '0')}
                                    loading={loadingExport}
                                    handleClose={async (type) => {
                                        try {
                                            if (type && receipt) {
                                                setLoadingExport(type);
                                                const data = await handleExportReceiptData({
                                                    lang: pos_strings.getLanguage(),
                                                    receipt: receipt,
                                                    title: `${pos_strings.receipt.receipt} - ${receipt.viva_receipt_id}.pdf`,
                                                    type: type,
                                                });

                                                if (data && data?.status === '1') {
                                                    toast.success(data?.message);
                                                }
                                                if (data && data?.status !== '1') {
                                                    toast.error(data?.message);
                                                }
                                                setLoadingExport(undefined);
                                            }

                                            await resetForm();
                                            // await setDirtyReceipt(false);
                                            await setReceiptId(undefined);
                                            await setOpenPaymentSuccess(false);
                                            await resetReceipt();
                                            setGiftCardId(undefined);
                                            handleModelClose();
                                            mutate();
                                        } catch (error) {
                                            setLoadingExport(undefined);
                                        }
                                    }}
                                />
                            )}
                            <FormikErrorFocus />
                            <div className="grid grid-flow-row grid-cols-1 gap-y-4 p-4">
                                <Input
                                    name="gift_code"
                                    value={values.gift_code || ''}
                                    label={pos_strings.gift_card.gift_card_number}
                                    placeholder={'XXXXXX'}
                                    onText={pos_strings.gift_card.auto_generated}
                                    error={touched?.gift_code && errors.gift_code}
                                    disabled
                                />
                                <div className="flex-grow">
                                    <Label label={strings.client} required />
                                    <ClientLazySearch
                                        value={values?.client_id}
                                        onChange={async (client) => {
                                            if (!client) return;
                                            await setFieldTouched('client_id');
                                            await setFieldValue('client_id', client.id);
                                            await setFieldValue('client', client);
                                        }}
                                        error={touched?.client_id && errors.client_id}
                                        inputProps={{ label: strings.client, required: true, placeholder: strings.client }}
                                        disabled={!!(isProcessing || isSubmitting) || !isOnline}
                                    />
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <Input
                                        disabled={!!(isProcessing || isSubmitting) || !isOnline}
                                        name="initial_value"
                                        value={values.initial_value || ''}
                                        onChange={(e) => {
                                            if (!e.target.value || isInteger(e.target.value)) {
                                                handleChange(e);
                                            }
                                        }}
                                        required
                                        onBlur={handleBlur}
                                        error={touched?.initial_value && errors.initial_value}
                                        label={pos_strings.gift_card.initial_value}
                                        placeholder={strings.amount}
                                    />
                                    <CalendarSelect
                                        disabled={!!(isProcessing || isSubmitting) || !isOnline}
                                        selectedDate={values.expired_at}
                                        placeholder={strings.select_date}
                                        inputProps={{
                                            label: pos_strings.gift_card.expiry_date,
                                            placeholder: strings.select_date,
                                            required: true,
                                        }}
                                        error={touched?.expired_at && errors.expired_at}
                                        minToday
                                        onChange={(date) => {
                                            setFieldTouched('expired_at');
                                            setFieldValue('expired_at', date);
                                        }}
                                    />
                                    {allowTaxInfoAccess(user?.company) ? (
                                        <Select
                                            label={pos_strings.product.tax_information}
                                            value={values.tax_information}
                                            placeholder={strings.Select}
                                            onChange={(value) => {
                                                setFieldTouched('tax_information');
                                                setFieldValue('tax_information', value.toString());
                                            }}
                                            displayValue={(value) => `${value}%`}
                                        >
                                            {countries
                                                .find((v) => v.name === user?.company?.country)
                                                ?.tax_rates?.map((item) => (
                                                    <Select.Option key={item} value={item}>
                                                        {item}%
                                                    </Select.Option>
                                                ))}
                                        </Select>
                                    ) : (
                                        <></>
                                    )}
                                </div>
                                <div className="flex-grow">
                                    <SelectedTerminalViewComponent
                                        disabled={loadingAbort || isSubmitting || isProcessing || !isOnline}
                                        loading={terminalLoading}
                                        nickname={terminal?.nickname}
                                        terminal_id={terminal?.virtual_terminal_id}
                                        show={!!terminal}
                                        dull
                                        onEditClick={() => {
                                            reloadTerminals();
                                            setOpenTerminal(true);
                                        }}
                                    />
                                </div>
                                <ServerError error={errors?.server} className="mt-4" />
                            </div>
                        </Modal>
                    </>
                );
            }}
        </Formik>
    );
};

export default GiftCardModal;
