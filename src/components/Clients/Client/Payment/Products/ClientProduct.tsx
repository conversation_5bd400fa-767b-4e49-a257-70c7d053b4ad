import ViewAllButton from '@components/form/ViewAllButton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { ClientServiceProductPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EmptyData from '@partials/Error/EmptyData';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Table from '@partials/Table/PageTable';
import ClientProductListItem from './ClientProductListItem';
import { useNavigate } from 'react-router';

const ClientProduct = () => {
    const { clientId } = useClient();
    const { data, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<ClientServiceProductPaginatedResponse, Error>(
        api.clientServiceProductReceipt({ client_id: clientId, type: 'product' }),
        {
            limit: 3,
        },
    );
    const navigate = useNavigate();

    if (loading) {
        return <SectionLoading />;
    }

    return (
        <>
            <Table>
                <Table.Head>
                    <Table.ThSort
                        sort={orderBy === 'receipt.viva_receipt_id' && orderDirection}
                        onClick={() => {
                            handleOrder('receipt.viva_receipt_id');
                        }}
                        children={strings.order_id}
                    />
                    <Table.ThSort
                        sort={orderBy === 'name' && orderDirection}
                        onClick={() => {
                            handleOrder('name');
                        }}
                        children={pos_strings.product.product_name}
                    />
                    <Table.ThSort
                        sort={orderBy === 'created_at' && orderDirection}
                        onClick={() => {
                            handleOrder('created_at');
                        }}
                        children={strings.DateTime}
                    />

                    <Table.ThSort
                        sort={orderBy === 'selling_price' && orderDirection}
                        onClick={() => {
                            handleOrder('selling_price');
                        }}
                        children={strings.price}
                    />
                    <Table.ThSort
                        sort={orderBy === 'quantity' && orderDirection}
                        onClick={() => {
                            handleOrder('quantity');
                        }}
                        children={strings.qty}
                    />
                    <Table.ThSort
                        sort={orderBy === 'total' && orderDirection}
                        onClick={() => {
                            handleOrder('total');
                        }}
                        children={strings.total_price}
                    />
                    <Table.Th
                        children={strings.status}
                    />
                    <Table.Th children={strings.Actions} className="text-right" />
                </Table.Head>
                <Table.Body>
                    {data?.data.map((product) => (
                        <ClientProductListItem
                            key={product.id}
                            product={product}
                            onViewClick={() => {
                                navigate(`receipt/${product.receipt_id}/detail`);
                            }}
                        />
                    ))}
                    {data?.data.length === 0 ? (
                        <EmptyData cardHeight="h-[20vh]" />
                    ) : (
                        <tr>
                            <td colSpan={10}>{data?.data.length !== 0 && <ViewAllButton to={`/clients/${clientId}/products`} />}</td>
                        </tr>
                    )}
                </Table.Body>
            </Table>
        </>
    );
};
export default ClientProduct;
