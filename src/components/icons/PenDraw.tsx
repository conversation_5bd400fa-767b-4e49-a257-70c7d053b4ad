import * as React from 'react';
import { SVGProps } from 'react';
const PenDrawIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="1em" height="1em" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M15.608 4.09911C16.3316 3.38896 17.3045 2.99408 18.3158 3.00007C19.327 3.00606 20.2952 3.41244 21.0104 4.13112C21.7256 4.84979 22.1302 5.82285 22.1365 6.83933C22.1427 7.85582 21.7501 8.83383 21.0438 9.56134L20.6163 9.98953L20.7515 10.1254C21.3408 10.718 21.672 11.5217 21.672 12.3596C21.672 13.1976 21.3408 14.0012 20.7515 14.5938L18.4878 16.8691C18.3396 17.013 18.141 17.0926 17.935 17.0908C17.7289 17.089 17.5317 17.0059 17.386 16.8594C17.2403 16.713 17.1576 16.5148 17.1558 16.3077C17.1541 16.1005 17.2333 15.901 17.3764 15.752L19.6401 13.4767C19.9348 13.1804 20.1003 12.7786 20.1003 12.3596C20.1003 11.9406 19.9348 11.5388 19.6401 11.2425L19.5049 11.1066L10.6296 20.0292C10.5284 20.1303 10.4018 20.2018 10.2633 20.2362L3.97546 21.8162C3.83892 21.8503 3.69578 21.8469 3.56097 21.8064C3.42617 21.766 3.30465 21.6898 3.20909 21.586C3.11354 21.4823 3.04745 21.3546 3.01773 21.2164C2.98802 21.0781 2.99575 20.9344 3.04014 20.8002L5.0051 14.875C5.04342 14.7591 5.10799 14.6536 5.19373 14.5669L15.608 4.09911ZM19.9309 5.21621C19.505 4.78877 18.9278 4.5487 18.3259 4.5487C17.7241 4.5487 17.1468 4.78877 16.7209 5.21621L6.43715 15.5513L4.98309 19.9344L9.67068 18.7572L19.9309 8.44425C20.3566 8.0161 20.5958 7.43555 20.5958 6.83023C20.5958 6.2249 20.3566 5.64435 19.9309 5.21621ZM9.23525 25C7.29387 25 5.76435 24.1057 4.74729 23.2509L6.76569 22.7453C7.51679 23.1801 8.36693 23.4125 9.23368 23.42C9.87189 23.42 10.529 23.1608 11.2301 22.6979C11.9327 22.2349 12.6103 21.6124 13.2956 20.9677L13.4733 20.7986C14.0785 20.2283 14.7057 19.6342 15.3062 19.2249C15.9475 18.7857 16.7995 18.3875 17.6908 18.7335C18.2819 18.9611 18.6655 19.3813 18.9343 19.8964C19.1873 20.3799 19.3618 20.9835 19.5316 21.6424C19.6102 21.9426 19.7407 22.2333 19.8932 22.4293C19.9422 22.4983 20.0028 22.5582 20.0724 22.6062C20.1117 22.6299 20.129 22.6299 20.1337 22.6299C20.3255 22.6299 20.654 22.5004 21.124 22.178C21.4148 21.9789 21.6648 21.7799 21.9131 21.5792C22.0672 21.4559 22.2228 21.3311 22.3879 21.2079C22.8264 20.8777 23.2053 20.6564 23.4835 20.5158C23.6259 20.4432 23.7717 20.3779 23.9205 20.3199L23.952 20.3073L23.963 20.3041L23.9677 20.3025H23.9693L24.2208 21.0499L23.9708 20.3009C24.1675 20.2366 24.3814 20.2528 24.5663 20.3459C24.7511 20.4391 24.892 20.6017 24.9584 20.7986C25.0248 20.9954 25.0113 21.2106 24.9209 21.3976C24.8305 21.5845 24.6704 21.7281 24.4754 21.7972L24.4691 21.8004C24.3756 21.8384 24.2838 21.8805 24.1941 21.9268C23.9897 22.0279 23.6895 22.2017 23.3295 22.4735C23.2367 22.5414 23.1173 22.6378 22.9789 22.7484C22.6944 22.9744 22.336 23.2604 22.0075 23.4847C21.5201 23.8197 20.8426 24.2116 20.1337 24.2116C19.4483 24.2116 18.9531 23.7928 18.6466 23.3947C18.348 22.9891 18.1317 22.5285 18.0099 22.039C17.837 21.3706 17.7018 20.933 17.5446 20.6327C17.4032 20.3673 17.2774 20.2662 17.1265 20.2061C16.9976 20.1587 16.7414 20.154 16.1912 20.5316C15.6992 20.8666 15.1616 21.3738 14.528 21.9726L14.3709 22.1196C13.6933 22.7595 12.9246 23.4705 12.0931 24.0188C11.2599 24.5686 10.301 25 9.23525 25Z"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth={0.3}
        />
    </svg>
);
export default PenDrawIcon;
