import Button from '@components/form/Button';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { ClientPrescriptionResponse } from '@interface/common';
import { ClientPrescription } from '@interface/model/prescription';
import strings from '@lang/Lang';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import Modal from '@partials/MaterialModal/Modal';
import { lazy, useState } from 'react';
import useSWR from 'swr';
const ClientPrescriptionModal = lazy(() => import('./ClientPrescriptionModal'));
const ClientPrescriptionSignModal = lazy(() => import('./ClientPrescriptionSignModal'));

export interface ClientPrescriptionSelectModalProps {
    selectedPrescriptions?: ClientPrescription[];
    openModal: boolean;
    onSelect: (prescriptions: ClientPrescription[]) => void;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const ClientPrescriptionSelectModal: React.FC<ClientPrescriptionSelectModalProps> = ({
    selectedPrescriptions: preSelectedPrescriptions,
    openModal,
    setOpenModal,
    onSelect,
}) => {
    const handleModelClose = () => setOpenModal(false);
    const [openAddModal, setOpenAddModal] = useState(false);
    const [openSignModal, setOpenSignModal] = useState(false);
    const [selectedPrescriptions, setSelectedPrescriptions] = useState<ClientPrescription[]>(preSelectedPrescriptions ?? []);
    const [selectedPrescriptionsForSign, setSelectedPrescriptionsForSign] = useState<ClientPrescription>();
    const { mutate: clientMutate, clientId } = useClient();
    const { data, mutate: prescriptionMutate, isLoading } = useSWR<ClientPrescriptionResponse>(api.clientPrescriptionList(clientId));

    function addOrRemovePresc(presc: ClientPrescription) {
        const arr = [...selectedPrescriptions];
        const index = arr.findIndex((pr) => pr.id === presc.id);

        if (index !== -1) {
            arr.splice(index, 1);
            setSelectedPrescriptions(arr);
        } else {
            arr.push(presc);
            setSelectedPrescriptions(arr);
        }
    }

    return (
        <>
            <ModalSuspense>
                {openSignModal && (
                    <ClientPrescriptionSignModal
                        openModal={openSignModal}
                        setOpenModal={setOpenSignModal}
                        mutate={prescriptionMutate}
                        clientMutate={clientMutate}
                        selectedPrescription={selectedPrescriptionsForSign}
                    />
                )}
                {openAddModal && (
                    <ClientPrescriptionModal
                        openModal={openAddModal}
                        noSaveSignButton
                        setOpenModal={setOpenAddModal}
                        setOpenSignModal={setOpenSignModal}
                        setSelectedPrescription={setSelectedPrescriptionsForSign}
                        mutate={prescriptionMutate}
                    />
                )}
            </ModalSuspense>
            {!openAddModal && !openSignModal && (
                <Modal
                    open={openModal}
                    title={strings.select_prescriptions}
                    handleClose={handleModelClose}
                    buttonChildren={
                        <div className="grid w-full grid-cols-2 gap-x-6 gap-y-4 border-t p-4 dark:border-dimGray lg:grid-cols-3">
                            <CancelButton onClick={handleModelClose} children={strings.Cancel} />
                            <div className="col-span-2 row-start-2 flex justify-center lg:col-span-1 lg:col-start-2 lg:row-start-1">
                                <Button
                                    type="submit"
                                    onClick={() => {
                                        setOpenAddModal(true);
                                    }}
                                    children={strings.new_prescription}
                                />
                            </div>
                            <Button
                                type="submit"
                                className=""
                                onClick={async () => {
                                    handleModelClose();
                                    onSelect(selectedPrescriptions);
                                }}
                                children={strings.Submit}
                            />
                        </div>
                    }
                >
                    <div className="grid grid-cols-1 gap-4 p-4 pl-7 md:grid-cols-2">
                        {isLoading ? (
                            <div className="col-span-2">
                                <SectionLoading />
                            </div>
                        ) : data?.data?.filter((v) => !v.is_cancelled).length === 0 ? (
                            <p className="col-span-2 text-center">{strings.no_data}</p>
                        ) : (
                            data?.data
                                ?.filter((v) => !v.is_cancelled)
                                .map((v, i) => (
                                    <Checkbox
                                        key={i}
                                        label={v.title}
                                        checked={!!selectedPrescriptions.find((pr) => pr.id === v.id)}
                                        onChange={() => addOrRemovePresc(v)}
                                    />
                                ))
                        )}
                    </div>
                </Modal>
            )}
        </>
    );
};

export default ClientPrescriptionSelectModal;
