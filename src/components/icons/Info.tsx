import * as React from 'react';
import { SVGProps } from 'react';
const InfoIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="1em" height="1em" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M256 80C221.191 80 187.163 90.3222 158.22 109.661C129.277 129 106.718 156.488 93.3972 188.648C80.0762 220.808 76.5908 256.195 83.3818 290.336C90.1728 324.477 106.935 355.837 131.549 380.451C156.163 405.065 187.523 421.827 221.664 428.618C255.805 435.409 291.193 431.924 323.352 418.603C355.512 405.282 383 382.723 402.339 353.78C421.678 324.837 432 290.81 432 256C432 209.322 413.457 164.556 380.451 131.549C347.444 98.5428 302.678 80 256 80Z"
            stroke="currentColor"
            strokeWidth={32}
            strokeMiterlimit={10}
        />
        <path
            d="M200 202.29C200 202.29 200.84 184.79 219.57 169.72C230.68 160.77 244 158.18 256 158C266.93 157.86 276.69 159.67 282.53 162.45C292.53 167.21 312 178.83 312 203.54C312 229.54 295 241.35 275.63 254.34C256.26 267.33 251 281.43 251 296"
            stroke="currentColor"
            strokeWidth={28}
            strokeMiterlimit={10}
            strokeLinecap="round"
        />
        <path
            d="M250 368C261.046 368 270 359.046 270 348C270 336.954 261.046 328 250 328C238.954 328 230 336.954 230 348C230 359.046 238.954 368 250 368Z"
            fill="currentColor"
        />
    </svg>
);
export default InfoIcon;
