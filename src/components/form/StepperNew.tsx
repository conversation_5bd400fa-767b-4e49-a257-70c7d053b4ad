import cx from 'classix';

export interface StepperProps {
    total: number;
    current: number;
}

const StepperNew: React.FC<StepperProps> = ({ total, current }) => {
    if (total < 0 || current < 0) {
        return <></>;
    }

    const currentIndex = current - 1;

    return (
        <div className="flex items-center">
            {[...Array(total)].map((_, index) => {
                const selected = index === currentIndex;
                const done = index < currentIndex;
                const isLast = index === total - 1;
                return (
                    <div key={index} className="flex items-center">
                        <div
                            className={cx(
                                'grid size-7 select-none place-items-center rounded-full border-2 text-gray-500 dark:border-gray-500 dark:text-gray-300',
                                done ? 'border-primary bg-primary text-white dark:border-primaryLight dark:bg-primaryLight dark:text-dimGray' : '',
                                selected && 'border-primary text-primary dark:border-primaryLight dark:text-primaryLight',
                            )}
                        >
                            {done ? (
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M3.625 8.625L6.125 11.125L12.375 4.875"
                                        stroke="currentColor"
                                        strokeWidth="1.5"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            ) : (
                                <p className={cx('font-medium', selected && 'font-bold')}>{index + 1}</p>
                            )}
                        </div>
                        {!isLast && (
                            <div
                                className={cx(
                                    'w-6 border bg-red-500 dark:border-gray-500 md:w-12',
                                    done && 'border-primary dark:border-primaryLight',
                                )}
                            ></div>
                        )}
                    </div>
                );
            })}
        </div>
    );
};

export default StepperNew;
