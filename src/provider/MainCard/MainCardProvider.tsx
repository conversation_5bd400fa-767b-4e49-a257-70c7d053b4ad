import { FC, PropsWithChildren, createContext, lazy, useContext, useState } from 'react';
import MainCardGeneralNoteProvider, { useMainCardGeneralNote } from './MainCardGeneralNotesProvider';
import MainCardProcedureProvider from './MainCardProcedureProvider';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import MainCardLoCProvider, { useMainCardLoC } from './MainCardLoCProvider';
import MainCardPrescrptionProvider, { useMainCardPrescription } from './MainCardPrescriptionProvider';
import MainCardBookingProvider, { useMainCardBooking } from './MainCardBookingProvider';
const ClientMainCardAddModal = lazy(() => import('@components/Clients/Client/sections/MainCardAddModal'));

interface IContext {
    openAddModal: boolean;
    setOpenAddModal: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface CountsData {
    general_notes_unsigned_exists: boolean;
    treatments_unsigned_exists: boolean;
    letter_of_consents_unsigned_exists: boolean;
    prescriptions_unsigned_exists: boolean;
    bookings_count: number;
    procedures_count: number;
    letters_count: number;
    general_notes_count: number;
    prescriptions_count: number;
    total: number;
    receipt_count: number;
    receipt_products_count: number;
    receipt_services_count: number;
    gift_card_count: number;
}

const MainCardContext = createContext<IContext>({} as IContext);

const MainCardProvider: FC<PropsWithChildren> = ({ children }) => {
    const [openAddModal, setOpenAddModal] = useState(false);

    const { setSelectedGeneralNote, setopenGeneralNote } = useMainCardGeneralNote();
    const { setSelectedLetterOfConsent, setOpenLoC } = useMainCardLoC();
    const { setSelectedPrescription, setOpenPrescriptionEditModal } = useMainCardPrescription();
    const { setOpenBookingModal } = useMainCardBooking();

    return (
        <MainCardContext.Provider
            value={{
                openAddModal,
                setOpenAddModal,
            }}
        >
            <ModalSuspense>
                {openAddModal && (
                    <ClientMainCardAddModal
                        openModal={openAddModal}
                        key={'main_modal'}
                        handleClose={() => setOpenAddModal(false)}
                        onLoCClick={() => {
                            setSelectedLetterOfConsent(undefined);
                            setOpenAddModal(false);
                            setTimeout(() => setOpenLoC(true), 100);
                        }}
                        onGeneralNotesClick={() => {
                            setSelectedGeneralNote(undefined);
                            setOpenAddModal(false);
                            setTimeout(() => setopenGeneralNote(true), 100);
                        }}
                        onBookingRecordClick={() => {
                            setOpenAddModal(false);
                            setTimeout(() => setOpenBookingModal(true), 100);
                        }}
                        onPrescriptionClick={() => {
                            setSelectedPrescription(undefined);
                            setOpenAddModal(false);
                            setTimeout(() => setOpenPrescriptionEditModal(true), 100);
                        }}
                    />
                )}
            </ModalSuspense>
            {children}
        </MainCardContext.Provider>
    );
};

export const useMainCard = () => {
    return useContext(MainCardContext);
};

const MainCardProviderHoC: FC<PropsWithChildren> = ({ children }) => (
    <MainCardProcedureProvider>
        <MainCardGeneralNoteProvider>
            <MainCardLoCProvider>
                <MainCardPrescrptionProvider>
                    <MainCardBookingProvider>
                        <MainCardProvider children={children} />
                    </MainCardBookingProvider>
                </MainCardPrescrptionProvider>
            </MainCardLoCProvider>
        </MainCardGeneralNoteProvider>
    </MainCardProcedureProvider>
);

export default MainCardProviderHoC;
