import Card from '@components/card';
import CustomTabs from '@components/form/CustomTabs';
import Heading from '@components/heading/Heading';
import useClient from '@hooks/useClient';
import useLocalStorage from '@hooks/useLocalStorage';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import React, { FC, lazy } from 'react';

export interface PaymentMainCardProps { }

const ClientReceipt = lazy(() => import('../Payment/Receipt/ClientReceipt'));
const ClientProduct = lazy(() => import('../Payment/Products/ClientProduct'));
const ClientService = lazy(() => import('../Payment/Services/ClientService'));
const ClientGiftCard = lazy(() => import('../Payment/GiftCard/ClientGiftCard'));

const ClientPaymentMainCard: React.FC<PaymentMainCardProps> = () => {
    const { setStorageValue: setTab, storedValue: tab } = useLocalStorage<number | null>('client_payment_info_tab', 0);
    const { counts } = useClient();

    const tabs = [
        {
            text: pos_strings.product.products,
            tabCount: counts?.receipt_products_count,
            Component: <ClientProduct />,
        },
        {
            text: strings.services,
            tabCount: counts?.receipt_services_count,
            Component: <ClientService />,
        },
        {
            text: pos_strings.gift_card.gift_card,
            tabCount: counts?.gift_card_count,
            Component: <ClientGiftCard />,
        },
        {
            text: pos_strings.receipt.receipt,
            tabCount: counts?.receipt_count,
            Component: <ClientReceipt />,
        },
    ];

    return (
        <Card>
            <CustomTabs
                tabs={tabs}
                selectedIndex={tab}
                onChange={(index, mobile) => setTab(index === tab && mobile ? null : index)}
                heading={
                    <div className="flex items-center justify-between">
                        <Heading text={pos_strings.payment.payment} variant="subHeader" className="py-1.5" />
                    </div>
                }
            />
        </Card>
    );
};
export default ClientPaymentMainCard;
