import Button from '@components/form/Button';
import CustomTabs from '@components/form/CustomTabs';
import { getHHMMfromMinutes, getMinutesFromHHMM, getMinutesToHH } from '@components/form/TimePicker';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import { SettingResponse } from '@interface/common';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import { Formik, FormikErrors } from 'formik';
import { ReactNode, lazy, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import strings from '../../../lang/Lang';
import Settings from '@configs/settings';

const LazyEmail = lazy(() => import('./tabs/Email'));
const LazyPortal = lazy(() => import('./tabs/Portal'));
const LazyScheduling = lazy(() => import('./tabs/Scheduling'));
const LazySMS = lazy(() => import('./tabs/SMS'));
const LazyHoliday = lazy(() => import('./tabs/Holidays'));

export interface IBookingSettingValues {
    minimum_lead_time: string;
    maximum_lead_time: string;
    internal_booking_confirmation: string;
    internal_booking_cancellation: string;
    internal_booking_reschedule: string;
    client_email_reminder: string;
    booking_cancellation_and_modify_policy: string;
    booking_policy_link: string;
    booking_policy: string;
    booking_policy_link_selected: boolean | null;
    booking_security: boolean;
    booking_portal_text: string;
    booking_widget: string;
    booking_extras_steps_reminder: string;
    internal_booking_confirmation_practitioner: boolean;
    internal_booking_cancellation_practitioner: boolean;
    internal_booking_reschedule_practitioner: boolean;
    SMS_CLIENT_BOOKING_REMINDER_TIME?: string;
    SMS_CLIENT_BOOKING_CONFIRMATION_ON?: boolean;
    SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID?: string;
    SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID?: string;
    SMS_CLIENT_BOOKING_CANCELLATION_ON?: boolean;
    SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID?: string;
    SMS_INTERNAL_BOOKING_CONFIRMATION_ON?: boolean;
    SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID?: string;
    SMS_INTERNAL_BOOKING_CANCELLATION_ON?: boolean;
    SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID?: string;
    BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME?: string;
    BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_ON?: boolean;
    EMAIL_CLIENT_BOOKING_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_CONFIRMATION_ON?: boolean;
    EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_CANCELLATION_ON?: boolean;
    EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_RESCHEDULED_ON?: boolean;
    EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_UPDATED_ON?: boolean;
    EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_REMINDER_ON?: boolean;
    EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_PRACTITIONER_CHANGED_ON?: boolean;
    EMAIL_CLIENT_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID?: string;
    EMAIL_INTERNAL_BOOKING_CONFIRMATION_ON?: boolean;
    EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID?: string;
    EMAIL_INTERNAL_BOOKING_CANCELLATION_ON?: boolean;
    EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID?: string;
    EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_ON?: boolean;
    EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID?: string;
    EMAIL_INTERNAL_BOOKING_RESCHEDULED_ON?: boolean;
    EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID?: string;
    EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_ON?: boolean;
    EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID?: string;
    MAGNETIC_BOOKING_ENABLED?: boolean;
    server?: string;
}
const bookingsettingsRoutes = [
    '/booking-settings/scheduling',
    '/booking-settings/email',
    '/booking-settings/sms',
    '/booking-settings/portal',
    '/booking-settings/holidays',
];

const BookingSetting = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [showTabs, setShowTabs] = useState(true);
    const selectedTab = showTabs ? bookingsettingsRoutes.findIndex((v) => v === location.pathname) : null;

    function onTabChange(index: number, mobile: boolean) {
        setShowTabs(selectedTab === index && mobile ? false : true);
        navigate(bookingsettingsRoutes[index]);
    }

    const { data: settingData, mutate, error } = useSWR<SettingResponse, Error>(api.setting);
    const loading = !settingData && !error;

    if (loading) {
        return <SectionLoading />;
    }

    if (!settingData?.data && !loading) {
        return <></>;
    }

    const getValueFromKey = (key: string) => settingData?.data.find((data) => data.key === key);

    return (
        <Formik<IBookingSettingValues>
            initialValues={
                !settingData
                    ? {
                          minimum_lead_time: '12:00',
                          maximum_lead_time: '365',
                          internal_booking_confirmation: '',
                          internal_booking_cancellation: '',
                          internal_booking_reschedule: '',
                          client_email_reminder: '16:00',
                          booking_policy_link: '',
                          booking_policy: '',
                          booking_security: false,
                          booking_policy_link_selected: null,
                          booking_portal_text: '',
                          booking_cancellation_and_modify_policy: '',
                          booking_widget: '',
                          booking_extras_steps_reminder: '',
                          internal_booking_cancellation_practitioner: false,
                          internal_booking_confirmation_practitioner: false,
                          internal_booking_reschedule_practitioner: false,
                          MAGNETIC_BOOKING_ENABLED: false,
                      }
                    : {
                          minimum_lead_time: getHHMMfromMinutes(getValueFromKey('MINIMUM_LEAD_TIME')?.value || '12:00'),
                          maximum_lead_time: getValueFromKey('MAXIMUM_LEAD_TIME')?.value || '365',
                          internal_booking_confirmation: getValueFromKey('INTERNAL_BOOKING_CONFIRMATION')?.value || '',
                          internal_booking_cancellation: getValueFromKey('INTERNAL_BOOKING_CANCELLATION')?.value || '',
                          internal_booking_reschedule: getValueFromKey('INTERNAL_BOOKING_RESCHEDULE')?.value || '',
                          client_email_reminder: getValueFromKey('CLIENT_EMAIL_REMINDER')?.value
                              ? getMinutesToHH(getValueFromKey('CLIENT_EMAIL_REMINDER')?.value || '16')
                              : '',
                          booking_cancellation_and_modify_policy: getValueFromKey('BOOKING_CANCELLATION_AND_MODIFY_POLICY')?.value
                              ? getValueFromKey('BOOKING_CANCELLATION_AND_MODIFY_POLICY')?.value === 'never'
                                  ? 'never'
                                  : getMinutesToHH(getValueFromKey('BOOKING_CANCELLATION_AND_MODIFY_POLICY')?.value || '')
                              : '',
                          booking_policy_link: getValueFromKey('BOOKING_POLICY_LINK')?.value || '',
                          booking_policy: getValueFromKey('BOOKING_POLICY')?.value || '',
                          booking_policy_link_selected: getValueFromKey('BOOKING_POLICY_LINK_SELECTED')?.value === '1',
                          booking_security: getValueFromKey('BOOKING_SECURITY')?.value === '1',
                          booking_portal_text: getValueFromKey('BOOKING_PORTAL_TEXT')?.value || '',
                          booking_widget: '',
                          booking_extras_steps_reminder: getValueFromKey('BOOKING_EXTRAS_STEPS_REMINDER')?.value
                              ? getMinutesToHH(getValueFromKey('BOOKING_EXTRAS_STEPS_REMINDER')?.value || '16')
                              : '',
                          internal_booking_cancellation_practitioner: getValueFromKey('INTERNAL_BOOKING_CANCELLATION_PRACTITIONER')?.value === '1',
                          internal_booking_confirmation_practitioner: getValueFromKey('INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER')?.value === '1',
                          internal_booking_reschedule_practitioner: getValueFromKey('INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER')?.value === '1',
                          SMS_CLIENT_BOOKING_REMINDER_TIME: getValueFromKey(Settings.SMS_CLIENT_BOOKING_REMINDER_TIME)?.value,
                          SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID: getValueFromKey(Settings.SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID)?.value ?? '',
                          SMS_CLIENT_BOOKING_CONFIRMATION_ON: getValueFromKey(Settings.SMS_CLIENT_BOOKING_CONFIRMATION_ON)?.value === '1',
                          SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID:
                              getValueFromKey(Settings.SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID)?.value ?? '',
                          SMS_CLIENT_BOOKING_CANCELLATION_ON: getValueFromKey(Settings.SMS_CLIENT_BOOKING_CANCELLATION_ON)?.value === '1',
                          SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID:
                              getValueFromKey(Settings.SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID)?.value ?? '',
                          SMS_INTERNAL_BOOKING_CONFIRMATION_ON: getValueFromKey(Settings.SMS_INTERNAL_BOOKING_CONFIRMATION_ON)?.value === '1',
                          SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID:
                              getValueFromKey(Settings.SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID)?.value ?? '',
                          SMS_INTERNAL_BOOKING_CANCELLATION_ON: getValueFromKey(Settings.SMS_INTERNAL_BOOKING_CANCELLATION_ON)?.value === '1',
                          SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID:
                              getValueFromKey(Settings.SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID)?.value ?? '',
                          BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME: getValueFromKey(Settings.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME)?.value ?? '',
                          BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID:
                              getValueFromKey(Settings.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID)?.value ?? '',
                          //   EMAIL_CLIENT_BOOKING_ON: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_ON)?.value === '1',
                          EMAIL_CLIENT_BOOKING_TEMPLATE_ID: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_TEMPLATE_ID)?.value ?? '',
                          EMAIL_CLIENT_BOOKING_CONFIRMATION_ON: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_CONFIRMATION_ON)?.value === '1',
                          EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID)?.value ?? '',
                          EMAIL_CLIENT_BOOKING_CANCELLATION_ON: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_CANCELLATION_ON)?.value === '1',
                          EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID)?.value ?? '',
                          EMAIL_CLIENT_BOOKING_RESCHEDULED_ON: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_RESCHEDULED_ON)?.value === '1',
                          EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID)?.value ?? '',
                          EMAIL_CLIENT_BOOKING_UPDATED_ON: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_UPDATED_ON)?.value === '1',
                          EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID)?.value ?? '',
                          EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID: getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID)?.value ?? '',
                          EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID)?.value ?? '',
                          EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID)?.value ?? '',
                          EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID)?.value ?? '',
                          EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID)?.value ?? '',
                          EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID:
                              getValueFromKey(Settings.EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID)?.value ?? '',
                          MAGNETIC_BOOKING_ENABLED: getValueFromKey(Settings.MAGNETIC_BOOKING_ENABLED)?.value === '1',
                      }
            }
            enableReinitialize
            validate={(v) => validateBookingSettings(v, selectedTab)}
            onSubmit={async (values, { resetForm, setErrors, setSubmitting, setFieldError }) => {
                const settings = [
                    {
                        key: 'MINIMUM_LEAD_TIME',
                        value: getMinutesFromHHMM(values.minimum_lead_time),
                    },
                    { key: 'MAXIMUM_LEAD_TIME', value: values.maximum_lead_time },
                    {
                        key: 'INTERNAL_BOOKING_CONFIRMATION',
                        value: values.internal_booking_confirmation,
                    },
                    {
                        key: 'INTERNAL_BOOKING_CANCELLATION',
                        value: values.internal_booking_cancellation,
                    },
                    {
                        key: 'INTERNAL_BOOKING_RESCHEDULE',
                        value: values.internal_booking_reschedule,
                    },
                    {
                        key: 'CLIENT_EMAIL_REMINDER',
                        value: +values.client_email_reminder * 60,
                    },
                    {
                        key: 'BOOKING_CANCELLATION_AND_MODIFY_POLICY',
                        value:
                            values.booking_cancellation_and_modify_policy === 'never' ? 'never' : +values.booking_cancellation_and_modify_policy * 60,
                    },
                    { key: 'BOOKING_POLICY_LINK', value: values.booking_policy_link },
                    { key: 'BOOKING_POLICY', value: values.booking_policy },
                    {
                        key: 'BOOKING_SECURITY',
                        value: values.booking_security === true ? '1' : '0',
                    },
                    { key: 'BOOKING_PORTAL_TEXT', value: values.booking_portal_text },
                    {
                        key: 'BOOKING_POLICY_LINK_SELECTED',
                        value: values.booking_policy_link_selected,
                    },
                    {
                        key: 'BOOKING_EXTRAS_STEPS_REMINDER',
                        value: values.booking_extras_steps_reminder ? +values.booking_extras_steps_reminder * 60 : '',
                    },
                    {
                        key: 'INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER',
                        value: values.internal_booking_confirmation_practitioner === true ? '1' : '0',
                    },
                    {
                        key: 'INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER',
                        value: values.internal_booking_reschedule_practitioner === true ? '1' : '0',
                    },
                    {
                        key: 'INTERNAL_BOOKING_CANCELLATION_PRACTITIONER',
                        value: values.internal_booking_cancellation_practitioner === true ? '1' : '0',
                    },
                    { key: Settings.SMS_CLIENT_BOOKING_REMINDER_TIME, value: values.SMS_CLIENT_BOOKING_REMINDER_TIME },
                    { key: Settings.SMS_CLIENT_BOOKING_CONFIRMATION_ON, value: values.SMS_CLIENT_BOOKING_CONFIRMATION_ON ? '1' : '0' },
                    { key: Settings.SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID, value: values.SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID },
                    { key: Settings.SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID, value: values.SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID },
                    { key: Settings.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME, value: values.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME },
                    { key: Settings.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID, value: values.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID },
                    { key: Settings.SMS_CLIENT_BOOKING_CANCELLATION_ON, value: values.SMS_CLIENT_BOOKING_CANCELLATION_ON ? '1' : '0' },
                    { key: Settings.SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID, value: values.SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID },
                    { key: Settings.SMS_INTERNAL_BOOKING_CONFIRMATION_ON, value: values.SMS_INTERNAL_BOOKING_CONFIRMATION_ON ? '1' : '0' },
                    { key: Settings.SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID, value: values.SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID },
                    { key: Settings.SMS_INTERNAL_BOOKING_CANCELLATION_ON, value: values.SMS_INTERNAL_BOOKING_CANCELLATION_ON ? '1' : '0' },
                    { key: Settings.SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID, value: values.SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID },
                    { key: Settings.EMAIL_CLIENT_BOOKING_CONFIRMATION_ON, value: values.EMAIL_CLIENT_BOOKING_CONFIRMATION_ON ? '1' : '0' },
                    { key: Settings.EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID, value: values.EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID },
                    { key: Settings.EMAIL_CLIENT_BOOKING_CANCELLATION_ON, value: values.EMAIL_CLIENT_BOOKING_CANCELLATION_ON ? '1' : '0' },
                    { key: Settings.EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID, value: values.EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID },
                    { key: Settings.EMAIL_CLIENT_BOOKING_RESCHEDULED_ON, value: values.EMAIL_CLIENT_BOOKING_RESCHEDULED_ON ? '1' : '0' },
                    { key: Settings.EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID, value: values.EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID },
                    { key: Settings.EMAIL_CLIENT_BOOKING_UPDATED_ON, value: values.EMAIL_CLIENT_BOOKING_UPDATED_ON ? '1' : '0' },
                    { key: Settings.EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID, value: values.EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID },
                    { key: Settings.EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID, value: values.EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID },
                    { key: Settings.EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID, value: values.EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID },
                    { key: Settings.EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID, value: values.EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID },
                    {
                        key: Settings.EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID,
                        value: values.EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID,
                    },
                    { key: Settings.EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID, value: values.EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID },
                    {
                        key: Settings.EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID,
                        value: values.EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID,
                    },
                    { key: Settings.MAGNETIC_BOOKING_ENABLED, value: values.MAGNETIC_BOOKING_ENABLED ? '1' : '0' },
                ];
                const response = await fetch(api.settingStoreV2, {
                    method: 'POST',
                    headers: {
                        Accept: 'application/json',
                        
                        
                        'X-App-Locale': strings.getLanguage(),
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ settings }),
                });

                const data = await response.json();

                if (response.status === 401) {
                    navigate('/');
                }

                if (data.status === '1') {
                    await mutate();
                    await resetForm();
                    toast.success(data.message);
                } else {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                }
                setSubmitting(false);
            }}
        >
            {({ errors, dirty, handleSubmit, isSubmitting }) => {
                function Buttton() {
                    return (
                        <>
                            <ServerError className="mt-4" error={errors?.server} />
                            <div className="py-4">
                                <Button
                                    size="normal"
                                    loading={isSubmitting}
                                    onClick={async () => {
                                        if (!dirty) {
                                            toast.success(strings.no_data_changed);
                                            return;
                                        }
                                        await handleSubmit();
                                    }}
                                    type="submit"
                                >
                                    {strings.Submit}
                                </Button>
                            </div>
                        </>
                    );
                }
                return (
                    <div>
                        <FormikErrorFocus />
                        <div className="grid max-w-full">
                            <CustomTabs
                                tabs={[
                                    {
                                        text: strings.scheduling_policy,
                                        Component: (
                                            <>
                                                <LazyScheduling />
                                                <Buttton />
                                            </>
                                        ),
                                    },
                                    {
                                        text: strings.email_settings,
                                        Component: (
                                            <>
                                                <LazyEmail />
                                                <Buttton />
                                            </>
                                        ),
                                    },
                                    {
                                        text: strings.smsSettings,
                                        Component: (
                                            <>
                                                <LazySMS />
                                                <Buttton />
                                            </>
                                        ),
                                    },
                                    {
                                        text: strings.booking_portal,
                                        Component: (
                                            <>
                                                <LazyPortal />
                                                <Buttton />
                                            </>
                                        ),
                                    },
                                    {
                                        text: strings.holidays,
                                        Component: (
                                            <>
                                                <LazyHoliday />
                                            </>
                                        ),
                                    },
                                ]}
                                selectedIndex={selectedTab}
                                onChange={onTabChange}
                                heading={<Heading text={strings.booking_settings} />}
                                card
                            />
                        </div>
                    </div>
                );
            }}
        </Formik>
    );
};

export const BookingSettingTitleDescriptionItem = ({ title, description }: { title: String; description: String }) => {
    return (
        <div className="col-span-2">
            <h3 className="pb-1 text-lg font-medium">{title}</h3>
            <p className="text-sm text-mediumGray">{description}</p>
        </div>
    );
};

export const BookingSettingBackGroungSectionItem = ({ children, className }: { children: ReactNode; className?: string }) => {
    return <div className={`${className} rounded-md bg-purpleGray p-4 dark:bg-black/40`}>{children}</div>;
};
export const BookingSettingHeaderItem = ({ title }: { title: string }) => {
    return <p className="text-base uppercase text-primary dark:text-primaryLight">{title}</p>;
};
function validateBookingSettings(values: IBookingSettingValues, tab?: number | null): void | object | Promise<FormikErrors<IBookingSettingValues>> {
    let error: FormikErrors<IBookingSettingValues> = {};
    if (tab === 1) {
        if (values.internal_booking_confirmation && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(values.internal_booking_confirmation)) {
            error.internal_booking_confirmation = strings.please_provide_valid_email;
        }

        if (values.internal_booking_cancellation && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(values.internal_booking_cancellation)) {
            error.internal_booking_cancellation = strings.please_provide_valid_email;
        }
        if (values.internal_booking_reschedule && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(values.internal_booking_reschedule)) {
            error.internal_booking_reschedule = strings.please_provide_valid_email;
        }
    }

    if (tab === 2) {
        if (values.SMS_CLIENT_BOOKING_REMINDER_TIME?.length && !values.SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID?.length) {
            error.SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID = strings.please_select_template;
        }

        if (values.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME && !values.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID?.length) {
            error.BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID = strings.please_select_template;
        }

        if (values.SMS_CLIENT_BOOKING_CONFIRMATION_ON && !values.SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID?.length) {
            error.SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID = strings.please_select_template;
        }

        if (values.SMS_CLIENT_BOOKING_CANCELLATION_ON && !values.SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID?.length) {
            error.SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID = strings.please_select_template;
        }

        if (values.SMS_INTERNAL_BOOKING_CONFIRMATION_ON && !values.SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID?.length) {
            error.SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID = strings.please_select_template;
        }

        if (values.SMS_INTERNAL_BOOKING_CANCELLATION_ON && !values.SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID?.length) {
            error.SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID = strings.please_select_template;
        }
    }
    return error;
}

export default BookingSetting;
