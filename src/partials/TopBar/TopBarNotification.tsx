import Button from '@components/form/Button';
import IconButton from '@components/form/IconButton';
import api from '@configs/api';
import { Menu, MenuButton, MenuItem, MenuItems, Transition } from '@headlessui/react';
import useNotification from '@hooks/useNotification';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import CheckCircleIcon from '@icons/CheckCircle';
import LoadingIcon from '@icons/Loading';
import { NotificationResponse } from '@interface/common';
import strings from '@lang/Lang';
import NotificationIcon from '@partials/Icons/Notification';
import NotificationFilledIcon from '@partials/Icons/NotificationFilled';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import React from 'react';
import { useNavigate } from 'react-router';
export interface TopBarNotificationProps {}
const TopBarNotification: React.FC<TopBarNotificationProps> = () => {
    const navigate = useNavigate();
    const { renderDateTime } = useHours();
    const [isOpen, setIsOpen] = React.useState(false);
    const { markAllAsRead, notificationData, readNotification } = useNotification();

    const { data, loading, mutate } = usePaginationSWR<NotificationResponse, Error>(
        isOpen ? api.notificationList({ count: 0, is_read: 2 }) : undefined,
        {
            limit: 3,
        },
    );

    return (
        <div className="flex items-center">
            <Menu as="div" className="relative inline-block text-left">
                {({ open, close }) => (
                    <>
                        <MenuButton as={IconButton} name={!open ? strings.notifications : ''} onClick={() => setIsOpen(!open)}>
                            {open ? (
                                <NotificationFilledIcon className="text-[22px] text-primary dark:text-primaryLight/80" />
                            ) : (
                                <NotificationIcon className="text-[22px] text-primary dark:text-primaryLight/80" />
                            )}
                            {!!notificationData?.unread_notification_count && (
                                <div className="absolute left-1/2 top-0 h-4 rounded-full bg-error px-1 text-xs font-normal text-white outline outline-1 outline-white dark:outline-dimGray">
                                    {notificationData?.unread_notification_count > 9 ? '9+' : notificationData?.unread_notification_count}
                                </div>
                            )}
                        </MenuButton>
                        <MenuItems
                            className={cx(
                                'z-50 mt-1 w-72 divide-y divide-gray-200 rounded-2xl bg-white py-1.5 shadow-card outline-none dark:divide-gray-700 dark:bg-dimGray md:w-96',
                                'transition duration-200 data-[closed]:-translate-y-1 data-[closed]:opacity-0',
                            )}
                            transition
                            anchor={{ to: 'bottom end', gap: 3 }}
                            modal={false}
                        >
                            <h1 className="px-5 pb-2.5 pt-1 font-semibold">{strings.notifications}</h1>
                            <div className="divide-y px-3 dark:divide-gray-700">
                                {!loading &&
                                    (data?.data.filter((v) => v.is_read === 0).length ? (
                                        data?.data
                                            .filter((v) => v.is_read === 0)
                                            .map((notifi, index) => (
                                                <button className={`w-full py-2 ${index >= 3 ? 'hidden' : ''}`} key={index}>
                                                    <div className={`space-y-1 rounded-2xl p-2 hover:bg-primary/5 hover:dark:bg-gray-800`}>
                                                        <div className="flex items-center justify-between">
                                                            <p
                                                                className="line-clamp-2 max-w-xs break-words text-left text-base font-medium"
                                                                onClick={() => {
                                                                    close();
                                                                    setIsOpen(false);
                                                                    navigate(`/notification?notification=2`);
                                                                }}
                                                            >
                                                                {notifi.title}
                                                            </p>
                                                            <IconButton
                                                                type="button"
                                                                name={strings.mark_as_read}
                                                                className="!p-0 !text-base hover:!bg-primary/5 hover:!ring-0"
                                                                onClick={async () => {
                                                                    await readNotification(notifi.id);
                                                                    await mutate();
                                                                }}
                                                                icon={<CheckCircleIcon className="text-mediumGray" />}
                                                            />
                                                        </div>
                                                        <div
                                                            className="space-y-1"
                                                            onClick={() => {
                                                                close();
                                                                setIsOpen(false);
                                                                navigate(`/notification?notification=2`);
                                                            }}
                                                        >
                                                            <div
                                                                className="prose line-clamp-2 whitespace-pre-wrap break-words text-left text-dimGray/60 dark:prose-invert dark:text-gray-500"
                                                                dangerouslySetInnerHTML={{ __html: notifi.description ?? '' }}
                                                            />
                                                            <p className="text-left text-mediumGray">
                                                                {renderDateTime(notifi.created_at, { utc: true, isShowLocal: true })}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </button>
                                            ))
                                    ) : (
                                        <div className="flex h-36 items-center justify-center md:h-48">
                                            <p>{strings.you_re_all_up_to_date}</p>
                                        </div>
                                    ))}
                                {loading && (
                                    <div className="flex h-36 items-center justify-center px-3 text-primary md:h-48">
                                        <LoadingIcon className="h-5 w-5" />
                                    </div>
                                )}
                            </div>
                            <div className="flex justify-between px-2 pb-0.5 pt-2">
                                <MenuItem
                                    as={Button}
                                    variant="ghost"
                                    color="black"
                                    size="small"
                                    onClick={() => navigate(`/notification?notification=0`)}
                                >
                                    {strings.all_notifications}
                                </MenuItem>
                                <MenuItem
                                    as={Button}
                                    variant="ghost"
                                    color="ghost"
                                    size="small"
                                    className="!text-primary dark:!text-primaryLight"
                                    onClick={async () => {
                                        if (notificationData?.unread_notification_count) {
                                            await markAllAsRead();
                                            await mutate();
                                        }
                                    }}
                                >
                                    {strings.mark_all_read}
                                </MenuItem>
                            </div>
                        </MenuItems>
                    </>
                )}
            </Menu>
        </div>
    );
};

export default TopBarNotification;
