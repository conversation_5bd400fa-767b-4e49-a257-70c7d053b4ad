import { generateUserFullName } from '@/helper';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import useMatchMultipleRoutes from '@hooks/useMatchMultipleRoutes';
import { usePaginationSWR, usePaginationSWRResponse } from '@hooks/usePaginationSWR';
import { ClientProcedurePaginatedResponse } from '@interface/common';
import { ClientTreatment } from '@interface/model/clientTreatment';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { FC, PropsWithChildren, createContext, lazy, useContext, useState } from 'react';
import { useNavigate } from 'react-router';

const ClientProcedureSignModal = lazy(() => import('@components/Clients/Client/Procedures/ClientProcedureVerifySignModal'));
const ClientProcedureViewModal = lazy(() => import('@components/Clients/Client/Procedures/ClientProcedureViewModal'));
const ClientProcedureCopyQuestionModal = lazy(() => import('@components/Clients/Client/Procedures/ClientProcedureCopyQuestionModal'));
const ClientProcedureDeleteModal = lazy(() => import('@components/Clients/Client/Procedures/ClientProcedureDeleteModal'));
const ClientRecordsCancelModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelModal'));
const ClientRecordsCancelNoteViewModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelNoteViewModal'));

interface IContext extends usePaginationSWRResponse<ClientProcedurePaginatedResponse, Error> {
    openProcedureSignModal: boolean;
    setOpenProcedureSignModal: React.Dispatch<React.SetStateAction<boolean>>;
    selectedProcedure?: ClientTreatment;
    setSelectedProcedure: React.Dispatch<React.SetStateAction<ClientTreatment | undefined>>;
    openProcedureViewModal: boolean;
    setOpenProcedureViewModal: React.Dispatch<React.SetStateAction<boolean>>;
    procedureCopyModal: boolean;
    setProcedureCopyModal: React.Dispatch<React.SetStateAction<boolean>>;
    openProcedureDeleteModal: boolean;
    setOpenProcedureDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
    openProcedureCancelModal: boolean;
    setOpenProcedureCancelModal: React.Dispatch<React.SetStateAction<boolean>>;
    openProcedureCancelNoteViewModal: boolean;
    setOpenProcedureCancelNoteViewModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const MainCardProcedureContext = createContext<IContext | null>(null);

const MainCardProcedureProvider: FC<PropsWithChildren> = ({ children }) => {
    const { clientId, mutateCounts } = useClient();
    const navigate = useNavigate();

    const isClientPage = useMatchMultipleRoutes([
        'clients/:clientId',
        'settings/team/:userId/booking/:bookingId/client/:clientId',
        'calendar/booking/:bookingId/client/:clientId',
    ]);

    const [openProcedureSignModal, setOpenProcedureSignModal] = useState(false);
    const [selectedProcedure, setSelectedProcedure] = useState<ClientTreatment>();
    const [openProcedureViewModal, setOpenProcedureViewModal] = useState(false);
    const [procedureCopyModal, setProcedureCopyModal] = useState(false);
    const [openProcedureDeleteModal, setOpenProcedureDeleteModal] = useState(false);
    const [openProcedureCancelModal, setOpenProcedureCancelModal] = useState(false);
    const [openProcedureCancelNoteViewModal, setOpenProcedureCancelNoteViewModal] = useState(false);

    const swr = usePaginationSWR<ClientProcedurePaginatedResponse, Error>(api.clientTreatments(clientId), { limit: isClientPage ? 3 : 20 });

    const procedureMutate = async () => {
        mutateCounts();
        swr.mutate();
    };

    return (
        <MainCardProcedureContext.Provider
            value={{
                openProcedureSignModal,
                setOpenProcedureSignModal,
                selectedProcedure,
                setSelectedProcedure,
                openProcedureViewModal,
                setOpenProcedureViewModal,
                procedureCopyModal,
                setProcedureCopyModal,
                openProcedureDeleteModal,
                setOpenProcedureDeleteModal,
                openProcedureCancelModal,
                setOpenProcedureCancelModal,
                openProcedureCancelNoteViewModal,
                setOpenProcedureCancelNoteViewModal,
                ...swr,
            }}
        >
            <ModalSuspense>
                {procedureCopyModal && selectedProcedure && (
                    <ClientProcedureCopyQuestionModal
                        onDone={() => {
                            navigate(`/clients/${clientId}/procedures/${selectedProcedure.id}/edit?copy`);
                            setSelectedProcedure(undefined);
                            setProcedureCopyModal(false);
                        }}
                        openModal={procedureCopyModal}
                        setOpenModal={setProcedureCopyModal}
                    />
                )}
                {openProcedureSignModal && selectedProcedure && (
                    <ClientProcedureSignModal
                        openModal={true}
                        onClose={() => {
                            setOpenProcedureSignModal(false);
                            setSelectedProcedure(undefined);
                        }}
                        mutate={procedureMutate}
                        clientMutate={mutateCounts}
                        selectedClientTreatment={selectedProcedure}
                    />
                )}
                {openProcedureViewModal && selectedProcedure && (
                    <ClientProcedureViewModal
                        openModal={openProcedureViewModal}
                        handleClose={() => {
                            setOpenProcedureViewModal(false);
                            setSelectedProcedure(undefined);
                        }}
                        selectedClientTreatment={selectedProcedure}
                    />
                )}
                {openProcedureDeleteModal && selectedProcedure && (
                    <ClientProcedureDeleteModal
                        openModal={openProcedureDeleteModal}
                        handleCloseModal={() => {
                            setOpenProcedureDeleteModal(false);
                            setSelectedProcedure(undefined);
                        }}
                        mutate={procedureMutate}
                        selectedClientTreatment={selectedProcedure}
                    />
                )}
                {openProcedureCancelModal && selectedProcedure && (
                    <ClientRecordsCancelModal
                        openModal={openProcedureCancelModal}
                        setOpenModal={setOpenProcedureCancelModal}
                        mutate={procedureMutate}
                        url={api.clientProcedureUpdate.replace(':id', selectedProcedure?.id.toString() || '')}
                    />
                )}
                {openProcedureCancelNoteViewModal && selectedProcedure && (
                    <ClientRecordsCancelNoteViewModal
                        openModal={openProcedureCancelNoteViewModal}
                        handleModelClose={() => setOpenProcedureCancelNoteViewModal(false)}
                        cancelledNoteDetails={selectedProcedure?.cancel_note}
                        cancelNoteDate={selectedProcedure?.cancelled_at}
                        cancelByuUserName={generateUserFullName(selectedProcedure.cancelled_by)}
                    />
                )}
            </ModalSuspense>
            {children}
        </MainCardProcedureContext.Provider>
    );
};

export const useMainCardProcedure = () => {
    const context = useContext(MainCardProcedureContext);

    if (!context) {
        throw new Error('useMainCardProcedure must be used within a MainCardProcedureProvider');
    }
    return context;
};

export default MainCardProcedureProvider;
