import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import svgrPlugin from 'vite-plugin-svgr';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '');

    return {
        envPrefix: 'REACT_APP_',
        build: {
            outDir: 'build',
            sourcemap: env.VITE_ENABLE_SOURCEMAP === 'true',
            rollupOptions: {
                output: {
                    manualChunks(id) {
                        if (id.includes('node_modules')) {
                            return id.toString().split('node_modules/')[1].split('/')[0].toString();
                        }
                    },
                },
            },
        },
        server: {
            port: 3000,
            proxy: {
                '/api': {
                    target: env.VITE_API_CONFIG_URL,
                    changeOrigin: true,
                    secure: true,
                    rewrite: (path) => path.replace(/^\/api/, ''),
                },
            },
        },
        preview: {
            port: 3000,
            open: true,
        },
        resolve: {
            alias: {
                '@components': path.resolve(__dirname, './src/components'),
                '@lang': path.resolve(__dirname, './src/lang'),
                '@hooks': path.resolve(__dirname, './src/hooks'),
                '@interface': path.resolve(__dirname, './src/interfaces'),
                '@partials': path.resolve(__dirname, './src/partials'),
                '@configs': path.resolve(__dirname, './src/configs'),
                '@validations': path.resolve(__dirname, './src/validations'),
                '@provider': path.resolve(__dirname, './src/provider'),
                '@pages': path.resolve(__dirname, './src/pages'),
                '@icons': path.resolve(__dirname, './src/components/icons'),
                '@images': path.resolve(__dirname, './src/images'),
                '@': path.resolve(__dirname, './src'),
            },
        },
        plugins: [
            react(),
            svgrPlugin({
                svgrOptions: {
                    icon: true,
                },
            }),
            // MillionLint.vite({
            //     enabled: false,
            // }),
        ],
    };
});
