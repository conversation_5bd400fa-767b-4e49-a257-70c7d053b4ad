import useTheme from '@hooks/useTheme';
import { Area, CartesianGrid, ComposedChart, Line, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

export interface PieChartProps {
    data: AreaChartData[];
    labelName?: string;
}

export type AreaChartData = { name: string; value: number };

const AreaChartComponent: React.FC<PieChartProps> = ({ data, labelName }) => {
    const { isDark } = useTheme();

    return (
        <ResponsiveContainer width="100%" height={340}>
            <ComposedChart data={data} margin={{ top: 10, right: 5, left: 5, bottom: 0 }}>
                <CartesianGrid strokeDasharray="6 6" vertical={false} opacity={isDark ? 0.5 : 1} />
                <XAxis tickLine={false} dataKey="name" />
                <YAxis tickLine={false} axisLine={false} dataKey="value" />
                <Tooltip
                    cursor={{ fill: isDark ? '#FFFFFF0D' : '#0000000D' }}
                    labelClassName="dark:text-black"
                    wrapperClassName="rounded-xl border-0"
                />
                <Line
                    type="monotone"
                    dataKey="value"
                    name={labelName}
                    stroke={'#5551CE'}
                    fill={'#5551CE'}
                    strokeWidth={3}
                    dot={{ fill: '#5551CE', r: 4, strokeWidth: 0 }}
                    activeDot={{ fill: 'white', r: 6, stroke: '#5551CE', strokeWidth: 3 }}
                />
                <Area type="monotone" stroke="none" tooltipType="none" dataKey="value" fillOpacity={1} fill="url(#color)" />
                <defs>
                    <linearGradient id="color" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#5551CE80" stopOpacity={1} />
                        <stop offset="100%" stopColor="#5551CE00" stopOpacity={1} />
                    </linearGradient>
                </defs>
            </ComposedChart>
        </ResponsiveContainer>
    );
};

export default AreaChartComponent;
