import { CardProps } from '@components/card';
import Button from '@components/form/Button';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import Table from '@partials/Table/PageTable';
import { useState } from 'react';
import DashbordViewAllModal from './DashbordViewAllModal';
import { SectionLoading } from '@partials/Loadings/SectionLoading';

export interface DashbordTableComponentProps<T> extends CardProps {
    data: T[];
    headings: string[];
    renderColumn: (item: T, colIndex: number, rowIndex: number) => React.ReactNode[];
    title?: string;
    loading?: boolean;
}

const LIMIT = 6;
export function DashbordTableComponent<T>({ data, title, headings, loading, renderColumn }: DashbordTableComponentProps<T>) {
    const [open, setOpen] = useState(false);

    const noMoreData = (data?.length ?? 6) < LIMIT;

    return (
        <>
            {open && (
                <DashbordViewAllModal<T>
                    openModal={open}
                    onClose={() => setOpen(false)}
                    headings={headings}
                    data={data}
                    renderColumn={renderColumn}
                    title={title}
                />
            )}
            <div>
                {loading ? (
                    <SectionLoading />
                ) : (
                    <Table>
                        <Table.Head>
                            {headings.map((heading, index) => (
                                <Table.Td key={index}>{heading}</Table.Td>
                            ))}
                        </Table.Head>
                        <Table.Body>
                            {!data?.length && <EmptyData cardHeight="h-80" />}
                            {data?.map((item, index) => (
                                <tr key={index} className={index <= 4 ? `table-row` : 'hidden'}>
                                    {headings.map((heading, colIndex) => (
                                        <Table.Td key={colIndex}>{renderColumn(item, colIndex, index)?.at(colIndex)}</Table.Td>
                                    ))}
                                </tr>
                            ))}
                            {data?.length !== 0 && !noMoreData && (
                                <tr>
                                    <td colSpan={6}>
                                        <Button size="small" variant="ghost" className="mx-auto my-2" onClick={() => setOpen(true)}>
                                            {strings.ViewAll}
                                        </Button>
                                    </td>
                                </tr>
                            )}
                        </Table.Body>
                    </Table>
                )}
            </div>
        </>
    );
}

export default DashbordTableComponent;
