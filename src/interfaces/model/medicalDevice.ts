export interface MedicalDeviceManagement {
    id: number;
    product_name: string;
    maintenance_document?: string;
    model: string;
    serial_number: string;
    brand: string;
    supplier: string;
    is_active: number;
    created_at: string;
    supplier_agreement?: string;
    updated_at: string;
    upload_manual?: string;
    compliance_declared: number;
    maintenances_count: number;
    deleted_at: string;
}
export interface MaintenanceDocument {
    id: number;
    title: string;
    protocol_path?: string;
    created_at: string;
    updated_at: string;
}