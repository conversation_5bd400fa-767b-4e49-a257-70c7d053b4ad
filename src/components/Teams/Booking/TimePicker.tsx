import { TimeSlots, useCalendarTimeSlots } from '@components/Calendar/Classic/hooks/useCalendarTimeSlots';
import Error from '@components/form/Error';
import Label from '@components/form/Label';
import Select from '@components/form/Select';
import { Listbox, ListboxButton, ListboxOptions, Transition } from '@headlessui/react';
import DownIcon from '@partials/Icons/Down';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import dayjs from 'dayjs';
import TimePic from 'react-time-picker';

export interface TimePickerProps {
    error?: string | false;
    value?: string;
    onChange: (val: string) => void;
    disabled?: boolean;
    required?: boolean;
    label?: string;
    name?: string;
    color?: 'primary' | 'white';
}

const TimePicker: React.FC<TimePickerProps> = ({ error, value, disabled, onChange, required, label, name, color = 'primary' }) => {
    const { isTwelveHours, renderHours } = useHours();
    let slots = TimeSlots();
    const { timeSlots } = useCalendarTimeSlots({ timeSlot: slots[0] });
    const className = cx(
        color === 'primary' && 'border-grayishBlue bg-grayishBlue',
        color === 'white' && 'border-white bg-white',
        error && 'border-red-500 text-red-500 focus:text-black',
    );

    return (
        <div className="relative">
            <Label label={label} required={required} />
            <div
                className={`form-select flex items-center ${className} relative w-full rounded-lg bg-none px-3.5 py-2 pr-1 text-left placeholder:text-mediumGray focus-within:border-primary focus-within:ring-1 focus-within:ring-primary hover:border-mediumGray disabled:cursor-not-allowed disabled:text-gray-500 dark:border-darkGray dark:bg-darkGray dark:text-white dark:placeholder:text-gray-600 dark:focus-within:border-primaryLight dark:hover:border-slate-700 md:py-1`}
            >
                <TimePic
                    value={value ?? ''}
                    className={`custom-time-picker relative w-full dark:bg-darkGray ${className}`}
                    disableClock
                    disabled={disabled}
                    name={name}
                    amPmAriaLabel="AM"
                    onChange={(val) => onChange(val?.toString() ?? '')}
                    clearIcon={null}
                    hourPlaceholder="00"
                    minutePlaceholder="00"
                    format={isTwelveHours ? 'hh:mm a' : 'HH:mm'}
                />
                <Listbox value={value} onChange={onChange} as="div" className="" disabled={disabled}>
                    <ListboxButton className="p-1 md:p-2">
                        <DownIcon className="text-gray-600 dark:text-gray-500" />
                    </ListboxButton>
                    <Transition
                        as="div"
                        className="absolute left-0 top-full z-50 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-black sm:text-sm"
                        enter="transition-all duration-300"
                        enterFrom="top-[90%] opacity-0"
                        enterTo="top-full opacity-100"
                        leave="transition-all ease-out duration-75"
                        leaveFrom="top-full opacity-100"
                        leaveTo="top-[90%] opacity-0"
                    >
                        <ListboxOptions>
                            {timeSlots.map((slot, index) => {
                                const formatedTimeText = dayjs(slot).format('HH:mm');
                                return (
                                    <Select.Option key={`slot_${index}`} value={formatedTimeText}>
                                        {renderHours(slot)}
                                    </Select.Option>
                                );
                            })}
                        </ListboxOptions>
                    </Transition>
                </Listbox>
            </div>
            <Error error={error} />
        </div>
    );
};

export default TimePicker;
