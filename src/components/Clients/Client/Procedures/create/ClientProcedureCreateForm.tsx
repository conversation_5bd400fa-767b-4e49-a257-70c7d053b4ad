import CalendarSelect from '@components/Calendar/Custom/CalendarSelect';
import TipTapEditor from '@components/TipTap/TipTapEditor';
import Autocomplete from '@components/form/Autocomplete';
import Button from '@components/form/Button';
import IconButton from '@components/form/IconButton';
import Input from '@components/form/Input';
import Label from '@components/form/Label';
import Select from '@components/form/Select';
import { Treatment } from '@interface/model/treatment';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { useHours } from '@provider/TimeProvider';
import { FastField, getIn, useFormikContext } from 'formik';
import React, { lazy, useState } from 'react';
import { useParams } from 'react-router';
import useSWR from 'swr';
import useSWRImmutable from 'swr/immutable';
import api from '../../../../../configs/api';
import { getUnitKeyToValue, isHTML } from '../../../../../helper';
import useAuth from '../../../../../hooks/useAuth';
import { ClientTreatmentResponse, SettingResponse, TreatmentsResponse } from '../../../../../interfaces/common';
import strings from '../../../../../lang/Lang';
import DeleteIcon from '../../../../../partials/Icons/Delete';
import { IClientProcedureCreateValues } from './ClientProcedureCreate';
import { useFormContext } from 'react-hook-form';
const ClientPrescriptionSelectModal = lazy(() => import('../../Prescription/ClientPrescriptionSelectModal'));

export interface ClientProcedureCreateFormProps {}
const rating = Array.from({ length: 11 }, (_, i) => i);

const ClientProcedureCreateForm: React.FC<ClientProcedureCreateFormProps> = () => {
    const { user } = useAuth();
    const { clientId, clientProcedureId }: { clientId?: string; clientProcedureId?: string } = useParams();
    const { data } = useSWR<SettingResponse, Error>(api.setting);
    const { renderDate } = useHours();

    const {
        data: procedureData,
        error: procedureError,
        isValidating,
    } = useSWRImmutable<ClientTreatmentResponse, Error>(
        () => {
            if (!clientProcedureId) return null;
            return api.clientTreatment({ clientId, procedureId: clientProcedureId });
        },
        {
            revalidateOnMount: true,
            revalidateIfStale: false,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        },
    );

    const {
        register,
        setValue,
        watch,
        getValues,
        formState: { errors, touchedFields },
        handleSubmit,
        reset,
    } = useFormContext<IClientProcedureCreateValues>();

    const { data: treatmentsData } = useSWR<TreatmentsResponse, Error>(api.treatment);

    const [openPrescriptionSelectModal, setOpenPrescriptionSelectModal] = useState(false);

    const isLoading = !clientProcedureId ? false : (!procedureData?.data && !procedureError) || isValidating;
    const enableNRS = data?.data.find((setting) => setting.key === api.NRS_RATING && setting.value === '1')?.value;

    const watchedTreatments = watch('treatments');
    const watchedPrescriptions = watch('prescriptions');

    return (
        <>
            <ModalSuspense>
                {openPrescriptionSelectModal && (
                    <ClientPrescriptionSelectModal
                        openModal={openPrescriptionSelectModal}
                        setOpenModal={setOpenPrescriptionSelectModal}
                        selectedPrescriptions={getValues('prescriptions')}
                        onSelect={(prescriptions) => {
                            setValue('prescriptions', prescriptions);
                        }}
                    />
                )}
            </ModalSuspense>
            <div className="space-y-6">
                <Input
                    type="text"
                    label={strings.PROCEDURE_TITLE}
                    placeholder={strings.PROCEDURE_TITLE}
                    className="m-0"
                    error={errors.name?.message}
                    {...register('name')}
                />

                <div className={`${enableNRS && 'grid gap-2 md:grid-cols-2'}`}>
                    <CalendarSelect
                        selectedDate={getValues('date')}
                        onChange={(date) => setValue('date', date || '')}
                        inputProps={{
                            label: strings.DATE_OF_PROCEDURE,
                        }}
                        placeholder={strings.DATE_OF_PROCEDURE}
                        error={errors?.date?.message}
                    />
                    {enableNRS && (
                        <Select
                            displayValue={(val) => val}
                            onChange={(e) => {
                                // setFieldTouched('nrs_rating');
                                setValue('nrs_rating', e);
                            }}
                            placeholder={strings.Select}
                            defaultValue={getValues('nrs_rating')}
                            label={strings.nrs_rating}
                        >
                            {rating.map((item) => (
                                <Select.Option key={item} value={item}>
                                    {item}
                                </Select.Option>
                            ))}
                        </Select>
                    )}
                </div>
                {treatmentsData?.data != null ? (
                    watchedTreatments?.map((selectedTreatment, treatmentIndex) => (
                        <div className="flex gap-2" key={treatmentIndex}>
                            <div className="flex-grow">
                                <Autocomplete<Treatment>
                                    inputProps={{
                                        required: true,
                                        placeholder: strings.Template,
                                    }}
                                    value={treatmentsData?.data?.find((treatment) => treatment.id === selectedTreatment.id)}
                                    displayValue={(option) => option?.name ?? ''}
                                    filteredValues={(query) =>
                                        treatmentsData?.data.filter((country) =>
                                            country.name.toLowerCase().replace(/\s+/g, '').includes(query.toLowerCase().replace(/\s+/g, '')),
                                        ) ?? []
                                    }
                                    options={treatmentsData?.data || []}
                                    renderOption={(treatment) =>
                                        `${treatment.name} ${
                                            treatment.cost ? `(${getUnitKeyToValue(user?.company?.unit || '')}${treatment.cost})` : ''
                                        }`
                                    }
                                    error={getIn(errors, `treatments.${treatmentIndex}.id.message`)}
                                    onChange={async (treatment) => {
                                        if (!treatment) return;
                                        if (treatment.id === selectedTreatment.id) return;
                                        await setValue(`treatments.${treatmentIndex}.id`, treatment.id, { shouldValidate: true });
                                        await setValue(`treatments.${treatmentIndex}.actual_cost`, treatment.cost);
                                        const notes = !treatment.notes
                                            ? ''
                                            : isHTML(treatment.notes)
                                              ? treatment.notes
                                              : treatment.notes?.replaceAll('\n', '<br/>');

                                        const existingNotes = getValues('notes_html') || '';
                                        await setValue(`notes_html`, `${existingNotes.trim().length ? `${existingNotes}` : ''}\n${notes}`.trim());

                                        document.getElementsByName('procedure_form_editor').forEach((elem) => {
                                            const actualDiv = elem.firstChild as HTMLDivElement | undefined;
                                            actualDiv?.scrollTo({ top: actualDiv.scrollHeight, behavior: 'smooth' });
                                        });

                                        // setFieldTouched(`treatments.${treatmentIndex}.id`);
                                        // setFieldTouched(`treatments.${treatmentIndex}.actual_cost`);
                                        // setFieldTouched(`notes_html`);
                                    }}
                                />
                            </div>
                            <div className="w-4/12">
                                <Input
                                    placeholder={strings.COST}
                                    className=""
                                    type="number"
                                    {...register(`treatments.${treatmentIndex}.actual_cost`)}
                                    value={selectedTreatment.actual_cost}
                                    onChange={async (e) => {
                                        await setValue(`treatments.${treatmentIndex}.actual_cost`, e.target.value);
                                        // setFieldTouched(`treatments.${treatmentIndex}.actual_cost`);
                                    }}
                                    error={getIn(errors, `treatments.${treatmentIndex}.actual_cost.messag`)}
                                />
                            </div>
                            <div className="mt-1">
                                <IconButton
                                    onClick={() => {
                                        setValue(
                                            'treatments',
                                            (getValues('treatments') || []).filter((data, index) => {
                                                if (index === treatmentIndex) {
                                                    return false;
                                                }
                                                return true;
                                            }),
                                        );
                                    }}
                                    type="button"
                                    name={strings.Delete}
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </div>
                        </div>
                    ))
                ) : (
                    <></>
                )}
                <div className="flex flex-wrap gap-2">
                    <Button
                        size="small"
                        variant="ghost"
                        type="button"
                        onClick={() => {
                            const v = getValues('treatments') ?? [];
                            setValue('treatments', [...v, { id: '', actual_cost: '' }]);
                        }}
                    >
                        {strings.add_text_template}
                    </Button>
                    <Button
                        size="small"
                        variant="ghost"
                        type="button"
                        onClick={() => {
                            setOpenPrescriptionSelectModal(true);
                        }}
                    >
                        {strings.add_prescription}
                    </Button>
                </div>
                {watchedPrescriptions?.length !== 0 && (
                    <div className="space-y-4">
                        <Label label={strings.prescriptions} />
                        <div className="divide-y rounded-md border bg-white py-1 dark:divide-gray-700 dark:border-gray-700 dark:bg-dimGray">
                            {watchedPrescriptions?.map((presc, index) => (
                                <div key={index} className="flex items-center py-1.5 pl-4 pr-3">
                                    <div className="flex-grow">
                                        <p>{presc.title}</p>
                                        {/* <p className="text-xs">
                                            {strings.end_date}: {renderDate(presc.expire_at)}
                                        </p> */}
                                    </div>
                                    <IconButton
                                        onClick={() => {
                                            const allPresc = [...(getValues('prescriptions') ?? [])];
                                            allPresc.splice(index, 1);
                                            setValue('prescriptions', allPresc);
                                        }}
                                        type="button"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                <p className="font-semibold">{`${strings.total_cost}: ${getUnitKeyToValue(user?.company?.unit || '')}${
                    watchedTreatments?.length
                        ? watchedTreatments?.reduce((count, treatment) => count + parseFloat((treatment.actual_cost ?? '0').toString() || '0'), 0)
                        : 0
                }`}</p>
                <div className="mb-2">
                    <TipTapEditor
                        loading={isLoading}
                        onChange={async (data) => {
                            await setValue('notes_html', data);
                            // await setFieldTouched('notes_html');
                        }}
                        name="procedure_form_editor"
                        className={`md:h-72 xl:h-[593px] dark:xl:h-[520px]`}
                        data={watch('notes_html') ?? watch('notes') ?? ''}
                        error={Boolean(errors.notes_html?.message)}
                        helperText={errors.notes_html?.message}
                    />
                </div>
            </div>
        </>
    );
};

export default ClientProcedureCreateForm;
