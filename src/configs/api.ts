import { ReceiptExportType } from '@interface/model/receipt';
import strings from '../lang/Lang';
import { SubscriptionPlatform } from '@interface/model/plan';
import { SettingTypes } from '@interface/common';
import { RecordReportAPITypes } from '@pages/reports/RecordReports';
import { BookingReportAPITypes } from '@pages/reports/BookingReports';
import { condition } from '@provider/SMSMarketingProvider';
import { POSReportAPITypes } from '@pages/reports/POSReports';

// Main API URL
export const ogBaseUrl = import.meta.env.REACT_APP_APP_URL;
// PROXIED API URL
export const baseUrl = ogBaseUrl;

export const storageURL = import.meta.env.REACT_APP_STORAGE_PATH;

type IdType = string | number | undefined;

export type DateFormat = 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'DD.MM.YYYY' | 'YYYY-MM-DD' | 'YYYY.MM.DD' | 'MM.DD.YYYY';

const api = {
    storage: `${storageURL}/`,
    storageUrl(path: string) {
        return `${api.storage}${path}`;
    },
    adminRole: 'admin',
    masterAdminRole: 'master_admin',
    userRole: 'user',
    prescriber: 'prescriber',
    stripeActive: 'active',
    csrfCookie: `${baseUrl}/csrf-cookie`,
    login: `${baseUrl}/v2/login`,
    signup: `${baseUrl}/v2/register`,
    signupSetup: `${baseUrl}/v2/register/set-up`,
    otpResend: `${baseUrl}/v2/resendOtp`,
    register: `${baseUrl}/register`,
    forgot: `${baseUrl}/forgot`,
    user: `${baseUrl}/user`,
    userSingle: `${baseUrl}/user/:id`,
    dashboard_box: `${baseUrl}/dashboard/boxData`,
    dashboard_graph: `${baseUrl}/dashboard/graphData`,

    companyUpdate: `${baseUrl}/company/update`,
    companyMasterUpdate: `${baseUrl}/company/:id/update`,
    companySingle: `${baseUrl}/company/:id`,
    companyUsers: `${baseUrl}/company/:id/users`,
    companyDelete: `${baseUrl}/company/:id/delete`,
    companyClients: `${baseUrl}/company/:id/clients`,
    companyUsage: `${baseUrl}/company/usage`,
    clientsCount: `${baseUrl}/v2/company/clients-count`,
    companyPublicUsers: `${baseUrl}/company/:id/user`,
    companies: `${baseUrl}/company`,
    companiesExport: `${baseUrl}/company?excel`,

    companyClientExtraFields: `${baseUrl}/company/client_fields`,
    companyClientExtraFieldsPublic: `${baseUrl}/company/client_fields/:id/public`,
    companyClientExtraFieldStore: `${baseUrl}/company/client_fields/store`,
    companyClientExtraFieldUpdate: `${baseUrl}/company/client_fields/:id/update`,
    companyClientExtraFieldDelete: `${baseUrl}/company/client_fields/:id/delete`,

    userStore: `${baseUrl}/user/store`,
    userUpdate: `${baseUrl}/user/:id/update`,
    userDelete: `${baseUrl}/user/:id/delete`,
    userRestore: `${baseUrl}/user/:id/restore`,
    userAll: `${baseUrl}/user/all`,
    userAllFilter({ filter, show_not_verified }: { filter?: string; show_not_verified?: boolean }) {
        return `${baseUrl}/user/all?filter=${filter}${show_not_verified ? '&withUnverified=1' : ''}`;
    },
    userChangeStatus: `${baseUrl}/user/:id/change-status`,
    treatment(id?: IdType) {
        if (id) {
            return `${baseUrl}/v2/treatment?company_id=${id}`;
        }
        return `${baseUrl}/v2/treatment`;
    },
    convertSuperUser(id?: IdType) {
        return `${baseUrl}/v2/user/${id}/make-super-admin`;
    },
    treatmentStore: `${baseUrl}/treatment/store`,
    treatmentUpdate(id?: IdType) {
        return `${baseUrl}/treatment/${id}/update`;
    },
    treatmentDisable(id: IdType) {
        return `${baseUrl}/treatment/${id}/delete`;
    },
    treatmentRestore(id: IdType) {
        return `${baseUrl}/treatment/restore?treatment_id=${id}`;
    },
    clientTreatments(id?: IdType) {
        if (!id) return null;
        return `${baseUrl}/client/${id}/treatments`;
    },
    clientTreatment({ clientId, procedureId }: { clientId: IdType; procedureId: IdType }) {
        return `${baseUrl}/client/${clientId}/treatments/${procedureId}`;
    },

    template: `${baseUrl}/v2/template`,
    templateStore: `${baseUrl}/v2/template/store`,
    templateUpdate(id: IdType) {
        return `${baseUrl}/v2/template/${id}/update`;
    },
    templateDelete(id: IdType) {
        return `${baseUrl}/v2/template/${id}/delete`;
    },
    templateRestore(id: IdType) {
        return `${baseUrl}/v2/template/${id}/restore`;
    },

    getTemplates: `${baseUrl}/v2/get-templates`,
    letterOfConsent: `${baseUrl}/v2/letter_of_consent`,
    letterOfConsentPublic: `${baseUrl}/letter_of_consent/:companyId/public`,
    letterOfConsentStore: `${baseUrl}/letter_of_consent/store`,
    letterOfConsentRestore(id: IdType) {
        return `${baseUrl}/letter_of_consent/restore?letter_of_consent_id=${id}`;
    },
    letterOfConsentUpdate(id: IdType) {
        return `${baseUrl}/letter_of_consent/${id}/update`;
    },
    letterOfConsentDelete(id: IdType) {
        return `${baseUrl}/letter_of_consent/${id}/delete`;
    },
    clientLetterOfConsents(id: IdType) {
        if (!id) return;
        return `${baseUrl}/client/${id}/letter_of_consents`;
    },
    clientLetterOfConsentStore: `${baseUrl}/client_letter_of_consent/:id/store`,
    clientLetterOfConsentUpdate: `${baseUrl}/client_letter_of_consent/:id/update`,
    clientLetterOfConsentDelete: `${baseUrl}/client_letter_of_consent/:id/delete`,

    clientPrescription: (id: IdType) => `${baseUrl}/v2/client/${id}/prescription`,
    clientPrescriptionStore: (id: IdType) => `${baseUrl}/v2/client/${id}/prescription/store`,
    clientPrescriptionUpdate: (clientId: IdType, id: IdType) => `${baseUrl}/v2/client/${clientId}/prescription/${id}/update`,
    clientPrescriptionDelete: (clientId: IdType, id: IdType) => `${baseUrl}/v2/client/${clientId}/prescription/${id}/delete`,

    clientPrescriptionFileDelete: (clientId: IdType, id: number, file: number, index: number) =>
        `${baseUrl}/v2/client/${clientId}/prescription/${id}/files/${file}/delete?index=${index}`,
    clientPrescriptionList: (clientId: IdType, all?: boolean) => `${baseUrl}/v2/client/${clientId}/prescription/list${all ? '?all' : ''}`,

    supportStore: `${baseUrl}/support/store`,

    generalNoteStore: `${baseUrl}/general_note/:id/store`,
    generalNoteUpdate: `${baseUrl}/general_note/:generalNoteId/update`,
    generalNoteDelete: `${baseUrl}/general_note/:generalNoteId/delete`,
    generalNoteFileDelete(id: number, file: number, index: number) {
        return `${baseUrl}/general_note/${id}/files/${file}/delete?index=${index}`;
    },
    clientGeneralNotes: (id: IdType) => `${baseUrl}/client/${id}/general_notes`,

    clients: `${baseUrl}/v2/client`,
    clientSingle(id: IdType) {
        return `${baseUrl}/v2/client/${id}`;
    },
    clientSingleLogs({ filter, id }: { id?: string; filter?: string }) {
        if (!filter) return `${baseUrl}/client/${id}/logs`;
        return `${baseUrl}/client/${id}/logs?filter=${filter}`;
    },
    clientLogDownload({ filter, id }: { id?: string; filter?: string }) {
        if (!filter) return `${baseUrl}/client/${id}/logs/download`;
        return `${baseUrl}/client/${id}/logs/download?filter=${filter}`;
    },
    clientDownload(id: IdType, force: boolean = false) {
        return `${baseUrl}/client/${id}/download${force ? '?force=1' : ''}`;
    },
    clientStore: `${baseUrl}/client/store`,
    clientUpdate: `${baseUrl}/client/:id/update`,
    clientDelete: `${baseUrl}/client/:id/delete`,
    clientRestore: `${baseUrl}/client/:id/restore`,
    clientPublicStore: `${baseUrl}/client/store/public`,
    clientPublicCreate: `${baseUrl}/client/create/public`,
    clientAftercares(id?: string) {
        if (!id) return null;
        return `${baseUrl}/client/${id}/logs/aftercare`;
    },

    clientConsentStore: `${baseUrl}/v2/client/:id/consent/store`,
    clientConsentSendMail: `${baseUrl}/v2/client/:id/consent/sendMail`,
    clientConsentCancel: `${baseUrl}/v2/client/:id/consent/cancel`,

    clientMedia(id?: string, page?: number) {
        if (!id) return null;
        if (page) return `${baseUrl}/v2/client/${id}/media?per_page=5&page=${page}`;
        return `${baseUrl}/v2/client/${id}/media?per_page=5`;
    },
    clientMediaNoGroup(id?: string) {
        if (!id) return null;
        return `${baseUrl}/v2/client/${id}/media?per_page=5&noGroupBy=true`;
    },
    clientMediaStore: `${baseUrl}/v2/client/:id/media/store`,
    clientMediaDelete(clientId: IdType, id: IdType) {
        return `${baseUrl}/v2/client/${clientId}/media/${id}/delete`;
    },
    clientMediasDelete(clientId: IdType) {
        return `${baseUrl}/v2/client/${clientId}/media/multi-delete`;
    },
    clientProcedureStore: `${baseUrl}/v2/client_treatment/:id/store`,
    clientProcedureUpdateMobile: `${baseUrl}/v2/client_treatment/:id/update/mobile`,
    clientProcedureDeleteMobile: `${baseUrl}/v2/client_treatment/:id/delete/mobile`,
    clientProcedureUpdate: `${baseUrl}/v2/client_treatment/:id/update`,

    clientKind(id: IdType) {
        return `${baseUrl}/client/${id}/kind`;
    },
    clientKindStore(id: IdType) {
        return `${baseUrl}/client/${id}/kind/store`;
    },

    clientAfterCareTreatmentStore: `${baseUrl}/client/:id/after_care_treatment/store`,
    healthStore: `${baseUrl}/health_questionnaire/:id/store/new`,
    aestheticStore: `${baseUrl}/aesthetic_interest/:id/store/new`,
    covid19Store: `${baseUrl}/covid19/:id/store/new`,
    clientAccess: `${baseUrl}/client_access`,
    singleClientAccess: `${baseUrl}/client_access/:id`,
    singleClientsAccesses(id?: IdType) {
        if (!id) return null;
        return `${baseUrl}/v1/client/${id}/accesses`;
    },
    clientVerificationStore(id: IdType) {
        return `${baseUrl}/v1/client/${id}/verification/store`;
    },

    onlyClientAccess: `${baseUrl}/client_access/:id/access`,
    clientRecordCounts: (id: IdType) => `${baseUrl}/v2/client/${id}/record-counts`,
    // clientAccessStore: `${baseUrl}/client_access/:id/store`,
    clientAccessStore(id: IdType) {
        return `${baseUrl}/client_access/${id}/store`;
    },
    setting: `${baseUrl}/setting`,
    settingGetKey: (id: SettingTypes) => `${baseUrl}/v2/setting/get/${id}`,
    settingPublic: `${baseUrl}/setting/:id/public`,
    settingStore: `${baseUrl}/setting/store`,
    subscription: {
        plans: `${baseUrl}/v3/subscription/plans`,
        store: `${baseUrl}/v3/subscription/store`,
        intent: `${baseUrl}/v3/subscription/create`,
        expiry: `${baseUrl}/v3/subscription/expiry`,
        cancel: `${baseUrl}/v2/subscription/delete-subscription`,
        cancelReadOnly: `${baseUrl}/v2/subscription/request-to-cancel`,
        requestCancel(key: string) {
            return `${baseUrl}/v2/subscription/${key}/cancel-confirmation`;
        },
    },
    invoice: {
        list(platforms?: SubscriptionPlatform[]) {
            const params = new URLSearchParams();
            if (platforms?.length) {
                for (const [key, platform] of platforms.entries()) {
                    params.set(`platforms[${key}]`, platform);
                }
            }
            return `${baseUrl}/v3/invoice?${params.toString()}`;
        },
        pay: `${baseUrl}/v3/invoice/:id/pay`,
        pending: `${baseUrl}/v3/invoice/pending`,
        void: (id: IdType) => `${baseUrl}/v3/invoice/${id}/void`,
        download: `${baseUrl}/v3/invoice/:id/download`,
    },
    promoCode: {
        apply(code?: string) {
            return `${baseUrl}/v3/promocode/show?code_name=${code}&type=monthly`;
        },
        active: `${baseUrl}/v3/promocode/active`,
    },
    billing: {
        show: `${baseUrl}/v3/billing/show`,
        update: `${baseUrl}/v3/billing/update`,
    },
    questionnairesPublic: `${baseUrl}/questionary/public?company_id=:company_id`,
    questionnaires: `${baseUrl}/questionary`,
    questionnaire: `${baseUrl}/questionary/:id`,
    questionnaireCreate: `${baseUrl}/questionary/store`,
    questionnaireUpdate: `${baseUrl}/questionary/:id/update`,
    questionnaireDelete: `${baseUrl}/questionary/:id/delete`,
    questionnaireQuestions: `${baseUrl}/questionary/:questionary/question`,
    questionnaireQuestionCreate: `${baseUrl}/questionary/:questionary/question/store`,
    questionnaireQuestionUpdate: `${baseUrl}/questionary/:questionary/question/:id/update`,
    questionnaireQuestionDelete: `${baseUrl}/questionary/:questionary/question/:id/delete`,
    questionnaireQuestionShift: (id: IdType, qId: IdType) => `${baseUrl}/v2/questionary/${id}/question/${qId}/shift`,

    clientQuestionnaire: `${baseUrl}/client/:client/questionary`,
    clientQuestionnaireData(id: IdType) {
        return `${baseUrl}/client/${id}/questionary_data`;
    },
    clientQuestionnaireDataSingle(id: IdType, type: 'health_questionary' | 'aesthetic_insterest' | 'covid19') {
        return `${baseUrl}/v1/client/${id}/questionary/${type}`;
    },
    clientQuestionnaireDataAll(id: IdType) {
        return `${baseUrl}/v2/client/${id}/questionaries`;
    },

    questionnaireAnswerCreate: `${baseUrl}/questionary/:questionary/answer/store`,
    companyLeadUpdate: `${baseUrl}/company/lead/store`,

    invite: `${baseUrl}/invite`,
    logout: `${baseUrl}/v2/logout`,
    getSignedUrl: `${baseUrl}/get-signed-url`,

    CLIENT_ACCESS_FULL_ACCESS: 'CLIENT_ACCESS_FULL_ACCESS',
    SHOW_HEALTH_QUESTIONNAIRE: 'SHOW_HEALTH_QUESTIONNAIRE',
    SHOW_AESTHETIC_INTEREST: 'SHOW_AESTHETIC_INTEREST',
    SHOW_LETTER_OF_CONSENT: 'SHOW_LETTER_OF_CONSENT',
    SHOW_COVID_19: 'SHOW_COVID_19',
    SEND_CLIENT_WELCOME_EMAIL: 'SEND_CLIENT_WELCOME_EMAIL',
    SHOW_2FA: 'SHOW_2FA',
    BANK_ID_VERIFICATION_ENABLED: 'BANK_ID_VERIFICATION_ENABLED',
    MANDATORY_BANK_ID_REG_PORTAL: 'MANDATORY_BANK_ID_REG_PORTAL',
    MANDATORY_BANK_ID_BOOKING_PORTAL: 'MANDATORY_BANK_ID_BOOKING_PORTAL',
    NRS_RATING: 'NRS_RATING',
    SUPER_USER_MAIL_WHEN_CLIENT_REGISTER: 'SUPER_USER_MAIL_WHEN_CLIENT_REGISTER',
    REQUIRED_CONSENT: 'REQUIRED_CONSENT',
    LOC_CHECKBOX: 'LOC_CHECKBOX',
    CALENDAR_FIRST_DAY_SUNDAY: 'CALENDAR_FIRST_DAY_SUNDAY',
    IS_TWELVE_HOURS: 'IS_TWELVE_HOURS',
    DATE_FORMAT: 'DATE_FORMAT',
    CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE: 'CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE',
    LANGUAGE: 'LANGUAGE',
    CUSTOMER_LANGUAGE: 'CUSTOMER_LANGUAGE',
    categoryOrderChange(id: IdType) {
        return `${baseUrl}/v2/categories/${id}/shift`;
    },
    leadTypes: {
        win: 'win',
        lost: 'lost',
        not_decided: 'not_decided',
    },

    questionTypes1: {
        yes_no: {
            text: strings.yes_no,
            value: 'yes_no',
        },
        yes_no_textbox: {
            text: strings.yes_no_textbox,
            value: 'yes_no_textbox',
        },
        textbox: {
            text: strings.textbox,
            value: 'textbox',
        },
        file_upload: {
            text: strings.file_upload,
            value: 'file_upload',
        },
        html_editor: {
            text: 'Rich Text',
            value: 'html_editor',
        },
        // select: 'Selection',
        // multi_select: 'Multi Selection',
        // image: 'Image Drawing',
    },

    registrationPortal: {
        viewProfile: 'PORTAL_VIEW_PROFILE',
        requiredProfile: 'PORTAL_REQUIRED_PROFILE',
        viewPersonalID: 'PORTAL_VIEW_PERSONAL_ID',
        requiredPersonalID: 'PORTAL_REQUIRED_PERSONAL_ID',
        viewDateOfBirth: 'PORTAL_VIEW_DATE_OF_BIRTH',
        requiredDateOfBirth: 'PORTAL_REQUIRED_DATE_OF_BIRTH',
        viewPhone: 'PORTAL_VIEW_PHONE',
        requiredPhone: 'PORTAL_REQUIRED_PHONE',
        viewOccupation: 'PORTAL_VIEW_OCCUPATION',
        requiredOccupation: 'PORTAL_REQUIRED_OCCUPATION',
        viewStreetAddress: 'PORTAL_VIEW_STREET_ADDRESS',
        requiredStreetAddress: 'PORTAL_REQUIRED_STREET_ADDRESS',
        viewCity: 'PORTAL_VIEW_CITY',
        requiredCity: 'PORTAL_REQUIRED_CITY',
        viewZipcode: 'PORTAL_VIEW_ZIPCODE',
        requiredZipcode: 'PORTAL_REQUIRED_ZIPCODE',
        viewState: 'PORTAL_VIEW_STATE',
        requiredState: 'PORTAL_REQUIRED_STATE',
        viewCountry: 'PORTAL_VIEW_COUNTRY',
        requiredCountry: 'PORTAL_REQUIRED_COUNTRY',
        viewCpr: 'PORTAL_VIEW_CPR_ID',
        requiredCpr: 'PORTAL_REQUIRED_CPR_ID',
        viewEmail: 'PORTAL_VIEW_EMAIL',
        requiredEmail: 'PORTAL_REQUIRED_EMAIL',
    },
    clientCustomField: {
        phoneNumber: 'PHONE_NUMBER',
        dateOfBirth: 'DATE_OF_BIRTH',
        personalId: 'PERSONAL_ID',
        occupation: 'OCCUPATION',
        streetAddress: 'STREET_ADDRESS',
        city: 'CITY',
        state: 'STATE',
        country: 'COUNTRY',
        zipCode: 'ZIPCODE',
        email: 'EMAIL',
        cprId: 'CPR_ID',
    },

    category: `${baseUrl}/v2/categories`,
    categoryWithServices: `${baseUrl}/v2/categories?with_service=1`,
    categoryCreate: `${baseUrl}/v2/categories/store`,
    categoryUpdate(id: IdType) {
        return `${baseUrl}/v2/categories/${id}/update`;
    },
    categoryDelete(id: IdType) {
        return `${baseUrl}/v2/categories/${id}/active-toggle`;
    },

    service: `${baseUrl}/v2/services`,
    services: (data?: { price_only?: boolean; search?: string }) => {
        const params = new URLSearchParams();

        if (data?.search) {
            params.set('search', data.search);
        }

        if (data?.price_only) {
            params.set('price_only', 'true');
        }

        return `${baseUrl}/v2/services?${params.toString()}`;
    },
    serviceCreate: `${baseUrl}/v2/services/store`,
    serviceUpdate(id: IdType) {
        return `${baseUrl}/v2/services/${id}/update`;
    },
    serviceyDelete(id: IdType) {
        return `${baseUrl}/v2/services/${id}/destroy`;
    },
    serviceyDisable(id: IdType) {
        return `${baseUrl}/v2/services/${id}/active-toggle`;
    },
    servicePractitionersAll({ userIds }: { userIds: number[] }) {
        if (!userIds) return `${baseUrl}/v2/services`;
        const params = new URLSearchParams();
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        return `${baseUrl}/v2/services?${params.toString()}`;
    },
    categoryService(id: IdType) {
        return `${baseUrl}/v2/booking-portal/${id}/categories`;
    },
    bookingNoIdServices(id: IdType, userId?: IdType) {
        if (userId) {
            return `${baseUrl}/v2/booking-portal/${id}/services?user_id=${userId}`;
        }
        return `${baseUrl}/v2/booking-portal/${id}/services`;
    },
    bookingServices(id: IdType, serviceId: IdType) {
        if (!serviceId) return null;
        return `${baseUrl}/v2/booking-portal/${id}/services/${serviceId}`;
    },
    companyInfo(id: IdType) {
        return `${baseUrl}/v2/booking-portal/${id}/company`;
    },
    bookingCategories(id: IdType, userId?: IdType, categoryId?: IdType) {
        const params = new URLSearchParams();
        if (userId) {
            params.set(`user_id`, `${userId}`);
        }
        if (categoryId) {
            params.set(`category_id`, `${categoryId}`);
        }
        return `${baseUrl}/v2/booking-portal/${id}/categories?${params.toString()}`;
    },
    bookingPractitioners(id: IdType, serviceId: IdType) {
        if (!serviceId || !id) return null;
        return `${baseUrl}/v2/booking-portal/${id}/practitioner?service_id=${serviceId}`;
    },
    bookingPractitionersAll({ serviceIds }: { serviceIds: number[] }) {
        if (!serviceIds) return `${baseUrl}/v2/practitioner`;
        const params = new URLSearchParams();
        serviceIds.forEach((element, index) => {
            params.set(`service_ids[${index}]`, `${element}`);
        });
        return `${baseUrl}/v2/practitioner?${params.toString()}`;
    },
    bookingPractitionersServiceId({ serviceId }: { serviceId: IdType }) {
        if (!serviceId) return null;
        return `${baseUrl}/v2/practitioner?service_id=${serviceId}`;
    },
    bookingPractitionersWithSlot({ userIds = [], id, serviceId, datetime }: { userIds: number[]; id: IdType; serviceId: IdType; datetime?: IdType }) {
        if (!serviceId || !id || !datetime) return null;
        const params = new URLSearchParams();
        params.set('service_id', `${serviceId}`);
        if (datetime) {
            params.set('available_at', `${datetime}`);
        }
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        return `${baseUrl}/v2/booking-portal/${id}/practitioner?${params.toString()}`;
    },
    bookingMonthDisabledDays(id: IdType, serviceId: IdType, startDate: IdType, endDate: IdType, userIds: number[]) {
        if (!serviceId || !id || !startDate || !endDate) return null;
        const params = new URLSearchParams();
        params.set(`start_date`, `${startDate}`);
        params.set(`end_date`, `${endDate}`);
        params.set(`service_id`, `${serviceId}`);
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        return `${baseUrl}/v2/booking-portal/${id}/available-days?${params.toString()}`;
    },
    bookingAvailableSlots(id: IdType, serviceId: IdType, startDate: IdType, endDate: IdType, { userIds = [] as number[] }) {
        if (!serviceId || !id || !startDate || !endDate) return null;
        const params = new URLSearchParams();
        params.set('start_date', `${startDate}`);
        params.set('end_date', `${endDate}`);
        params.set('service_id', `${serviceId}`);
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });

        return `${baseUrl}/v2/booking-portal/${id}/available-slots?${params.toString()}`;
    },
    dateFormat: 'YYYY-MM-DD',
    dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
    dateFormats: ['DD/MM/YYYY', 'MM/DD/YYYY', 'DD.MM.YYYY', 'YYYY-MM-DD', 'YYYY.MM.DD', 'MM.DD.YYYY'],

    userServiceList(id?: string) {
        return `${baseUrl}/v2/user/services?user_id=${id}`;
    },

    userServicesDelete(id: IdType) {
        return `${baseUrl}/v2/user/services/${id}/delete`;
    },
    userServicesAdd: `${baseUrl}/v2/user/services/add`,

    userBookingListItem({ userId, show_upcoming_first = '0' }: { userId?: string; show_upcoming_first?: string }) {
        return `${baseUrl}/v2/user/bookings?user_id=${userId}&receipt=1&client_receipt=1&show_upcoming_first=${show_upcoming_first}`;
    },
    userBookingList({
        serviceIds,
        start_at,
        end_at,
        userIds,
        show_upcoming_first,
        show_receipt,
        show_note,
    }: {
        serviceIds: number[];
        start_at: string;
        end_at: string;
        userIds: number[];
        show_upcoming_first?: string;
        show_receipt?: boolean;
        show_note?: string;
    }) {
        if (!(serviceIds && start_at && end_at && userIds)) {
            return null;
        }
        const params = new URLSearchParams();
        params.set('start_at', start_at);
        params.set('end_at', end_at);
        params.set('show_upcoming_first', show_upcoming_first || '0');
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        serviceIds.forEach((element, index) => {
            params.set(`service_ids[${index}]`, `${element}`);
        });
        if (show_receipt) {
            params.set('receipt', '1');
            params.set('client_receipt', '1');
        }
        if (show_note) {
            params.set('show_note', '1');
        }
        return `${baseUrl}/v2/user/bookings?${params.toString()}`;
    },
    boookingUnAvailableDays({ start_at, end_at, userIds }: { start_at: string; end_at: string; userIds?: number[] }) {
        const params = new URLSearchParams();
        params.set('start_at', start_at);
        params.set('end_at', end_at);
        userIds?.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        return `${baseUrl}/v2/unavailable-days?${params.toString()}`;
    },
    bookingPortalSubmit(id: IdType) {
        return `${baseUrl}/v2/booking-portal/${id}/bookings/create`;
    },
    bookingOTPVerify(id: IdType) {
        return `${baseUrl}/v2/booking-portal/${id}/bookings/verify-otp`;
    },
    bookingOTPResend(id: IdType) {
        return `${baseUrl}/v2/booking-portal/${id}/bookings/resend-otp`;
    },
    settingStoreV2: `${baseUrl}/v2/setting/store-array`,
    companyUnavailableTime(id?: number) {
        return `${baseUrl}/v2/unavailable-time?user_id=${id}`;
    },
    companyUnavailableTimeAdd: `${baseUrl}/v2/unavailable-time/update`,
    userTimeSlotsUpdate: `${baseUrl}/v2/user-time-slots/update`,
    userTimeSlotsToggle: `${baseUrl}/v2/user-time-slots/toggle-time`,
    companyUnavailableTimeEdit(id: IdType) {
        return `${baseUrl}/v2/unavailable-time/${id}/edit`;
    },
    companyUnavailableTimeDelete(id: IdType) {
        return `${baseUrl}/v2/unavailable-time/${id}/delete`;
    },
    companyBusinessTimeAdd: `${baseUrl}/v2/business-hours/update`,
    companyBusinessTimeListWithBreaks: `${baseUrl}/v2/business-hours?with_breaks=1`,
    companyBusinessTimeList: `${baseUrl}/v2/business-hours`,
    importSampleData: `${baseUrl}/v2/get-excel-data`,
    importClients: `${baseUrl}/v2/import-clients`,
    importBooking: `${baseUrl}/v2/import-bookings`,
    bankIDVerificationURL(token?: IdType) {
        return `bankid:///?autostarttoken=${token}&redirect=null`;
    },
    userBookingServiceList(id?: string) {
        return `${baseUrl}/v2/user/services?user_id=${id}`;
    },
    userBookingAdd: `${baseUrl}/v2/user/bookings/create`,
    userPrivateTimeAdd: `${baseUrl}/v2/private-slot/create`,
    userPrivateTimeEdit(bookingId: IdType) {
        return `${baseUrl}/v2/private-slot/${bookingId}/edit`;
    },
    UserbookingMonthDisabledDays(id: IdType, userId: IdType, startDate: IdType, endDate: IdType) {
        if (!userId || !id || !startDate || !endDate) return null;
        return `${baseUrl}/v2/booking-portal/${id}/available-days?start_date=${startDate}&end_date=${endDate}&user_ids=${userId}`;
    },
    bookingSingleDetail(id: IdType, clientKey?: string) {
        if (clientKey) {
            return `${baseUrl}/v2/user/bookings/${id}/detail?client_key=${clientKey}`;
        }
        return `${baseUrl}/v2/user/bookings/${id}/detail`;
    },
    bookingDetailCancel(id: IdType) {
        return `${baseUrl}/v2/user/bookings/${id}/cancel`;
    },
    clientBookingListItem(clientId?: IdType) {
        return `${baseUrl}/v2/user/bookings?client_id=${clientId}&receipt=1&client_receipt=1`;
    },
    bookingSendEmail(userId: IdType) {
        return `${baseUrl}/v2/user/bookings/${userId}/send-booking`;
    },
    bookingEdit(bookingId: IdType) {
        return `${baseUrl}/v2/user/bookings/${bookingId}/edit`;
    },
    bookingNoShowComment(bookingId: IdType) {
        return `${baseUrl}/v2/user/bookings/${bookingId}/no-show-comment`;
    },
    bookingGetKey({ companyId, clientId, key, status }: { companyId: IdType; key?: string | null; clientId?: string; status?: boolean }) {
        if (!key) return null;
        const params = new URLSearchParams();
        if (clientId) {
            params.set('client_key', `${clientId}`);
        }
        if (status) {
            params.set('status', `${status}`);
        }
        return `${baseUrl}/v2/booking-portal/${companyId}/bookings/${key}/get-key-booking?${params.toString()}`;
    },
    bookingEmailOtpResend(companyId: IdType, key?: string) {
        return `${baseUrl}/v2/booking-portal/${companyId}/bookings/${key}/send-key-otp`;
    },
    bookingVerifyKeyOtpSend(companyId: IdType, key?: string) {
        return `${baseUrl}/v2/booking-portal/${companyId}/bookings/${key}/verify-key-otp`;
    },
    bookingCancel(companyId: IdType, bookingId: IdType) {
        return `${baseUrl}/v2/booking-portal/${companyId}/bookings/${bookingId}/cancel`;
    },
    bookingReschedule(companyId: IdType, bookingId: IdType) {
        return `${baseUrl}/v2/booking-portal/${companyId}/bookings/${bookingId}/reschedule`;
    },
    bookingUnavailableDays({ userId, startDate, endDate, serviceId }: { userId: IdType; startDate: IdType; endDate: IdType; serviceId: IdType }) {
        if (!serviceId || !userId) return null;
        return `${baseUrl}/v2/practitioner/${userId}/get-unavailable-days?start_date=${startDate}&end_date=${endDate}&service_id=${serviceId}`;
    },
    practitionerAvailableSlots({ serviceId, userId, startDate, endDate }: { serviceId: IdType; userId: IdType; startDate: IdType; endDate: IdType }) {
        return `${baseUrl}/v2/practitioner/${userId}/get-available-slots?start_date=${startDate}&end_date=${endDate}&service_id=${serviceId}`;
    },
    scheduleTemplates() {
        return `${baseUrl}/v2/schedule-templates`;
    },
    scheduleTemplatesSave() {
        return `${baseUrl}/v2/schedule-templates/store`;
    },
    scheduleTemplatesApply(id: IdType) {
        return `${baseUrl}/v2/schedule-templates/${id}/apply`;
    },
    scheduleToggleSlots() {
        return `${baseUrl}/v2/user-time-slots/toggle-slots`;
    },
    pasteSlots: `${baseUrl}/v2/user-time-slots/paste-slots`,
    userTimeSlots({
        userId,
        startDate,
        endDate,
        types,
        show_upcoming_first,
    }: {
        userId: number[];
        startDate: IdType;
        endDate: IdType;
        types: string[];
        show_upcoming_first?: string;
    }) {
        const params = new URLSearchParams();
        params.set('start_at', `${startDate}`);
        params.set('end_at', `${endDate}`);
        params.set('show_upcoming_first', show_upcoming_first || '0');
        if (userId.length === 1) {
            params.set(`user_id`, `${userId.at(0)}`);
        } else {
            userId.forEach((element, index) => {
                params.set(`user_ids[${index}]`, `${element}`);
            });
        }
        types.forEach((element, index) => {
            params.set(`types[${index}]`, `${element}`);
        });
        return `${baseUrl}/v2/user-time-slots?${params.toString()}`;
    },
    singleServiceAccesses(id?: IdType) {
        if (!id) return null;
        return `${baseUrl}/v2/user/services/${id}/attached-users`;
    },
    serviceAccessStore(id: IdType) {
        return `${baseUrl}/v2/user/services/${id}/toggle-user-attachment`;
    },

    serviceWithoutCategories: `${baseUrl}/v2/services?only_without_category=1`,

    companyWorkingHoursTimeAdd: `${baseUrl}/v2/user-time-slots/update-schedule`,
    bookingFindAvailableSlots(id: IdType, startDate: IdType, serviceId: IdType, { userIds = [] as number[] }) {
        const params = new URLSearchParams();
        params.set('service_id', `${serviceId}`);
        params.set('start_date', `${startDate}`);
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });

        return `${baseUrl}/v2/booking-portal/${id}/find-available-slot?${params.toString()}`;
    },
    clientMerge: `${baseUrl}/v2/client/merge`,
    scheduleClear: `${baseUrl}/v2/user-time-slots/clear-schedule`,
    companyPrivateTimeDelete(id: IdType) {
        return `${baseUrl}/v2/user-time-slots/${id}/delete`;
    },
    createExistingBooking: `${baseUrl}/v2/user/bookings/create-on-existing`,
    editExistingBooking(id: IdType) {
        return `${baseUrl}/v2/user/bookings/${id}/edit-on-existing`;
    },
    clientQuestionarySign(clientId: IdType, questionaryId: IdType) {
        return `${baseUrl}/v2/client/${clientId}/questionaries/${questionaryId}/update`;
    },
    userDashboardGraph(startDate: IdType, endDate: IdType, userIds: number[], filter: string) {
        const params = new URLSearchParams();

        params.set('start_date', `${startDate}`);
        params.set('end_date', `${endDate}`);
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        params.set('filter', filter);
        return `${baseUrl}/v2/dashboard/users/graphData?${params.toString()}`;
    },
    userDashboardClient(startDate?: IdType, endDate?: IdType, userIds?: IdType[], filter?: string) {
        const params = new URLSearchParams();

        params.set('start_date', `${startDate}`);
        params.set('end_date', `${endDate}`);
        userIds?.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        params.set('filter', filter || '');
        return `${baseUrl}/v2/dashboard/clients-analytics?${params.toString()}`;
    },
    userDashboardService(startDate: IdType, endDate: IdType, userIds: IdType[], filter: string) {
        const params = new URLSearchParams();

        params.set('start_date', `${startDate}`);
        params.set('end_date', `${endDate}`);
        userIds.forEach((element, index) => {
            params.set(`user_ids[${index}]`, `${element}`);
        });
        params.set('filter', filter);
        return `${baseUrl}/v2/dashboard/services-analytics?${params.toString()}`;
    },
    clientDuplicatePersonalId: `${baseUrl}/v2/client/get-pop-up-data`,
    timeZone: `${baseUrl}/v2/timezones`,
    bankId: {
        start: `${baseUrl}/v2/bank-id/start-order`,
        qr: `${baseUrl}/v2/bank-id/generate-qr`,
        check(order_ref: IdType) {
            return `${baseUrl}/v2/bank-id/collect-order?order_ref=${order_ref}`;
        },
    },
    userBankId: {
        start: `${baseUrl}/v2/user/bank-id-verification/start-order`,
        qr: `${baseUrl}/v2/user/bank-id-verification/generate-qr`,
        check(order_ref: IdType) {
            return `${baseUrl}/v2/user/bank-id-verification/collect-order?order_ref=${order_ref}`;
        },
    },
    bookingQuestionaryWithLoc(companyId: IdType, bookingId: IdType) {
        return `${baseUrl}/v2/booking-portal/${companyId}/bookings/${bookingId}/add-extra-data`;
    },
    connectToStripe: `${baseUrl}/v2/user/connect-to-stripe`,
    getQuestionaryWithLoc(company_id: IdType, queLocId: IdType) {
        return `${baseUrl}/v2/customer-portal/${company_id}/after-care-info/${queLocId}`;
    },
    questionaryAndLocFill(company_id: IdType, queLocId: IdType) {
        return `${baseUrl}/v2/customer-portal/${company_id}/after-care-info/${queLocId}/customer-fill`;
    },
    templates: {
        email: {
            list: `${baseUrl}/v2/email/templates`,
        },
    },
    sms: {
        pay: `${baseUrl}/v2/sms/pay`,
        autopay: {
            setup: `${baseUrl}/v2/sms/auto-pay-setup`,
            cancel: `${baseUrl}/v2/sms/auto-pay-cancel`,
        },
        myCredits: `${baseUrl}/v2/sms/my-credits`,
        invoicePay: (invoiceId: IdType) => `${baseUrl}/v2/sms/invoice/${invoiceId}/pay`,
        invoiceVoid: (invoiceId: IdType) => `${baseUrl}/v2/sms/invoice/${invoiceId}/void`,
        invoices: `${baseUrl}/v2/sms/invoices?sms_quantity=1`,
        priceList: `${baseUrl}/v2/sms/price-list`,
        verification: {
            sendOTP: `${baseUrl}/v2/sms/verification/send-otp`,
            verifyOTP: `${baseUrl}/v2/sms/verification/verify-otp`,
        },
        templates: {
            list: `${baseUrl}/v2/sms/templates`,
            activeList: `${baseUrl}/v2/sms/templates?filter=is_active&filter_value=1`,
            store: `${baseUrl}/v2/sms/templates/store`,
            update: (id: IdType) => `${baseUrl}/v2/sms/templates/${id}/update`,
            delete: (id: IdType) => `${baseUrl}/v2/sms/templates/${id}/delete`,
            toggle: (id: IdType) => `${baseUrl}/v2/sms/templates/${id}/toggle`,
        },
        client: {
            send: (id: IdType) => `${baseUrl}/v2/client/${id}/sms/send`,
            getCreditUsage: (id: IdType, questionners: boolean = false, hasVideoCall: boolean = false) => {
                const params = new URLSearchParams();
                if (questionners) params.set('has-questionners', '1');
                if (hasVideoCall) params.set('has-video-call', '1');

                return `${baseUrl}/v2/client/${id}/sms/get-credit-usage?${params.toString()}`;
            },
        },
        calendar: {
            weekly: `${baseUrl}/v2/sms/calendar/send-to-clients`,
        },
        marketing: {
            list: `${baseUrl}/v3/campaign/sms`,
            store: `${baseUrl}/v3/campaign/sms/store`,
            listFilter(message: string, filterValue: condition[], service: number[]) {
                const params = new URLSearchParams();
                params.set('content', message);
                params.set(`client_filter[${filterValue.length + 1}][type]`, 'has_mobile_number');
                params.set(`client_filter[${filterValue.length + 1}][key]`, 'day');
                params.set(`client_filter[${filterValue.length + 1}][value]`, '1');
                filterValue.forEach((item, index) => {
                    if (item.type === 'booking') {
                        params.set(`client_filter[${index}][type]`, 'booking');
                        params.set(`client_filter[${index}][key]`, item?.key?.endsWith('s') ? item.key.slice(0, -1) : item.key);
                        params.set(`client_filter[${index}][value][date]`, item.value);
                        service.forEach((element, ind) => {
                            params.set(`client_filter[${index}][value][services][${ind}]`, element.toString());
                        });
                    } else {
                        params.set(`client_filter[${index}][type]`, item?.type || '');
                        params.set(`client_filter[${index}][key]`, item?.key?.endsWith('s') ? item.key.slice(0, -1) : item.key);
                        params.set(`client_filter[${index}][value]`, item.value);
                    }
                });
                return `${baseUrl}/v3/campaign/sms/credit/required?${params.toString()}`;
            },
            clientListCount(isCount: boolean, filterValue: condition[], service: number[]) {
                const params = new URLSearchParams();
                if (isCount) {
                    params.set('count', '1');
                }
                filterValue.forEach((item, index) => {
                    if (item.type === 'booking') {
                        params.set(`client_filter[${index}][type]`, 'booking');
                        params.set(`client_filter[${index}][key]`, item?.key?.endsWith('s') ? item.key.slice(0, -1) : item.key);
                        params.set(`client_filter[${index}][value][date]`, item.value);
                        service.forEach((element, ind) => {
                            params.set(`client_filter[${index}][value][services][${ind}]`, element.toString());
                        });
                    } else {
                        params.set(`client_filter[${index}][type]`, item?.type || '');
                        params.set(`client_filter[${index}][key]`, item?.key?.endsWith('s') ? item.key.slice(0, -1) : item.key);
                        params.set(`client_filter[${index}][value]`, item.value);
                    }
                });
                params.set(`client_filter[${filterValue.length}][type]`, 'has_mobile_number');
                params.set(`client_filter[${filterValue.length}][key]`, 'day');
                params.set(`client_filter[${filterValue.length}][value]`, '1');
                return `${baseUrl}/v3/clients/filter?${params.toString()}`;
            },
            sendSMSClientList: (id?: IdType) => `${baseUrl}/v3/campaign/sms/${id}/list`,
        },
    },
    reportError: `${baseUrl}/v2/report/error`,
    prescriberDoctorList: `${baseUrl}/v3/doctor-portal/company/list-doctors`,
    prescriptionList(company_id?: string) {
        if (company_id) {
            return `${baseUrl}/v3/doctor-portal/prescription/list?company_id=${company_id}`;
        }
        return `${baseUrl}/v3/doctor-portal/prescription/list`;
    },
    prescriptionSign(id: IdType) {
        return `${baseUrl}/v3/doctor-portal/prescription/${id}/sign`;
    },
    prescriptionSendRequest: `${baseUrl}/v3/doctor-portal/user/send-invite`,
    meridiqBilling: `${baseUrl}/v3/doctor-portal/doctor/billing`,
    clinicBilling(start_date?: string, end_date?: string) {
        return `${baseUrl}/v3/doctor-portal/company/list-billing?start_date=${start_date}&end_date=${end_date}`;
    },
    updateClinicBilling(billing_id?: IdType, status?: string) {
        return `${baseUrl}/v3/doctor-portal/company/${billing_id}/update-billing?status=${status}`;
    },
    meridiqBillingReceipt(id: IdType) {
        return `${baseUrl}/v3/doctor-portal/doctor/${id}/receipt-bill`;
    },
    clinicList: `${baseUrl}/v3/doctor-portal/company/list`,
    clinicDetail(clinic_id?: IdType) {
        return `${baseUrl}/v3/doctor-portal/company/${clinic_id}/view`;
    },
    clinicServiceStop(id?: IdType) {
        return `${baseUrl}/v3/doctor-portal/company/${id}/stop-service`;
    },
    clinicFeesUpdate(id?: IdType) {
        return `${baseUrl}/v3/doctor-portal/company/${id}/edit-fees`;
    },
    clinicReportList(id?: IdType) {
        return `${baseUrl}/v3/doctor-portal/company/${id}/list-report`;
    },
    clinicReportDownload(id: IdType) {
        return `${baseUrl}/v3/doctor-portal/company/${id}/download-report`;
    },
    creditcardList: ``,
    doctorInvitation(companyId?: IdType) {
        return `${baseUrl}/v3/doctor-portal/user/${companyId}/accept-invite`;
    },
    creditCard: {
        list: `${baseUrl}/v2/payment-methods`,
        create: `${baseUrl}/v2/payment-methods/create`,
        update: (id: IdType) => `${baseUrl}/v2/payment-methods/${id}/update`,
        delete: (id: IdType) => `${baseUrl}/v2/payment-methods/${id}/delete`,
    },
    prescriptionDeleteInvite(connectionId?: IdType) {
        return `${baseUrl}/v3/doctor-portal/user/${connectionId}/delete-invite`;
    },
    platFormList: `${baseUrl}/v3/platforms`,
    prescriptionRegister: `${baseUrl}/v3/doctor-portal/register`,
    prescriptionOtp: `${baseUrl}/v3/doctor-portal/user/send-otp`,
    otpVerification: `${baseUrl}/v3/doctor-portal/user/verification-otp`,

    // POS APIs
    pos: {
        vivaWallet: {
            connect: `${baseUrl}/v3/point-of-sale/account`,
        },

        category: {
            list: `${baseUrl}/v3/point-of-sale/categories`,
            store: `${baseUrl}/v3/point-of-sale/categories/store`,
            update: (id: IdType) => `${baseUrl}/v3/point-of-sale/categories/${id}/update`,
            delete: (id: IdType) => `${baseUrl}/v3/point-of-sale/categories/${id}/active-toggle`,
            shift: (id: IdType) => `${baseUrl}/v3/point-of-sale/categories/${id}/shift`,
        },

        product: {
            list: (data?: { search?: string; client_id?: string }) => {
                const params = new URLSearchParams();

                if (data?.search) {
                    params.set('search', data?.search);
                }
                if (data?.client_id) {
                    params.set('client_id', data?.client_id);
                }

                return `${baseUrl}/v3/point-of-sale/products?${params.toString()}`;
            },
            store: `${baseUrl}/v3/point-of-sale/products/store`,
            update: (id: IdType) => `${baseUrl}/v3/point-of-sale/products/${id}/update`,
            delete: (id: IdType) => `${baseUrl}/v3/point-of-sale/products/${id}/active-toggle`,
            export: `${baseUrl}/v3/point-of-sale/products/export`,
        },

        giftCard: {
            list: (data?: { client_id?: string }) => {
                const params = new URLSearchParams();

                if (data?.client_id) {
                    params.set('client_id', data?.client_id);
                }
                return `${baseUrl}/v3/point-of-sale/gift-cards?${params.toString()}`;
            },
            store: `${baseUrl}/v3/point-of-sale/gift-cards/store`,
            update: (id: IdType) => `${baseUrl}/v3/point-of-sale/gift-cards/${id}/update`,
            code: (id: IdType) => `${baseUrl}/v3/point-of-sale/gift-cards/${id}`,
            export: `${baseUrl}/v3/point-of-sale/gift-cards/export`,
        },

        terminal: {
            list: `${baseUrl}/v3/point-of-sale/terminals`,
            update: (id: IdType) => `${baseUrl}/v3/point-of-sale/terminals/${id}/update`,
        },

        payment: {
            store: `${baseUrl}/v3/point-of-sale/payment/store`,
            abort: (receiptId: IdType) => `${baseUrl}/v3/point-of-sale/payment/${receiptId}/abort`,
            retry: (receiptId: IdType) => `${baseUrl}/v3/point-of-sale/payment/${receiptId}/retry`,
        },

        refund: {
            store: (receiptId: IdType) => `${baseUrl}/v3/point-of-sale/refund/${receiptId}/store`,
            abort: (refundId: IdType) => `${baseUrl}/v3/point-of-sale/refund/${refundId}/abort`,
            retry: (refundId: IdType) => `${baseUrl}/v3/point-of-sale/refund/${refundId}/retry`,
            export: (refundId: IdType, data: { type: ReceiptExportType }) => {
                const params = new URLSearchParams();

                if (data?.type) {
                    params.set('type', data.type);
                }

                return `${baseUrl}/v3/point-of-sale/refund/${refundId}/export?${params.toString()}`;
            },
            show: (refundId: IdType, data?: { polling?: boolean; gift_card?: boolean; receipt?: boolean; items?: boolean }) => {
                const params = new URLSearchParams();

                if (data?.polling) {
                    params.set('poll', 'true');
                }

                if (data?.receipt) {
                    params.set('receipt', 'true');
                }

                if (data?.gift_card) {
                    params.set('gift_card', 'true');
                }

                if (data?.items) {
                    params.set('items', 'true');
                }

                return `${baseUrl}/v3/point-of-sale/refund/${refundId}?${params.toString()}`;
            },
        },

        receipt: {
            list: (data?: { start_date?: string; end_date?: string; client_id?: string }) => {
                const params = new URLSearchParams();

                if (data?.start_date) {
                    params.set('start_date', data.start_date);
                }

                if (data?.end_date) {
                    params.set('end_date', data.end_date);
                }
                if (data?.client_id) {
                    params.set('client_id', data.client_id);
                }
                return `${baseUrl}/v3/point-of-sale/receipts?${params.toString()}`;
            },
            show: (
                id: IdType,
                data?: {
                    relatable?: boolean;
                    terminal?: boolean;
                    polling?: boolean;
                    client?: boolean;
                    company?: boolean;
                    gift_card?: boolean;
                    items?: boolean;
                    refunds?: boolean;
                    date?: string;
                },
            ) => {
                const params = new URLSearchParams();

                if (data?.polling) {
                    params.set('poll', 'true');
                }

                if (data?.relatable) {
                    params.set('relatable', 'true');
                }

                if (data?.refunds) {
                    params.set('refunds', 'true');
                }

                if (data?.client) {
                    params.set('client', 'true');
                }

                if (data?.company) {
                    params.set('company', 'true');
                }

                if (data?.gift_card) {
                    params.set('gift_card', 'true');
                }

                if (data?.items) {
                    params.set('items', 'true');
                }

                if (data?.terminal) {
                    params.set('terminal', 'true');
                }

                if (data?.date) {
                    params.set('date', data.date);
                }

                return `${baseUrl}/v3/point-of-sale/receipts/${id}?${params.toString()}`;
            },
            export: (id: IdType, data: { type: ReceiptExportType }) => {
                const params = new URLSearchParams();

                if (data?.type) {
                    params.set('type', data.type);
                }

                return `${baseUrl}/v3/point-of-sale/receipts/${id}/export?${params.toString()}`;
            },
        },

        report: {
            list: (data?: { start_date?: string; end_date?: string }) => {
                const params = new URLSearchParams();

                if (data?.start_date) {
                    params.set('start_date', data.start_date);
                }

                if (data?.end_date) {
                    params.set('end_date', data.end_date);
                }

                return `${baseUrl}/v3/point-of-sale/report?${params.toString()}`;
            },
            current: `${baseUrl}/v3/point-of-sale/report/current`,
            closeBatch: `${baseUrl}/v3/point-of-sale/report/close_batch`,
            download: (data?: { id: string | number; start_date?: string; end_date?: string }) => {
                const params = new URLSearchParams();

                if (data?.start_date) {
                    params.set('start_date', data.start_date);
                }

                if (data?.end_date) {
                    params.set('end_date', data.end_date);
                }
                return `${baseUrl}/v3/point-of-sale/report/${data?.id}/download?${params.toString()}`;
            },
        },

        infrasec: {
            enroll: `${baseUrl}/v3/point-of-sale/infrasec/enroll`,
        },

        fortnox: {
            auth: {
                connect: `${baseUrl}/v3/fortnox/auth/connect`,
                callback: `${baseUrl}/v3/fortnox/auth/callback`,
            },
        },

        setting: {
            key: {
                VIVA_CONNECTED_POPUP: 'VIVA_CONNECTED_POPUP',
            },
            uploadSwishQR: `${baseUrl}/v3/point-of-sale/upload-swish-qr`,
        },
    },

    v3: {
        companyUpdate: `${baseUrl}/v3/company/update`,
    },

    videoCall: {
        cancel: (id: IdType) => `${baseUrl}/v3/video-call/${id}/end`,
    },

    prescriptionUpcomingFess: `${baseUrl}/v3/doctor-portal/upcoming-fees`,
    syncTemplateList(type?: 'SMS' | 'PRESCRIPTION' | 'EMAIL') {
        return `${baseUrl}/v3/general_template/sync-templates?type=${type}`;
    },
    generalTemplateList(type?: 'SMS' | 'PRESCRIPTION' | 'EMAIL' | 'MANAGEMENT_DOCUMENT', is_active?: boolean) {
        return `${baseUrl}/v3/general_template?type=${type}`;
    },
    generalTemplateQuestionList(id?: IdType) {
        return `${baseUrl}/v3/template-questionary/${id}/question`;
    },
    prescriptionDelete(prescription_id?: IdType, client_id?: IdType) {
        return `${baseUrl}/v2/client/${client_id}/prescription/${prescription_id}/delete`;
    },
    treatmentDelete(id: IdType) {
        return `${baseUrl}/v2/treatment/${id}/force-delete`;
    },
    notificationList({ count, is_read, is_popup }: { count?: number; is_read?: number; is_popup?: number }) {
        if (count) {
            return `${baseUrl}/v3/user/notifications?show_only_count=${count}`;
        }
        if (is_read === 1 || is_read === 2) {
            return `${baseUrl}/v3/user/notifications?is_read=${is_read === 2 ? 0 : is_read}`;
        }
        if (is_popup) {
            return `${baseUrl}/v3/user/notifications?show_only_popups=${is_popup}`;
        }
        return `${baseUrl}/v3/user/notifications`;
    },
    notificationMarkAsAllRead: `${baseUrl}/v3/user/notifications/read`,
    notificationRead(id?: IdType) {
        return `${baseUrl}/v3/user/notification/${id}/read`;
    },
    bookingNoteList(booking_id?: IdType) {
        return `${baseUrl}/v2/user/bookings/notes?booking_id=${booking_id}`;
    },
    bookingNoteStore: `${baseUrl}/v2/user/bookings/notes/create`,
    bookingNoteUpdate(id?: IdType) {
        return `${baseUrl}/v2/user/bookings/notes/${id}/edit`;
    },
    bookingNoteDelete(id?: IdType) {
        return `${baseUrl}/v2/user/bookings/notes/${id}/delete`;
    },
    clientGeneralNotesDelete(id?: IdType) {
        return `${baseUrl}/general_note/${id}/delete`;
    },
    clientProcedureDelete(id?: IdType) {
        return `${baseUrl}/client_treatment/${id}/delete`;
    },
    exportClientData: `${baseUrl}/v3/clients/get-export`,
    clientRegenQuetionerPDF(client_id?: IdType, ques_id?: IdType) {
        return `${baseUrl}/v2/client/${client_id}/questionaries/${ques_id}/regenPdf`;
    },
    clientFileBatch: {
        create(client_id?: IdType) {
            return `${baseUrl}/v3/clients/${client_id}/file-batches/create`;
        },
        list(client_id: IdType, id: IdType, type: IdType) {
            return `${baseUrl}/v3/clients/${client_id}/file-batches?signable_id=${id}&signable_type=${type}`;
        },
    },
    clientEnableBlockAccess(client_id: IdType) {
        return `${baseUrl}/client_access/${client_id}/update-access`;
    },
    verify: {
        email: (id: IdType, hash: IdType, params: IdType) => {
            return `${baseUrl}/email/verify/${id}/${hash}?${params}`;
        },
    },
    bookingPortalCreateWithPos(companyId: IdType) {
        return `${baseUrl}/v3/booking-portal/${companyId}/bookings/create-with-pos`;
    },
    onlinePaymentStatus(params?: string) {
        return `${baseUrl}/v3/payment/online/status?${params}`;
    },
    companyUserCalendar: `${baseUrl}/v3/oauth/google-calender`,
    companyUserCalendarList: `${baseUrl}/v3/google-calendars`,
    companyUserCalendarCreate: `${baseUrl}/v3/google-calendars/store`,
    companyUserCalendardelete(userId: IdType) {
        return `${baseUrl}/v3/google-calendars/${userId}/delete`;
    },
    email_templates: {
        list: `${baseUrl}/v3/email-templates`,
        listActive: `${baseUrl}/v3/email-templates?filter=is_active&filter_value=1`,
        store: `${baseUrl}/v3/email-templates/store`,
        update: (id: IdType) => `${baseUrl}/v3/email-templates/${id}/update`,
        toggle: (id: IdType) => `${baseUrl}/v3/email-templates/${id}/toggle`,
        delete: (id: IdType) => `${baseUrl}/v3/email-templates/${id}/delete`,
    },
    management_device: {
        list: `${baseUrl}/v3/management-devices`,
        store: `${baseUrl}/v3/management-devices/store`,
        download: (force: boolean) => `${baseUrl}/v3/management-devices/download${force ? '?force=1' : ''}`,
        update(id: IdType) {
            return `${baseUrl}/v3/management-devices/${id}/edit`;
        },
        delete(id: IdType) {
            return `${baseUrl}/v3/management-devices/${id}/destroy`;
        },
        restore(id: IdType) {
            return `${baseUrl}/v3/management-devices/restore?medical_device_id=${id}`;
        },
        logs(id: IdType) {
            return `${baseUrl}/v3/management-devices/${id}/logs`;
        },
        logsDownload(id: IdType) {
            return `${baseUrl}/v3/management-devices/${id}/logs/download`;
        },
        maintenance: {
            list: (id: IdType) => `${baseUrl}/v3/management-devices/${id}/list-maintenance`,
            store: (id: IdType) => `${baseUrl}/v3/management-devices/${id}/create-maintenance`,
        },
    },
    documents: {
        list: `${baseUrl}/v3/company/documents`,
        store: `${baseUrl}/v3/company/documents/create`,
        download: (force: boolean) => `${baseUrl}/v3/company/documents/download${force ? '?force=1' : ''}`,
        view: (docId: IdType) => `${baseUrl}/v3/company/documents/${docId}`,
        update: (docId: IdType) => `${baseUrl}/v3/company/documents/${docId}/update`,
        versions: (docId: IdType) => `${baseUrl}/v3/company/documents/${docId}/versions`,
        logs: (id: IdType) => `${baseUrl}/v3/company/documents/${id}/logs`,
        logsDownload: (id: IdType) => `${baseUrl}/v3/company/documents/${id}/logs/download`,
        delete(id: IdType) {
            return `${baseUrl}/v3/company/documents/${id}/delete`;
        },
        restore(id: IdType) {
            return `${baseUrl}/v3/company/documents/restore?company_document_id=${id}`;
        },
    },
    legalDocuments: {
        list: `${baseUrl}/v3/legal-documents`,
        view: (lang: IdType, doc: 'PUBLIC' | 'DPA' | 'SUPPLIER') => `${baseUrl}/v3/legal-documents/get-view?lang=${lang}&type=${doc}`,
        store: `${baseUrl}/v3/legal-documents/store`,
    },
    reports: {
        record: (type: RecordReportAPITypes, start_at: IdType, end_at: IdType, period: IdType, user_ids?: number[]) => {
            const params = new URLSearchParams();
            params.set('type', `${type}`);
            params.set('start_date', `${start_at}`);
            params.set('end_date', `${end_at}`);
            params.set('period', `${period}`);
            if (user_ids?.length) {
                user_ids.forEach((element, index) => {
                    params.set(`user_ids[${index}]`, `${element}`);
                });
            }
            return `${baseUrl}/v3/reports/record?${params.toString()}`;
        },
        booking: (type: BookingReportAPITypes, start_at: IdType, end_at: IdType, period: IdType, user_ids?: number[]) => {
            const params = new URLSearchParams();
            params.set('type', `${type}`);
            params.set('start_date', `${start_at}`);
            params.set('end_date', `${end_at}`);
            params.set('period', `${period}`);
            if (user_ids?.length) {
                user_ids.forEach((element, index) => {
                    params.set(`user_ids[${index}]`, `${element}`);
                });
            }
            return `${baseUrl}/v3/reports/booking?${params.toString()}`;
        },
        pos: (type: POSReportAPITypes, start_at: IdType, end_at: IdType, period: IdType, user_ids?: number[]) => {
            const params = new URLSearchParams();
            params.set('type', `${type}`);
            params.set('start_date', `${start_at}`);
            params.set('end_date', `${end_at}`);
            params.set('period', `${period}`);
            if (user_ids?.length) {
                user_ids.forEach((element, index) => {
                    params.set(`user_ids[${index}]`, `${element}`);
                });
            }
            return `${baseUrl}/v3/reports/pos?${params.toString()}`;
        },
        export: (type: 'record' | 'booking' | 'pos', start_at: IdType, end_at: IdType, period: IdType, user_ids?: number[]) => {
            const params = new URLSearchParams();
            params.set('type', `${type}`);
            params.set('start_date', `${start_at}`);
            params.set('end_date', `${end_at}`);
            params.set('period', `${period}`);
            if (user_ids?.length) {
                user_ids.forEach((element, index) => {
                    params.set(`user_ids[${index}]`, `${element}`);
                });
            }
            switch (type) {
                case 'record':
                    return `${baseUrl}/v3/reports/record-export?${params.toString()}`;
                case 'booking':
                    return `${baseUrl}/v3/reports/booking-export?${params.toString()}`;
                case 'pos':
                    return `${baseUrl}/v3/reports/pos-export?${params.toString()}`;
            }
        },
    },
    clientServiceProductReceipt({ client_id, type }: { client_id: IdType; type: 'service' | 'product' }) {
        const params = new URLSearchParams();
        if (client_id) {
            params.set('client_id', `${client_id}`);
        }
        if (type) {
            params.set('type', `${type}`);
        }
        return `${baseUrl}/v3/point-of-sale/receipt_items?${params.toString()}`;
    },
    googlePlaces: {
        list: `${baseUrl}/v3/google-places`,
        store: `${baseUrl}/v3/google-places/store`,
        show: (id: IdType) => `${baseUrl}/v3/company/extra/${id}/show`,
        storeExtra: `${baseUrl}/v3/company/extra/store`,
    },
    calendarHolidays: `${baseUrl}/v3/holiday/calendars`,
    holidayList: `${baseUrl}/v3/holidays`,
    clientDownloadDataPdf(id?: IdType) {
        return `${baseUrl}/v3/clients/${id}/pdfs`;
    },
};

export const Routes = {
    viewBookingPortalInfo({ companyId, key, lang, clientKey }: { companyId: IdType; key: IdType; lang: IdType; clientKey?: string }) {
        return `/booking/info/${companyId}?lang=${lang}&key=${key}&client_key=${clientKey}`;
    },
    viewBookingPortalReschedule({ companyId, key, lang, clientKey }: { companyId: IdType; key: IdType; lang: IdType; clientKey?: string }) {
        if (!clientKey) return `/booking/reschedule/${companyId}?lang=${lang}&key=${key}`;
        return `/booking/reschedule/${companyId}?lang=${lang}&key=${key}&client_key=${clientKey}`;
    },
    viewBookingPortalLocAndQue({
        companyId,
        key,
        lang,
        isLoc,
        clientKey,
    }: {
        companyId: IdType;
        key: IdType;
        lang: IdType;
        isLoc: IdType;
        clientKey?: string;
    }) {
        if (clientKey) {
            return `/booking/${companyId}?lang=${lang}&key=${key}&is_FillLocAndQue=${isLoc}&client_key=${clientKey}`;
        } else {
            return `/booking/${companyId}?lang=${lang}&key=${key}&is_FillLocAndQue=${isLoc}`;
        }
    },
};

export default api;
