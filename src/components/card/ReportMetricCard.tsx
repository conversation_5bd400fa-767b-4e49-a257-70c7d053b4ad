import { CSSProperties, FC, ReactNode } from 'react';
import Card from '.';
import cx from 'classix';
import MetricUpIcon from '@icons/MetricUpIcon';
import useTheme from '@hooks/useTheme';
import Skeleton from '@components/Skeleton/Skeleton';

const colors: {
    [key in ReportMetricCardProps['color']]: CSSProperties;
} = {
    primary: { backgroundColor: '#F2F2FF', color: '#6E6AF0' },
    yellow: { backgroundColor: '#FFF3D6', color: '#E1A619' },
    green: { backgroundColor: '#D9F7E8', color: '#32CA81' },
    orange: { backgroundColor: '#FFE9DF', color: '#F78450' },
};

const darkColors: {
    [key in ReportMetricCardProps['color']]: CSSProperties;
} = {
    primary: { backgroundColor: '#6E6AF026', color: '#6E6AF0' },
    yellow: { backgroundColor: '#E1A61926', color: '#E1A619' },
    green: { backgroundColor: '#32CA8126', color: '#32CA81' },
    orange: { backgroundColor: '#F7845026', color: '#F78450' },
};

const metricColors: {
    [key in 'up' | 'down']: CSSProperties;
} = {
    up: { color: '#21AD63' },
    down: { color: '#FF3E1D' },
};

const ReportIconItem: FC<ReportMetricCardProps> = ({ icon, color, title, value, metric = 0, loading = false }) => {
    const metricUp = metric > 0.1;

    const { isDark } = useTheme();

    return (
        <Card className="flex flex-row-reverse gap-2 md:flex-row md:gap-4">
            <div
                className={cx('flex size-10 items-center justify-center rounded-xl text-2xl md:size-16 md:rounded-2xl md:text-3xl')}
                style={isDark ? darkColors[color] : colors[color]}
            >
                {icon}
            </div>
            <div className="flex h-full grow flex-col justify-between">
                <p className="text-sm text-gray-600 first-letter:uppercase dark:text-gray-300 xl:text-base">{title}</p>
                <div className="flex justify-between">
                    {loading ? <Skeleton className="h-6 w-16 md:w-20" /> : <div className="text-xl font-semibold">{value}</div>}
                    {metric !== 0 && (
                        <div className="flex items-center space-x-0.5" style={metricUp ? metricColors.up : metricColors.down}>
                            <MetricUpIcon className={cx('text-xl', !metricUp && '-scale-y-100')} />
                            <p>
                                <span className="font-medium">{Math.abs(metric)}</span>
                                <span className="text-xs font-normal">%</span>
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </Card>
    );
};

interface ReportMetricCardProps {
    title: string;
    icon: ReactNode;
    value: string | number | ReactNode;
    metric?: number;
    color: 'primary' | 'yellow' | 'green' | 'orange';
    loading?: boolean;
}

export default ReportIconItem;
