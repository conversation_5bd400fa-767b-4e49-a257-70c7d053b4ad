import strings from '@lang/Lang';
import TimePeriod from './TimePeriod';
import cx from 'classix';

export interface JoinedProps {
    value: string;
    onChange: (value: string) => void;
    error?: string;
}
const Joined: React.FC<JoinedProps> = ({ onChange, value, error }) => {
    return (
        <div className={cx('w-full', error && 'pb-2')}>
            <div className="flex flex-wrap items-center gap-2 rounded-lg bg-grayishBlue px-3 py-1.5 dark:bg-darkGray">
                <p className="">
                    {strings.target_client_who_joined} {strings.in_last}
                </p>
                <div>
                    <TimePeriod onChange={onChange} value={value} />
                </div>
            </div>
            <p className="absolute text-[10px] text-red-500 first-letter:capitalize">{error}</p>
        </div>
    );
};
export default Joined;
