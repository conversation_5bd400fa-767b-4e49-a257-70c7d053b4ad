import { DocumentVersion } from '@interface/model/document';
import { File } from '@interface/model/File';
import { useState } from 'react';
import strings from '@lang/Lang';
import ViewModalTextItem from '@partials/ViewModal/ViewModalTextItem';
import api from '@configs/api';

export interface DocumentVersionViewComponentProps {
    version: DocumentVersion;
}

const DocumentVersionViewComponent: React.FC<DocumentVersionViewComponentProps> = ({ version }) => {
    const [downloading, setDownloading] = useState<string | undefined>();
    const [viewFileModalOpen, setViewFileModalOpen] = useState<boolean>(false);

    const questions = version?.questions;

    return (
        <>
            <div className="grid grid-flow-row grid-cols-1 gap-4 p-4">
                {questions?.map((question, index) => {
                    const views: React.ReactNode[] = [];

                    const isYesNo = question.type === 'yes_no_textbox' || question.type === 'yes_no';
                    const hasTextarea = question.type === 'yes_no_textbox' || question.type === 'textbox';
                    const isHTML = question.type === 'html_editor';

                    const answer = version.formatted_response.at(index);

                    views.push(
                        <div className="flex flex-col px-0 py-2 md:flex-row md:px-0">
                            <p className="flex-grow font-medium">
                                <span>{index + 1}.&nbsp;</span>
                                {question.question}
                            </p>
                        </div>,
                    );

                    if (isYesNo && answer?.value) {
                        views.push(<p className="pl-5">{answer?.value == 'yes' ? strings.Yes : strings.No}</p>);
                    }

                    if (hasTextarea) {
                        views.push(<p className="pl-5">{typeof answer === 'string' ? answer : answer?.text}</p>);
                    }

                    if (isHTML) {
                        views.push(
                            <div
                                className="prose prose-gray whitespace-pre-wrap break-words pl-5 dark:prose-invert"
                                dangerouslySetInnerHTML={{ __html: answer?.text ?? '' }}
                            />,
                        );
                    }

                    return (
                        <div key={index} className="w-full">
                            {views}
                        </div>
                    );
                })}
                <ViewModalTextItem title={strings.VerifiedSign} show={!!version?.signed_at} value={api.storageUrl(version?.sign)} image />
            </div>
        </>
    );

    async function onDownloadClick(file: File, file_name: string) {
        setDownloading(`download_${file.id}`);

        const response = await fetch(file.url);

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);

        a.target = '_blank';

        a.setAttribute('download', file_name);
        a.click();

        setDownloading(undefined);
    }
};

export default DocumentVersionViewComponent;
