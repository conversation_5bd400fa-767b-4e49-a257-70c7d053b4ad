import { commonFetch, getUnitWithValue, saveSetting } from '@/helper';
import Button from '@components/form/Button';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import useLocalStorage from '@hooks/useLocalStorage';
import CalendarIcon from '@icons/Calendar';
import { ClientsCountResponse, SettingResponse, SubscriptionPlanResponse } from '@interface/common';
import strings from '@lang/Lang';
import RadioButton from '@partials/MaterialRadioButton/MaterialRadioButton';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import Modal from './Modal';
import dayjs from 'dayjs';

const BookingPaidRemainderModal = () => {
    const [openModal, setOpenModal] = useState(false);
    const { storedValue: modalShown, setStorageValue: setModalShown } = useLocalStorage('booking_paid_info', false);
    const { data, mutate, isLoading: loading } = useSWR<SettingResponse, Error>(api.setting);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [optStatus, setOptStatus] = useState<number>(0);
    const today = dayjs();
    const futureDate = dayjs('2025-01-01');
    const {
        user,
        userType: { isSuperAdmin },
    } = useAuth();
    const { data: bookingCount } = useSWR<ClientsCountResponse>(!openModal ? null : api.clientsCount, commonFetch);
    const { data: response } = useSWR<SubscriptionPlanResponse, Error>(!openModal ? null : api.subscription.plans, commonFetch, {
        revalidateOnFocus: false,
        refreshInterval: 0,
        refreshWhenHidden: false,
        revalidateOnMount: true,
        revalidateOnReconnect: true,
        refreshWhenOffline: true,
    });
    const currentBillingCycleYearly =
        user?.company?.record_plan?.is_yearly || user?.company?.pos_plan?.is_yearly || user?.company?.management_plan?.is_yearly || false;
    const bookingSystem = response?.data.find((val) => val?.is_yearly === currentBillingCycleYearly && val.platform === 'BOOKING_SYSTEM');
    useEffect(() => {
        if (today.isBefore(futureDate) && user?.company?.is_booking_on) {
            if (!loading && data?.data) {
                if (data?.data.find((setting) => setting.key === 'BOOKING_PLAN')?.value) return;
                if (isSuperAdmin) {
                    setOpenModal(true);
                }
                if (!isSuperAdmin && !modalShown) {
                    setOpenModal(true);
                }
            }
        }
    }, []);
    async function handleSubmit() {
        setIsLoading(true);
        const data = await saveSetting({ key: 'BOOKING_PLAN', value: optStatus === 1 ? 'OPT_IN' : 'OPT_OUT' });
        if (data.status === '1') {
            mutate();
            setOpenModal(false);
        } else {
            toast.error(data.message || 'please try again');
        }
        setIsLoading(false);
    }
    return (
        <Modal open={openModal} title={``} hideCloseButton>
            <div className="space-y-6 p-6">
                <div className="space-y-2 text-center">
                    <div className="flex justify-center">
                        <div className="rounded-full bg-primary p-3 dark:bg-primaryLight">
                            <CalendarIcon className="text-4xl text-white" />
                        </div>
                    </div>
                    <p className="text-2xl font-bold text-primary dark:text-primaryLight">{strings.information_to_you}</p>
                    <p className="px-1 text-2xl font-bold">{strings.the_booking_system_will_be_paid_starting_from}</p>
                </div>
                {isSuperAdmin ? (
                    <div className="space-y-3">
                        <p className="text-lg font-semibold">{strings.give_your_input}</p>
                        <div>
                            <RadioButton label={strings.opt_in} checked={optStatus === 1} onClick={() => setOptStatus(1)} />
                            <p className="pl-6 text-sm">
                                {strings.formatString(
                                    strings.the_booking_system_will_be_included_in_the_current_paid_plan,
                                    getUnitWithValue(bookingSystem?.currency ?? 'sek', bookingSystem?.price.toString() ?? '0'),
                                    currentBillingCycleYearly ? strings.Yearly.toLowerCase() : strings.Monthly.toLowerCase(),
                                )}
                            </p>
                        </div>
                        {optStatus === 1 && (
                            <div className="space-y-1 text-sm text-warning">
                                <p>{strings.your_price_will_be_deducted_based_on_the_number_of_active_booking_users}</p>
                                <p>
                                    {strings.active_user}: {bookingCount?.data?.booking_users}
                                </p>
                                <p>
                                    {strings.total}:{' '}
                                    {getUnitWithValue(
                                        bookingSystem?.currency ?? 'sek',
                                        ((bookingCount?.data?.booking_users || 1) * parseInt(bookingSystem?.price || '0')).toString(),
                                    )}
                                    /{currentBillingCycleYearly ? strings.Yearly.toLowerCase() : strings.Monthly.toLowerCase()}
                                </p>
                            </div>
                        )}
                        <div>
                            <RadioButton label={strings.opt_out} checked={optStatus === 2} onClick={() => setOptStatus(2)} />
                            <p className="pl-6 text-sm">{strings.the_booking_system_will_end_on_at_midnight}</p>
                        </div>
                    </div>
                ) : (
                    <div className="space-y-6 text-center">
                        <p>{strings.if_you_are_not_using_the_booking_system}</p>
                        <div className="flex justify-center">
                            <RadioButton label={strings.do_not_show_this_again} checked={optStatus === 3} onClick={() => setOptStatus(3)} />
                        </div>
                    </div>
                )}
                <div className="flex justify-center gap-7 pt-4">
                    <Button variant="outlined" onClick={() => setOpenModal(false)}>
                        <p className="text-primary">{strings.remind_me_later}</p>
                    </Button>
                    <Button
                        size="big"
                        loading={isLoading}
                        disabled={optStatus === 0}
                        onClick={() => {
                            if (optStatus === 3) {
                                setModalShown(true);
                                setOpenModal(false);
                            } else {
                                handleSubmit();
                            }
                        }}
                    >
                        <p className="px-5">{strings.Submit}</p>
                    </Button>
                </div>
            </div>
        </Modal>
    );
};
export default BookingPaidRemainderModal;
