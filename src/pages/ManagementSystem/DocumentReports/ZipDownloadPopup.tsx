import Button from '@components/form/Button';
import { File } from '@interface/model/File';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';
import { useHours } from '@provider/TimeProvider';

export interface ZipDownloadData {
    id: number;
    message?: string;
    file?: File;
}

export interface ZipDownloadPopupProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    data?: ZipDownloadData;
    refetch: (force: boolean) => Promise<void>;
}

function ZipDownloadPopup({ openModal, setOpenModal, data, refetch }: ZipDownloadPopupProps) {
    const { renderDateTime } = useHours();

    return (
        <Modal
            open={openModal}
            title={strings.important}
            handleClose={() => setOpenModal(false)}
            submitButton={
                <Button
                    onClick={() => {
                        if (data?.file) {
                            refetch(true);
                        }
                        setOpenModal(false);
                    }}
                >
                    {data?.file ? strings.generateNew : strings.Okay}
                </Button>
            }
        >
            <div className="space-y-4 p-4">
                <p className="whitespace-pre-wrap text-center">{data?.message}</p>
                {data?.file && (
                    <>
                        <div className="text-center">
                            <p className="mb-1 text-sm">
                                {strings.generatedOn}: {renderDateTime(data.file.created_at, { utc: true, isShowLocal: true })}
                            </p>
                            <div className="inline-block">
                                <a href={data.file.url} target="_blank" rel="noreferrer" onClick={() => setOpenModal(false)}>
                                    <Button
                                        variant="outlined"
                                        children={`${strings.Download} ${data.file.filename.split('/').at(-1)}`}
                                        color="primary"
                                    />
                                </a>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </Modal>
    );
}

export default ZipDownloadPopup;
