import { generateFullName, timeZone } from "@/helper";
import Card from "@components/card";
import Button from "@components/form/Button";
import CountrySelect from "@components/form/CountrySelect";
import { CreditCard } from "@components/form/CreditCard";
import Input from "@components/form/Input";
import Label from "@components/form/Label";
import PhoneSelect from "@components/form/PhoneSelect";
import Heading from "@components/heading/Heading";
import api from "@configs/api";
import { findCountryByCode, findCountryByName } from "@configs/countries";
import useAuth from "@hooks/useAuth";
import useLocalStorage from "@hooks/useLocalStorage";
import strings from "@lang/Lang";
import PrescriptionLogoWithName from '@partials/Icons/PrescriptionLogoWithName';
import Checkbox from "@partials/MaterialCheckbox/MaterialCheckbox";
import Modal from "@partials/MaterialModal/Modal";
import { CardElement, useElements, useStripe } from "@stripe/react-stripe-js";
import { PrescriptionSignupSetupFormValues, prescriptionSingUpSetupValidation } from "@validations/prescription-signup";
import dayjs from "dayjs";
import { Formik } from "formik";
import React, { useEffect } from "react";
import { Navigate, useNavigate } from "react-router";
import { toast } from "react-toastify";

export interface SetUpProps {

}
const PrescriptionSignup: React.FC<SetUpProps> = () => {
    const { user, mutate } = useAuth()
    const [signupSetup, setSignupSetup] = React.useState<boolean>(false);
    const [paymentMethodValue, setPaymentMethodValue] = React.useState<any>('');
    const today = dayjs();
    const navigate = useNavigate();
    const { setStorageValue: setIsPrescriber } = useLocalStorage('is_prescriber', false);
    const stripe = useStripe();
    const elements = useElements();
    const [otpModal, setOtpModal] = React.useState<boolean>(false);
    useEffect(() => {
        if (!signupSetup) {
            // reSendOTP();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])
    async function reSendOTP() {
        const otpResponse = await fetch(api.prescriptionOtp, {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                'X-App-Locale': strings.getLanguage(),
            },
            credentials: 'include',
        });
        const data = await otpResponse.json();
        if (data.status === '1') {
            setOtpModal(true);
        } else {
            toast.error(data.message)
        }
    }

    return (
        <div>
            {!user && <Navigate to={'/prescription/sign-up'} />}
            {showOTPModal()}
            <div className="container px-6 mt-8">
                <PrescriptionLogoWithName />
            </div>
            <div className="container max-w-3xl my-6 px-6">
                <Card className="text-dimGray px-6">
                    <div className="max-w-3xl mx-auto py-4">
                        {signupSetup ?
                            <div>
                                <Heading className="dark:text-white pb-6" text={strings.license_agreement} />
                            </div> :
                            <div className="pb-6">
                                <Heading className="dark:text-white" text={strings.letsSetUpYourAccount} variant="bigTitle" />
                            </div>
                        }
                        <Formik<PrescriptionSignupSetupFormValues>
                            key={"setup_form"}
                            initialValues={{
                                first_name: '',
                                last_name: '',
                                company_name: '',
                                country: '',
                                country_code: '',
                                mobile_number: '',
                                // otp: '',
                                Address: '',
                                city: '',
                                paymentMethod: '',
                                personal_id: '',
                                vat_number: '',
                                zip_code: '',
                                license_agreement: false,
                            }}
                            enableReinitialize
                            validate={prescriptionSingUpSetupValidation}
                            onSubmit={async (values, { resetForm, setSubmitting }) => {
                                setIsPrescriber(true);
                                const agreement = document.getElementById('license_argreement')?.innerHTML;

                                const finalObject = {
                                    ...values,
                                    vat_number: values.vat_number,
                                    address: {
                                        city: values.city,
                                        country: values.country,
                                        line1: values.Address,
                                        postal_code: values.zip_code,
                                    },
                                    license_agreement: agreement?.replace(`${generateFullName(values.first_name, values.last_name)}`, '{{user_name}}'),
                                }
                                if (!signupSetup) {
                                    const card = elements?.getElement(CardElement);
                                    if (!stripe || !elements || !card) {
                                        setSubmitting(false);
                                        return;
                                    }
                                    const connectToStripe = await fetch(api.connectToStripe, {
                                        method: 'POST',
                                        headers: {
                                            Accept: 'application/json',
                                            'X-App-Locale': strings.getLanguage(),
                                            'X-Time-Zone': timeZone(),
                                        },
                                        credentials: 'include',
                                    });
                                    const stripeData = await connectToStripe.json();
                                    const payload = await stripe.confirmCardSetup(stripeData.data.intent, {
                                        payment_method: {
                                            card: card,
                                            billing_details: {
                                                address: {
                                                    city: values.city,
                                                    country: findCountryByName(values.country)?.abbr,
                                                    line1: values.Address,
                                                    postal_code: values.zip_code,
                                                },
                                                email: user?.company?.email,
                                                phone: values.mobile_number,
                                                name: generateFullName(values.first_name, values.last_name),
                                            },
                                            metadata: {
                                                company_name: values.company_name,
                                                vat_number: values.vat_number ?? '',
                                            },
                                        },
                                    });

                                    if (payload?.error?.code === 'setup_intent_unexpected_state') {
                                        window.location.reload();
                                    }

                                    if (payload?.error) {
                                        toast.error(payload?.error.message || 'card error, please try again.');
                                        setSubmitting(false);
                                        return;
                                    }

                                    setPaymentMethodValue(payload?.setupIntent.payment_method)
                                
                                    setSignupSetup(true);
                                } else {
                                    const response = await fetch(api.prescriptionRegister, {
                                        method: 'POST',
                                        headers: {
                                            "Accept": 'application/json',
                                            'X-App-Locale': strings.getLanguage(),
                                            "Content-Type": "application/json",
                                        },
                                        credentials: 'include',
                                        body: JSON.stringify({
                                            ...finalObject,
                                            payment_method: paymentMethodValue,
                                        }),
                                    });
                                    const data = await response.json();
                                    if (data.status === '1') {
                                        toast.success(data.message || 'Your account has been registered.');
                                        await mutate();
                                        navigate('/');
                                        resetForm();
                                    }
                                    if (['2', '3', '0'].includes(data.status)) {
                                        toast.error(data?.message || 'server error. please try contact admin.');
                                    }
                                    setSubmitting(false);
                                }
                            }}
                        >
                            {({ errors, values, touched, handleChange, handleBlur, dirty, setFieldValue, setFieldTouched, handleSubmit, isSubmitting, isValidating, setFieldError, submitForm }) => {
                                return (
                                    <div>
                                        {signupSetup ?
                                            <div className="space-y-6">
                                                <div id='license_argreement' className="space-y-4">
                                                    <div className="space-y-1">
                                                        <h1 className="text-lg text-primary dark:text-primaryLight">LICENSAVTAL GÄLLANDE ORDINATIONSTJÄNST FRÅN MERIDIQ</h1>
                                                        <p className="text-black dark:text-white">Jag, {generateFullName(values.first_name, values.last_name)}, härmed godkänner detta avtal ("Avtal") som ingås den {today.format('DD-MM-YYYY')} ("Effektivt datum"), mellan mig, nedan kallad "Läkaren," och MERIDIQ AB, nedan kallad "Tjänsteleverantören."</p>
                                                    </div>
                                                    <div className="space-y-1">
                                                        <h1 className="text-lg text-primary dark:text-primaryLight">Godkännande av Tjänster:</h1>
                                                        <p className="text-black dark:text-white">1.1 Jag godkänner att Tjänsteleverantören tillhandahåller mig tillgång till en online-plattform som underlättar listning av kliniker kopplade till mig, samt listning av patienter som behöver ordination. Jag godkänner även möjligheten att godkänna ordinationer för dessa patienter på plattformen.</p>
                                                        <p className="text-black dark:text-white">1.2 Jag bekräftar mitt godkännande av att utföra granskning av patientinformation, godkänna ordinationer och tillhandahålla medicinska konsultationer genom den online-plattformen.</p>
                                                    </div>
                                                    <div className="space-y-1">
                                                        <h1 className="text-lg text-primary dark:text-primaryLight">Godkännande av Avgifter:</h1>
                                                        <p className="text-black dark:text-white">2.1 Jag erkänner och godkänner att Tjänsteleverantören kommer att ta ut en avgift för varje ordination som jag godkänner genom den online-plattformen. Jag godkänner de angivna avgiftsbeloppen och betalningsvillkoren enligt det avgiftsschema som tillhandahålls av Tjänsteleverantören.</p>
                                                    </div>
                                                    <div className="space-y-1">
                                                        <h1 className="text-lg text-primary dark:text-primaryLight">Bekräftelse av Efterlevnad av Lagar:</h1>
                                                        <p className="text-black dark:text-white">3.1 Jag åtar mig att följa alla tillämpliga lagar och regler, inklusive Hälso- och sjukvårdslagen och GDPR.</p>
                                                        <p className="text-black dark:text-white">3.2 Jag kommer att säkerställa skyddet och konfidentialiteten av patientdata samt följa principerna om dataminimering, ändamålsbegränsning och datasäkerhet enligt GDPR.</p>
                                                    </div>
                                                    <div className="space-y-1">
                                                        <h1 className="text-lg text-primary dark:text-primaryLight">Godkännande av Tid och Uppsägning:</h1>
                                                        <p className="text-black dark:text-white">4.1 Jag godkänner att detta Avtal träder i kraft på Effektivt datum och fortsätter tills det sägs upp av mig med 30 dagars skriftligt meddelande.</p>
                                                        <p className="text-black dark:text-white">4.2 Jag förstår att jag kan säga upp detta Avtal omedelbart om Tjänsteleverantören bryter mot någon väsentlig bestämmelse i detta avtal.</p>
                                                    </div>
                                                </div>
                                                <Checkbox
                                                    label={strings.i_accept_this_license_agreement}
                                                    checked={values.license_agreement}
                                                    name="license_agreement"
                                                    onChange={(e) => {
                                                        setFieldValue('license_agreement', e.target.checked)
                                                    }}
                                                />
                                                <div className="flex justify-center">
                                                    <Button size="big"
                                                        disabled={!values.license_agreement}
                                                        onClick={() => { handleSubmit(); }}
                                                        loading={isSubmitting || isValidating}
                                                    >
                                                        {strings.SAVEANDFINISH}
                                                    </Button>
                                                </div>
                                            </div>
                                            :
                                            <div className=" space-y-6">
                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                    <Input
                                                        placeholder={strings.Firstname}
                                                        label={strings.Firstname}
                                                        value={values.first_name}
                                                        type="first_name"
                                                        name="first_name"
                                                        required
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={errors.first_name && touched.first_name && errors.first_name}
                                                    />
                                                    <Input
                                                        placeholder={strings.Lastname}
                                                        label={strings.Lastname}
                                                        value={values.last_name}
                                                        type="last_name"
                                                        required
                                                        name="last_name"
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={errors.last_name && touched.last_name && errors.last_name}
                                                    />
                                                    <Input
                                                        label={strings.CompanyName}
                                                        placeholder={strings.CompanyName}
                                                        value={values.company_name}
                                                        type="company_name"
                                                        required
                                                        name="company_name"
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={errors.company_name && touched.company_name && errors.company_name}
                                                    />
                                                    <PhoneSelect
                                                        value={values.mobile_number}
                                                        countryValue={findCountryByCode(values.country_code)}
                                                        required
                                                        onChangeCountry={(value) => {
                                                            setFieldTouched('country_code');
                                                            setFieldValue('country_code', value?.code);
                                                        }}
                                                        onChange={(number) => {
                                                            setFieldTouched('mobile_number');
                                                            setFieldValue('mobile_number', number);
                                                        }}
                                                        error={touched?.mobile_number && errors.mobile_number}
                                                        countryError={touched?.country_code && errors.country_code}
                                                    />
                                                    {/* <Input
                                                        label={strings.email_otp}
                                                        placeholder={strings.otp}
                                                        value={values.otp}
                                                        type="number"
                                                        required
                                                        name="otp"
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={errors.otp && touched.otp && errors.otp}
                                                        suffix={strings.resend}
                                                        onSuffixClick={reSendOTP}
                                                    /> */}
                                                    <Input
                                                        name="personal_id"
                                                        value={values.personal_id || ''}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        onInput={(e) => {
                                                            e.currentTarget.value = e.currentTarget.value.toUpperCase().replace(/[^A-Z0-9-]/g, '').replace(/(\..*?)\..*/g, '$1');
                                                        }}
                                                        error={touched?.personal_id && errors?.personal_id}
                                                        label={strings.PersonalID_with_format}
                                                        placeholder={strings.PersonalID_format}
                                                    />
                                                </div>
                                                <hr />
                                                <Input
                                                    placeholder={strings.Address}
                                                    label={strings.Address}
                                                    value={values.Address}
                                                    type="Address"
                                                    name="Address"
                                                    onChange={handleChange}
                                                    onBlur={handleBlur}
                                                    error={errors.Address && touched.Address && errors.Address}
                                                />
                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                    <Input
                                                        name="city"
                                                        type="text"
                                                        value={values.city || ''}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={touched?.city && errors.city}
                                                        label={strings.City}
                                                        placeholder={strings.City}
                                                    />
                                                    <Input
                                                        name="zip_code"
                                                        type="text"
                                                        value={values.zip_code || ''}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={touched?.zip_code && errors.zip_code}
                                                        label={strings.ZipCode}
                                                        placeholder={strings.ZipCode}
                                                    />
                                                    <CountrySelect
                                                        defaultValue={findCountryByName(values.country)}
                                                        required
                                                        onChange={(value) => {
                                                            if (!value?.name) return;
                                                            setFieldTouched('country');
                                                            setFieldValue('country', value.name);
                                                        }}
                                                        error={touched?.country && errors.country && errors.country}
                                                    />
                                                    <Input
                                                        name="vat_number"
                                                        type="text"
                                                        value={values.vat_number || ''}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                        error={touched?.vat_number && errors.vat_number}
                                                        label={strings.VAT_number}
                                                        placeholder={strings.VAT_number}
                                                    />
                                                </div>
                                                <hr />
                                                <div className="block md:hidden text-black dark:text-white">{strings.billingDetails}</div>
                                                <div>
                                                    <Label label={strings.cardDetails} />
                                                    <CreditCard
                                                        error={touched?.paymentMethod && errors?.paymentMethod}
                                                        disabled={false}
                                                    />
                                                </div>
                                                <Button
                                                    size="big"
                                                    fullWidth
                                                    type="submit"
                                                    loading={isSubmitting || isValidating}
                                                    onClick={() => { handleSubmit(); }}
                                                >
                                                    {strings.startJourney}
                                                </Button>
                                            </div>
                                        }
                                    </div>
                                )
                            }}
                        </Formik>
                    </div>
                </Card>
            </div>
        </div>
    );
    function showOTPModal() {
        return (
            <Modal
                open={otpModal}
                title={strings.otp_sent}
                handleClose={() => setOtpModal(false)}
            >
                <div className="space-y-4 p-7 text-center">
                    {strings.otp_sent_successfully}
                </div>
            </Modal>
        );
    }
}

export default PrescriptionSignup;