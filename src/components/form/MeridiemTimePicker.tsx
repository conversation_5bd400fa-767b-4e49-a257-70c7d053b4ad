import { Popover, PopoverButton, PopoverPanel, Transition } from '@headlessui/react';
import React from 'react';
import Input from './Input';
import Error from './Error';
import { useHours } from '@provider/TimeProvider';
import strings from '@lang/Lang';
import Button from './Button';
import Timeit from './TimeIt';

export type Meridiem = 'AM' | 'PM';

export interface MeridiemTimePickerProps {
    value?: string | any;
    label?: string;
    onChange?: (time: string, meridiem?: Meridiem) => void;
    error?: string | false | string[];
    placeholder?: string;
    disabled?: boolean;
    divideInMinutes?: number;
    className?: string;
    meridiem?: Meridiem;
    required?: boolean;
}

const MeridiemTimePicker: React.FC<MeridiemTimePickerProps> = ({
    value,
    label,
    error,
    onChange = () => {},
    placeholder,
    disabled,
    divideInMinutes = 5,
    className,
    meridiem,
    required,
}) => {
    const { isTwelveHours } = useHours();
    async function handleChangeTime(time: string, meridiem?: Meridiem) {
        await onChange(time, isTwelveHours ? meridiem : undefined);
    }

    return (
        <div className="relative">
            <Popover className="relative">
                <PopoverButton
                    as={Input}
                    readOnly
                    className={`form-select ${className} ${error ? 'border-red-500 text-red-500 focus:text-black' : ''}`}
                    label={label}
                    value={value + `${value ? ` ${meridiem ? meridiem : ''}` : ''}`}
                    placeholder={placeholder}
                    required={required}
                    disabled={disabled}
                />
                <PopoverPanel
                    anchor={{ to: 'bottom start', gap: 4 }}
                    className="absolute z-50 overflow-auto rounded-lg bg-white p-4 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-900 sm:text-sm"
                >
                    <div className="flex flex-col justify-center gap-3 md:flex-row">
                        <Timeit
                            defaultValue={value}
                            onChange={(e) => handleChangeTime(e, meridiem)}
                            minuteExclude={Array.from(Array(60).keys()).filter((v) => v % divideInMinutes !== 0)}
                            hourExclude={Array.from(Array(24).keys()).filter((h) => (isTwelveHours ? h > 12 : h > 24))}
                            notShowExclude
                        />
                        {isTwelveHours && (
                            <div className="flex flex-row justify-center gap-2 md:flex-col">
                                <Button
                                    size="small"
                                    variant={`${meridiem === 'AM' ? 'filled' : 'outlined'}`}
                                    onClick={() => handleChangeTime(value, 'AM')}
                                >
                                    {strings.am}
                                </Button>
                                <Button
                                    size="small"
                                    variant={`${meridiem === 'PM' ? 'filled' : 'outlined'}`}
                                    onClick={() => handleChangeTime(value, 'PM')}
                                >
                                    {strings.pm}
                                </Button>
                            </div>
                        )}
                    </div>
                </PopoverPanel>
            </Popover>
            <Error error={error} />
        </div>
    );
};

export default MeridiemTimePicker;
