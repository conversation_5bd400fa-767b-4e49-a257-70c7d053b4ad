import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions, Transition } from '@headlessui/react';
import LoadingIcon from '@icons/Loading';
import strings from '@lang/Lang';
import CloseIcon from '@partials/Icons/Close';
import { cx } from 'classix';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import Error from './Error';
import { InputProps } from './Input';
import Label from './Label';
import ChevronIcon from '@partials/Icons/Chevron';

export interface AutocompleteProps<T> {
    options: T[];
    value?: T | null;
    children?: ReactNode;
    onChange: (val: T | undefined) => void;
    displayValue: (val?: T) => string;
    filteredValues: (query: string) => T[];
    error?: string | false;
    inputProps: InputProps;
    renderOption: (option: T) => string;
    disabled?: boolean;
    loading?: boolean;
    optionsHeight?: string;
    onSearchChange?: (query: string) => void;
}

const AutocompleteVirtual = <T extends unknown>({
    options,
    onChange,
    value,
    displayValue,
    filteredValues,
    error,
    inputProps: { className: inputClassName, ...inputProps },
    renderOption,
    disabled,
    loading,
    optionsHeight,
    onSearchChange,
}: AutocompleteProps<T>) => {
    const [query, setQuery] = useState('');
    const [selectedValue, setSelectedValue] = useState<T | undefined | null>(value);

    useEffect(() => {
        if (selectedValue) {
            onChange(selectedValue);
        }
    }, [selectedValue]);

    useEffect(() => {
        setSelectedValue(value);
    }, [value]);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const list = useMemo(() => (query === '' ? options : filteredValues(query)), [query, options]);

    const showCloseIcon = selectedValue && !inputProps?.required && !disabled;

    const className = cx(
        'form-input block w-full rounded-lg border-grayishBlue bg-transparent px-3.5 py-2 text-dimGray transition-all placeholder:text-mediumGray hover:border-mediumGray focus:border-primary focus:ring-1 disabled:cursor-not-allowed disabled:text-gray-500 dark:border-darkGray dark:text-white dark:placeholder:text-gray-600 dark:hover:border-gray-600 md:py-2.5',
        error && 'border-red-500 text-red-500 focus:text-black',
        loading && 'pl-10',
        showCloseIcon ? 'pr-14' : 'pr-7 lg:pr-14',
    );

    const { ref, ...restInputProps } = inputProps;

    return (
        <div className="relative">
            <Label label={inputProps?.label} required={inputProps?.required} />
            <Combobox<T | null> value={selectedValue ?? null} onChange={setSelectedValue} disabled={disabled} immediate virtual={{ options: list }}>
                {({ open }) => (
                    <div className="relative">
                        <div className={`w-full rounded-lg bg-grayishBlue p-0 dark:bg-darkGray ${inputClassName}`}>
                            {loading && (
                                <div className="absolute inset-0 flex w-10 items-center justify-center text-primary">
                                    <LoadingIcon />
                                </div>
                            )}
                            <ComboboxInput
                                className={className}
                                displayValue={(v) => displayValue(v as T)}
                                onChange={(event) => {
                                    setQuery(event.target.value);
                                    if (onSearchChange) onSearchChange(event.target.value);
                                }}
                                {...restInputProps}
                            />
                            <ComboboxButton className="absolute right-1 top-1/2 -translate-y-1/2 p-1 text-gray-500 sm:p-2">
                                <ChevronIcon className="text-xl" side={open ? 'top' : 'bottom'} />
                            </ComboboxButton>
                            {showCloseIcon && (
                                <div
                                    className="absolute right-8 top-2 p-1 md:top-3"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        setSelectedValue(undefined);
                                        onChange(undefined);
                                    }}
                                >
                                    <CloseIcon className="text-primary dark:text-primaryLight" />
                                </div>
                            )}
                        </div>
                        <ComboboxOptions
                            className={cx(
                                'soft-searchbar z-50 w-[--input-width] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-black dark:text-white sm:text-sm',
                                'transition duration-200 [--anchor-gap:3px] [--anchor-max-height:16rem] data-[closed]:-translate-y-1 data-[closed]:opacity-0',
                                'empty:invisible',
                            )}
                            transition
                            anchor={{ to: 'bottom start' }}
                        >
                            {({ option }) => (
                                <ComboboxOption
                                    value={option}
                                    className={({ focus }) =>
                                        cx(
                                            'relative w-full cursor-default select-none py-2 pr-4',
                                            focus ? 'bg-purpleGray text-primary dark:bg-dimGray dark:text-white' : 'text-gray-900 dark:text-white',
                                        )
                                    }
                                >
                                    {({ selected }) => (
                                        <p
                                            className={cx(
                                                'flex space-x-1 pl-3 hover:text-primary hover:dark:text-primaryLight',
                                                selected ? 'font-semibold text-primary dark:text-primaryLight' : '',
                                            )}
                                        >
                                            {renderOption(option)}
                                        </p>
                                    )}
                                </ComboboxOption>
                            )}
                        </ComboboxOptions>
                    </div>
                )}
            </Combobox>
            <Error error={error} />
        </div>
    );
};

export default AutocompleteVirtual;
