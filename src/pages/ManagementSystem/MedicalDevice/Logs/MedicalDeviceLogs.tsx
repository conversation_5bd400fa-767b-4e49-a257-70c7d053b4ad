import Skeleton from '@components/Skeleton/Skeleton';
import Button from '@components/form/Button';
import Pagination from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import useClient from '@hooks/useClient';
import MaterialBreadcrumbs from '@partials/Breadcrumbs/MaterialBreadcrumbs';
import React from 'react';
import { useParams } from 'react-router';
import api from '@configs/api';
import { generateClientFullName, timeZone } from '@/helper';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { ClientLogsPaginatedResponse, LogsPaginatedResponse, MedicalDevicePaginatedResponse } from '@interface/common';
import { Client } from '@interface/model/client';
import strings from '@lang/Lang';
import DownloadIcon from '@partials/Icons/Download';
import Card from '@partials/Paper/PagePaper';
import Table from '@partials/Table/PageTable';
import PageTableBody from '@partials/Table/PageTableBody';
import PageTableHead from '@partials/Table/PageTableHead';
import PageTableTH from '@partials/Table/PageTableTH';
import PageTableTHSort from '@partials/Table/PageTableTHSort';

import MedicalDeviceLogListItem from './MedicalDeviceLogListItem';
import { MedicalDeviceManagement } from '@interface/model/medicalDevice';

export interface ClientLogsProps {}

async function download(id?: string) {
    const response = await fetch(api.management_device.logsDownload(id), {
        method: 'GET',
        headers: {
            Accept: 'application/json',
            'X-App-Locale': strings.getLanguage(),
            'X-Time-Zone': timeZone(),
        },
        credentials: 'include',
    });

    const data = await response.blob();
    const a = document.createElement('a');
    a.href = window.URL.createObjectURL(data);
    a.setAttribute('download', `${strings.medical_device} logs.pdf`);
    a.click();
}

const MedicalDeviceLogs: React.FC<ClientLogsProps> = () => {
    const { medicalDeviceId } = useParams();

    const { data, page, setPage, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<LogsPaginatedResponse, Error>(
        api.management_device.logs(medicalDeviceId),
    );

    const [downloading, setDownloading] = React.useState(false);

    return (
        <div className="mt-4">
            <div className="mb-4">
                <MaterialBreadcrumbs />
            </div>
            <Card>
                <Heading text={`${strings.medical_device} ${strings.logs}`} variant="headingTitle" className="mb-4" />
                <Table>
                    <PageTableHead>
                        <PageTableTH>{strings.Details}</PageTableTH>
                        <PageTableTHSort sort={orderBy === 'created_at' && orderDirection} onClick={() => handleOrder('created_at')}>
                            {strings.DateTime}
                        </PageTableTHSort>
                    </PageTableHead>
                    <PageTableBody>
                        {data?.data.map((log) => <MedicalDeviceLogListItem key={log.id} log={log} />)}
                        {loading && <ClientLogsSkeleton limit={10} />}
                    </PageTableBody>
                </Table>
                <div className="mt-4 flex justify-between">
                    {/* <Button variant="outlined" size="small" loading={downloading} onClick={onDownload}>
                        <DownloadIcon className="mr-2 text-lg" />
                        {strings.Download}
                    </Button> */}
                    <Pagination
                        pageSize={data?.per_page ?? 0}
                        totalCount={data?.total ?? 0}
                        currentPage={page}
                        onPageChange={(page) => setPage(page)}
                    />
                </div>
            </Card>
        </div>
    );

    async function onDownload() {
        if (downloading) return;
        setDownloading(true);
        await download(medicalDeviceId);
        setDownloading(false);
    }
};

function ClientLogsSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" delay={key * 100} />
                            </div>
                        </td>
                        <td>
                            <div className="flex items-center">
                                <Skeleton className="mr-4 h-10 w-full cursor-wait" delay={key * 100} />
                            </div>
                        </td>
                    </tr>
                );
            })}
        </>
    );
}

export default MedicalDeviceLogs;
