import CalendarSelect from '@components/Calendar/Custom/CalendarSelect';
import Button from '@components/form/Button';
import TimePicker from '@components/Teams/Booking/TimePicker';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import { QuestionnerWithCustomDateTime } from '@interface/questionary';
import strings from '@lang/Lang';
import FullPageError from '@partials/Error/FullPageError';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import Modal from '@partials/MaterialModal/Modal';
import DynamicQuestionaryComponent from '@partials/Qestionary/DynamicQuestionaryComponent';
import { useHours } from '@provider/TimeProvider';
import { Formik } from 'formik';
import * as React from 'react';
import { useNavigate, useParams } from 'react-router';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import config from '../../../../../config';
import { timeZone } from '../../../../../helper';
import { QuestionaryQuestionResponse } from '../../../../../interfaces/common';
import { Questionary } from '../../../../../interfaces/model/questionary';
import { validateClientDynamicQuestionnaire } from '../../../../../validations';
import { SectionLoading } from '@partials/Loadings/SectionLoading';

interface IClientDynamicQuestionariesModalProps {
    openModal?: boolean;
    setOpenModal?: React.Dispatch<React.SetStateAction<boolean>>;
    mutate?: () => Promise<any>;
    selectedQuestionary?: Questionary;
}

export interface DynamicQuestionaryValuesData {
    question: string;
    type: string;
    files?: FileList;
    value?: 0 | 1 | '' | null | '0' | '1' | string;
    text?: string | null;
}

export interface DynamicQuestionaryValues extends QuestionnerWithCustomDateTime {
    data: DynamicQuestionaryValuesData[];
    server?: string;
}

const ClientDynamicQuestionariesModal: React.FC<IClientDynamicQuestionariesModalProps> = ({
    openModal = false,
    setOpenModal = () => {},
    mutate = async () => {},
    selectedQuestionary,
}) => {
    const navigate = useNavigate();
    const { clientId }: { clientId?: string } = useParams();
    const { dateFormat, parseDate } = useHours();

    const { data: questionnaireQuestionData, error } = useSWR<QuestionaryQuestionResponse, Error>(
        api.questionnaireQuestions.replace(':questionary', selectedQuestionary?.id.toString() || ''),
    );

    const loading = !questionnaireQuestionData && !error;

    if (error && selectedQuestionary) {
        return <FullPageError message={error.message || 'server error'} code={error.status || 500} />;
    }

    return (
        <Formik<DynamicQuestionaryValues>
            initialValues={
                selectedQuestionary
                    ? {
                          data:
                              questionnaireQuestionData?.data.map((question, index) => {
                                  if (question.type === config.questionTypes1.yes_no_textbox.value) {
                                      return {
                                          question: question.question,
                                          type: question.type,
                                          text:
                                              selectedQuestionary?.data && selectedQuestionary.data?.formatted_response[index]?.text
                                                  ? selectedQuestionary.data.formatted_response[index]?.text
                                                  : '',
                                          value:
                                              selectedQuestionary?.data && selectedQuestionary.data?.formatted_response[index]?.value
                                                  ? selectedQuestionary.data.formatted_response[index]?.value === 'yes'
                                                      ? 1
                                                      : 0
                                                  : '',
                                      };
                                  }

                                  if (question.type === config.questionTypes1.yes_no.value) {
                                      return {
                                          question: question.question,
                                          type: question.type,
                                          value:
                                              selectedQuestionary?.data && selectedQuestionary.data?.formatted_response[index]?.value
                                                  ? selectedQuestionary.data.formatted_response[index]?.value === 'yes'
                                                      ? 1
                                                      : 0
                                                  : '',
                                          text: '',
                                      };
                                  }

                                  if (question.type === config.questionTypes1.textbox.value) {
                                      return {
                                          question: question.question,
                                          type: question.type,
                                          text:
                                              typeof selectedQuestionary.data?.formatted_response[index] === 'string'
                                                  ? (selectedQuestionary.data.formatted_response[index] as string)
                                                  : '',
                                      };
                                  }

                                  if (question.type === config.questionTypes1.file_upload.value) {
                                      return {
                                          question: question.question,
                                          type: question.type,
                                      };
                                  }

                                  return {
                                      question: question.question,
                                      type: question.type,
                                      text: '',
                                  };
                              }) || [],
                      }
                    : {
                          data: [],
                      }
            }
            enableReinitialize
            validate={(values) => validateClientDynamicQuestionnaire(values, questionnaireQuestionData?.data || [], dateFormat)}
            onSubmit={async (values, { setSubmitting, resetForm, setFieldError }) => {
                const formData = new FormData();
                formData.set('client_id', clientId!);
                formData.set('is_custom_date_selected', `${values.is_custom_date_selected ?? ''}`);
                formData.set('date', values.date ? parseDate(values.date).format('YYYY-MM-DD') : '');
                formData.set('time', values.time ?? '');

                for (let index = 0; index < values.data.length; index++) {
                    const data = values.data[index];
                    const keys = Object.keys(data);
                    const hasAnswer = keys.includes('value');
                    if (hasAnswer) {
                        formData.set(`data[${index}][value]`, data.value === 1 ? 'yes' : data.value === 0 ? 'no' : '');
                    }

                    if (data.files?.length) {
                        for (let i = 0; i < data.files.length; i++) {
                            formData.set(`data[${index}][files][${i}]`, data.files[i]);
                            formData.set(`data[${index}][file_names][${i}]`, data.files[i].name);
                        }
                    } else {
                        formData.set(hasAnswer ? `data[${index}][text]` : `data[${index}]`, data.text ?? '');
                    }
                }

                const response = await fetch(api.questionnaireAnswerCreate.replace(':questionary', selectedQuestionary?.id.toString() || ''), {
                    method: 'POST',
                    headers: {
                        'X-App-Locale': strings.getLanguage(),
                        'X-Time-Zone': timeZone(),
                        Accept: 'application/json',
                    },
                    credentials: 'include',
                    body: formData,
                });
                if (response.status === 401) {
                    navigate('/');
                }
                const data = await response.json();
                if (data.status !== '1') {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                } else {
                    toast.success(data.message || 'dynamic questionary updated successfully.');
                    await resetForm();
                    await mutate();
                    setOpenModal(false);
                }
                setSubmitting(false);
            }}
        >
            {({ errors, handleSubmit, dirty, isSubmitting, isValidating, resetForm, setFieldValue, values }) => {
                const handleModelClose = async () => {
                    if (isSubmitting || isValidating) return;
                    setOpenModal(false);
                    await resetForm();
                };

                return (
                    <Modal
                        size="large"
                        open={openModal}
                        title={selectedQuestionary?.title || ''}
                        handleClose={handleModelClose}
                        cancelButton={
                            <CancelButton disabled={!!isSubmitting || loading} onClick={handleModelClose}>
                                {strings.Cancel}
                            </CancelButton>
                        }
                        submitButton={
                            <Button
                                loading={!!isSubmitting}
                                disabled={!!isSubmitting || loading}
                                onClick={() => {
                                    if (!questionnaireQuestionData?.data?.length) {
                                        toast.success(strings.no_data_changed);
                                        handleModelClose();
                                        return;
                                    }

                                    return handleSubmit();
                                }}
                            >
                                {strings.Submit}
                            </Button>
                        }
                    >
                        <FormikErrorFocus />
                        <div className="p-4">
                            <div className="mb-2 grid grid-flow-row gap-4">
                                <div className="flex w-full flex-col items-start gap-x-6 gap-y-3 pt-2">
                                    <div className="">
                                        <Checkbox
                                            label={strings.use_custom_date_time}
                                            value={values.is_custom_date_selected}
                                            name="is_custom_date_selected"
                                            onChange={(ev) => setFieldValue('is_custom_date_selected', ev.target.checked ? 1 : 0)}
                                        />
                                    </div>
                                    {values.is_custom_date_selected === 1 && (
                                        <div className="flex w-full flex-col items-start gap-4 md:flex-row">
                                            <CalendarSelect
                                                selectedDate={values.date}
                                                onChange={(date) => setFieldValue('date', date)}
                                                inputProps={{
                                                    label: strings.Date,
                                                    name: 'date',
                                                }}
                                                placeholder={strings.select_date}
                                                error={errors.date}
                                            />
                                            <TimePicker
                                                label={strings.time}
                                                value={values.time}
                                                onChange={(time) => setFieldValue('time', time)}
                                                error={errors.time}
                                            />
                                        </div>
                                    )}
                                </div>
                                <DynamicQuestionaryComponent />
                                {questionnaireQuestionData?.data && !questionnaireQuestionData?.data?.length ? strings.NoQuestions : ''}
                                {loading ? <SectionLoading /> : ''}
                                <ServerError error={errors?.server} className="mt-4" />
                            </div>
                        </div>
                    </Modal>
                );
            }}
        </Formik>
    );
};

export default ClientDynamicQuestionariesModal;
