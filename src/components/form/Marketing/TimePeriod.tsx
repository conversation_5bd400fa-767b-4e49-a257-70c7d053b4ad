import strings from '@lang/Lang';
import { useState } from 'react';
import { Combobox, ComboboxButton, ComboboxInput, ComboboxOptions, Transition } from '@headlessui/react';
import cx from 'classix';
import useTranslation from '@hooks/useTranslation';

export interface TimePeriodProps {
    value?: string;
    onChange: (value: string) => void;
    removeYearOption?: boolean;
}
const TimePeriod: React.FC<TimePeriodProps> = ({ value, onChange, removeYearOption }) => {
    const [language] = useTranslation();
    const timeDuration = [
        `1 ${strings.day}`,
        `1 ${strings.week.toLowerCase()}`,
        `1 ${strings.month}`,
        removeYearOption ? '' : `1 ${strings.year}`,
    ].filter(Boolean);
    const [inputValue, setInputValue] = useState<string>(value || `1 ${strings.day}`);
    const [suggestions, setSuggestions] = useState<string[]>(timeDuration);
    const units = [strings.day, strings.week.toLowerCase(), strings.month, removeYearOption ? '' : strings.year].filter(Boolean);
    const className = cx(
        'form-input block max-w-32 rounded-[4px] border-white bg-white py-1 pl-2 text-dimGray dark:border-black dark:bg-black dark:text-white',
    );

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/[-._\/+=';,.?{}!@#$%^&*():]/g, '');
        setInputValue(value);
        onChange(value);
        const number = value.split(' ')[0] || '1';
        const generatedSuggestions = units.map((unit) => `${number} ${unit}${number === '1' || language === 'sv' ? '' : 's'}`);
        setSuggestions(generatedSuggestions);
    };
    const handleSuggestionClick = (suggestion: string) => {
        setInputValue(suggestion);
        onChange(suggestion);
        setSuggestions([]);
    };

    return (
        <div className="relative">
            <Combobox value={inputValue} immediate>
                <div className="relative">
                    <div
                        className={`bg-whiborder-white rounded-lg border-white py-0 pl-0 dark:border-black dark:bg-black`}
                        onClick={() => {
                            const regex = new RegExp(`(\\d+)(?: (\\w+))?`);
                            const match = inputValue.match(regex);
                            if (match) {
                                const number = match[1];
                                const generatedSuggestions = units.map((unit) => `${number} ${unit}${number === '1' ? '' : 's'}`);
                                setSuggestions(generatedSuggestions);
                            } else {
                                setSuggestions(timeDuration);
                            }
                        }}
                    >
                        <ComboboxInput
                            autoComplete="off"
                            type="text"
                            value={inputValue}
                            className={className}
                            displayValue={(val: string) => val}
                            onChange={handleInputChange}
                        />
                    </div>
                    {suggestions.length > 0 && (
                        <Transition
                            as="div"
                            className={`absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-[4px] bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-black sm:text-sm`}
                            enter="transition-all duration-300"
                            enterFrom="top-[90%] opacity-0"
                            enterTo="top-full opacity-100"
                            leave="transition-all ease-out duration-75"
                            leaveFrom="top-full opacity-100"
                            leaveTo="top-[90%] opacity-0"
                        >
                            <ComboboxOptions>
                                {suggestions.map((suggestion, index) => {
                                    return (
                                        <div
                                            key={index}
                                            className={`w-full px-3 py-2 hover:bg-purpleGray hover:text-primary dark:hover:bg-dimGray dark:hover:text-primaryLight ${suggestion === inputValue ? 'text-primary dark:text-primaryLight' : ''}`}
                                            onClick={() => handleSuggestionClick(suggestion)}
                                        >
                                            {suggestion}
                                        </div>
                                    );
                                })}
                            </ComboboxOptions>
                        </Transition>
                    )}
                </div>
            </Combobox>
        </div>
    );
};
export default TimePeriod;
