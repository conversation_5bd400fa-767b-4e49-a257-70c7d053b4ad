import Button from '@components/form/Button';
import StepperNew from '@components/form/StepperNew';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import { LegalDocumentsResponse } from '@interface/common';
import strings from '@lang/Lang';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import { ResolverError, useForm, UseFormReturn } from 'react-hook-form';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import useSWRImmutable from 'swr/immutable';

interface SignupDocumentsProps {}

interface SignupDocumentsFormValues {
    step: number;
    document1_submitted: boolean;
    document2_submitted: boolean;
    document3_submitted: boolean;
}

type DOC_TYPES = 'PUBLIC' | 'DPA' | 'SUPPLIER';

const SignupDocuments = ({}: SignupDocumentsProps) => {
    const { mutate: reloadAuth } = useAuth();
    const { mutate: documentsMutate } = useSWR<LegalDocumentsResponse>(api.legalDocuments.list);
    const navigate = useNavigate();
    const form = useForm<SignupDocumentsFormValues>({
        defaultValues: { step: 1 },
        resolver: documentValidator,
    });

    const {
        handleSubmit,
        watch,
        setValue,
        formState: { isSubmitting, isValid },
    } = form;

    const currentStep = watch('step') ?? 1;
    const lastStep = currentStep === 3;

    return (
        <form
            onSubmit={handleSubmit(async (values) => {
                if (!lastStep) {
                    setValue('step', currentStep + 1);
                    return;
                }

                const response = await fetch(api.legalDocuments.store, {
                    method: 'POST',
                    headers: {
                        Accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-App-Locale': strings.getLanguage(),
                    },
                    credentials: 'include',
                    body: JSON.stringify(values),
                });

                const data = await response.json();

                if (data.status == '1') {
                    await documentsMutate();
                    await reloadAuth();
                    toast.success(data.message);
                    navigate('/', { replace: true });
                } else {
                    toast.error('Something went wrong, try again.');
                }
            })}
        >
            <div className="space-y-6">
                <Heading text={strings.documents} variant="bigTitle" />
                <StepperNew current={currentStep} total={3} />
                <DocumentViewer step={currentStep} form={form} />
                <Button type="submit" loading={isSubmitting} disabled={!isValid}>
                    {lastStep ? 'Submit' : 'Next'}
                </Button>
            </div>
        </form>
    );
};

interface DocumentViewerProps {
    step: number;
    form: UseFormReturn<SignupDocumentsFormValues, any, SignupDocumentsFormValues>;
}

const DocumentViewer = ({ step, form }: DocumentViewerProps) => {
    const { data, isLoading } = useSWRImmutable(api.legalDocuments.view(strings.getLanguage(), getStepName(step)));

    if (isLoading) {
        return <SectionLoading />;
    }

    return (
        <>
            <div
                className="prose max-w-full overflow-auto rounded-lg border bg-gray-50 p-4 font-sans dark:prose-invert prose-headings:my-4 prose-p:my-2 prose-a:text-blue-600 prose-hr:my-3 dark:border-gray-600 dark:bg-gray-800 lg:max-h-[80vh]"
                dangerouslySetInnerHTML={{ __html: data?.data || '' }}
            />
            <div className="flex flex-col items-start">
                <Checkbox
                    key={`document_${step}`}
                    label={strings.i_accept_this_license_agreement}
                    // @ts-ignore
                    error={Boolean(form.formState.errors[`document${step}_submitted`])}
                    // @ts-ignore
                    helperText={form.formState.errors[`document${step}_submitted`]?.message}
                    // @ts-ignore
                    {...form.register(`document${step}_submitted`)}
                />
            </div>
        </>
    );
};

const getStepName = (step: number): DOC_TYPES => {
    switch (step) {
        case 1:
            return 'PUBLIC';
        case 2:
            return 'DPA';
        default:
            return 'SUPPLIER';
    }
};

export const documentValidator = (values: SignupDocumentsFormValues): ResolverError<SignupDocumentsFormValues> => {
    const resolver: ResolverError<SignupDocumentsFormValues> = { values, errors: {} };

    if (values.step === 1 && !values.document1_submitted) {
        resolver.errors.document1_submitted = { type: 'required', message: strings.please_accept_the_consent };
    }
    if (values.step === 2 && !values.document2_submitted) {
        resolver.errors.document2_submitted = { type: 'required', message: strings.please_accept_the_consent };
    }
    if (values.step === 3 && !values.document3_submitted) {
        resolver.errors.document3_submitted = { type: 'required', message: strings.please_accept_the_consent };
    }

    return resolver;
};

export default SignupDocuments;
