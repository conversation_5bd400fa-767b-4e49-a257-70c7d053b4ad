import ScheduleCreateModal from '@components/Calendar/Schedule/ScheduleCreateModal';
import Button from '@components/form/Button';
import IconButton from '@components/form/IconButton';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import { Dialog, DialogBackdrop, DialogPanel, Transition, TransitionChild } from '@headlessui/react';
import TodayCalendarIcon from '@icons/TodayCalendar';
import strings from '@lang/Lang';
import ChevronIcon from '@partials/Icons/Chevron';
import LeftArrowCircle from '@partials/Icons/LeftArrowCircle';
import RightArrowCircle from '@partials/Icons/RightArrowCircle';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import dayjs from 'dayjs';
import { Fragment, useMemo, useState } from 'react';
import CalendarSlotIntervalSelect from '../Custom/CalendarSlotIntervalSelect';
import { useScheduleCalendar } from './ScheduleCalendarProvider';
import { CopyPasteButton } from './ScheduleCalendarWeeklyView';
import ScheduleClearModal from './ScheduleClearModal';
import ScheduleViewServicesModal from './ScheduleViewServicesModal';

export interface ScheduleCalendarHeaderProps {}

const ScheduleCalendarHeader: React.FC<ScheduleCalendarHeaderProps> = () => {
    const {
        start_date,
        end_date,
        selectedUser,
        cursorDate,
        navigation: { toPrev, toNext, setToday },
        intervalSlot,
        setIntervalSlot,
        copySchedule,
        pasteSchedule,
        clipboard,
        extraBarOpen,
        setExtraBarOpen,
        setOpenHoursModalOpen,
        swr: { data: unavailableData, mutate: mutateUnavailableDays },
    } = useScheduleCalendar();

    const weekStartDay = dayjs(start_date);
    const weekEndDay = dayjs(end_date);
    const date = dayjs(cursorDate);
    const text = useMemo(() => {
        const isSameMonth = weekStartDay.month() === weekEndDay.month();
        return `${weekStartDay.format(isSameMonth ? 'DD' : 'DD MMM')} - ${weekEndDay.format('DD MMM YYYY')}`;

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [date.format(api.dateFormat)]);

    const [saveModalOpen, setSaveModalOpen] = useState(false);
    const [clearModalOpen, setClearModalOpen] = useState(false);
    const [viewServicesModal, setViewServicesModal] = useState(false);

    return (
        <div className="pt-2">
            <ModalSuspense>
                {saveModalOpen && (
                    <ScheduleCreateModal
                        openModal={saveModalOpen}
                        setOpenCreateModal={setSaveModalOpen}
                        mutate={mutateUnavailableDays}
                        selectedUser={selectedUser}
                    />
                )}
                {clearModalOpen && (
                    <ScheduleClearModal
                        openModal={clearModalOpen}
                        setOpenClearModal={setClearModalOpen}
                        mutate={mutateUnavailableDays}
                        selectedUser={selectedUser}
                    />
                )}

                {viewServicesModal && (
                    <ScheduleViewServicesModal
                        openModal={viewServicesModal}
                        setOpenViewModal={setViewServicesModal}
                        mutate={mutateUnavailableDays}
                        selectedUser={selectedUser}
                    />
                )}
            </ModalSuspense>

            <div className="md:hidden">{mobileView()}</div>
            <div className="hidden flex-col-reverse justify-between gap-2 px-2 md:flex xl:flex-row">{desktopView()}</div>
        </div>
    );

    function desktopView() {
        return (
            <>
                <div className="mb-1 flex items-center gap-2 md:mb-0 md:mt-0">
                    <Button className="hidden 2xl:block" size="small" onClick={setToday}>
                        {strings.today}
                    </Button>
                    <IconButton className="lg:block 2xl:hidden" onClick={setToday} children={<TodayCalendarIcon />} />
                    <div className="flex items-center gap-1 p-1">
                        <IconButton onClick={toPrev}>
                            <ChevronIcon side="left" />
                        </IconButton>
                        <IconButton onClick={toNext}>
                            <ChevronIcon side="right" />
                        </IconButton>
                    </div>
                    <Heading text={text} className="!text-base md:mr-4 md:!text-xl" />
                    <CopyPasteButton
                        onCopy={() => {
                            const list = unavailableData?.data.filter((v) => v.type === 'AVAILABLE') ?? [];
                            copySchedule('weekly', weekStartDay, weekEndDay, list);
                        }}
                        onPaste={() => {
                            pasteSchedule('weekly', weekStartDay, weekEndDay);
                        }}
                        selected={
                            clipboard &&
                            clipboard.type === 'weekly' &&
                            dayjs(clipboard.start_at).format(api.dateFormat) === weekStartDay.format(api.dateFormat)
                        }
                    />
                </div>

                <div className="flex items-center justify-between gap-2">
                    <Button size="small" variant="ghost" onClick={() => setClearModalOpen(true)}>
                        <span>{strings.clear_schedule}</span>
                    </Button>

                    <Button size="small" variant="ghost" onClick={() => setViewServicesModal(true)}>
                        <span>{strings.view_services}</span>
                    </Button>
                    <Button size="small" onClick={() => setSaveModalOpen(true)}>
                        <span>{strings.create_schedule}</span>
                    </Button>
                </div>
            </>
        );
    }
    function mobileView() {
        return (
            <>
                <MobileExtraPanel />
                <div className="flex items-center justify-between gap-2 md:gap-4"></div>
                <div className="mb-1 flex items-center justify-between">
                    <Heading text={text} className="!text-base" />

                    <div className="flex justify-between gap-4">
                        <IconButton onClick={setToday} children={<TodayCalendarIcon />} />
                        <div className="flex items-center gap-2 p-1">
                            <IconButton onClick={toPrev} className="!p-1" children={<LeftArrowCircle />} />
                            <IconButton onClick={toNext} className="!p-1" children={<RightArrowCircle />} />
                        </div>
                    </div>
                </div>
            </>
        );
    }

    function MobileExtraPanel() {
        return (
            <Dialog
                open={extraBarOpen}
                onClose={() => setExtraBarOpen(false)}
                className="fixed inset-0 right-0 z-50 h-screen w-screen overflow-hidden"
            >
                <DialogBackdrop className="fixed inset-0 bg-black/30" />
                <DialogPanel className="fixed right-0 top-0 h-screen w-3/4 bg-white px-6 py-8 shadow-card dark:bg-dimGray">
                    <div className="flex flex-col items-start space-y-4">
                        <Button
                            size="small"
                            variant="outlined"
                            onClick={() => {
                                setExtraBarOpen(false);
                                setOpenHoursModalOpen(true);
                            }}
                        >
                            {strings.open_hours}
                        </Button>
                        <CalendarSlotIntervalSelect
                            selected={intervalSlot.id}
                            onChange={(id) => {
                                setIntervalSlot(id);
                            }}
                        />
                        <CopyPasteButton
                            onCopy={() => {
                                const list = unavailableData?.data.filter((v) => v.type === 'AVAILABLE') ?? [];
                                copySchedule('weekly', weekStartDay, weekEndDay, list);
                            }}
                            onPaste={() => {
                                pasteSchedule('weekly', weekStartDay, weekEndDay);
                            }}
                            selected={
                                clipboard &&
                                clipboard.type === 'weekly' &&
                                dayjs(clipboard.start_at).format(api.dateFormat) === weekStartDay.format(api.dateFormat)
                            }
                        />
                        <div className="flex flex-wrap gap-4">
                            <Button
                                size="small"
                                variant="ghost"
                                onClick={() => {
                                    setExtraBarOpen(false);
                                    setClearModalOpen(true);
                                }}
                            >
                                <span>{strings.clear_schedule}</span>
                            </Button>

                            <Button
                                size="small"
                                variant="ghost"
                                onClick={() => {
                                    setExtraBarOpen(false);
                                    setViewServicesModal(true);
                                }}
                            >
                                <span>{strings.view_services}</span>
                            </Button>
                            <Button
                                size="small"
                                onClick={() => {
                                    setExtraBarOpen(false);
                                    setSaveModalOpen(true);
                                }}
                            >
                                <span>{strings.create_schedule}</span>
                            </Button>
                        </div>
                    </div>
                </DialogPanel>
            </Dialog>
        );
    }
};

export default ScheduleCalendarHeader;
