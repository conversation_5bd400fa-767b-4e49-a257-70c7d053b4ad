import Avatar from '@components/avatar/Avatar';
import Card from '@components/card';
import Button from '@components/form/Button';
import StarRating from '@components/form/StarRating';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import GoogleRatingIcon from '@icons/GoogleRating';
import LoadingIcon from '@icons/Loading';
import { GoogleRatingsResponse } from '@interface/common';
import { Company } from '@interface/model/company';
import strings from '@lang/Lang';
import EditIcon from '@partials/Icons/Edit';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import React from 'react';
import useSWR from 'swr';
import GoogleRatingRemoveModal from './GoogleRatingRemoveModal';
const CompanyRatingModal = React.lazy(() => import('./GoogleRatingsModal'));

export interface GoogleRatingsProps {
    company?: Company;
}
const GoogleRatings: React.FC<GoogleRatingsProps> = ({ company }) => {
    const [openRatingModal, setOpenRatingModal] = React.useState(false);
    const [removeModal, setRemoveModal] = React.useState(false);
    const { data, isLoading, mutate } = useSWR<GoogleRatingsResponse, Error>(api.googlePlaces.show(company?.encrypted_id));

    return (
        <Card removeShadow>
            <ModalSuspense>
                {openRatingModal && (
                    <CompanyRatingModal
                        openModal={openRatingModal}
                        handleModalClose={() => setOpenRatingModal(false)}
                        googleRatingsDetails={data?.data?.place_id ? data?.data : undefined}
                        company={company}
                        mutate={mutate}
                    />
                )}
                {removeModal && (
                    <GoogleRatingRemoveModal
                        openModal={removeModal}
                        handleClose={() => setRemoveModal(false)}
                        mutate={mutate}
                        googleRatingsDetails={data?.data?.place_id ? data?.data : undefined}
                    />
                )}
            </ModalSuspense>
            <div className="flex items-center justify-between">
                <Heading text={strings.google_ratings} variant="subTitle" />
                {data?.data?.place_id && (
                    <Button variant="ghost" size="small" color="red" onClick={() => setRemoveModal(true)}>
                        {strings.remove}
                    </Button>
                )}
            </div>
            <div className="space-y-5">
                {isLoading && (
                    <div className="flex items-center justify-center py-9">
                        <SectionLoading />
                    </div>
                )}
                {!isLoading &&
                    (data?.data?.place_id ? (
                        <div className="space-y-4">
                            <p className="text-lg font-semibold">{data?.data?.name ?? ''}</p>
                            <div className="flex items-center gap-2">
                                <p>{parseFloat(data?.data?.rating ?? '0').toFixed(1)}</p>
                                <StarRating rating={parseFloat(data?.data?.rating ?? '0')} size="small" />
                                <a
                                    className="text-primary dark:text-primaryLight"
                                    href={data?.data?.google_maps_links?.reviewsUri}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    ({(data?.data?.user_rating_count ?? 0) + ' ' + strings.reviews.toLowerCase()})
                                </a>
                            </div>
                            <hr className="" />
                            <div>
                                <p className="font-medium">{strings.reviews}</p>
                                <div className="soft-searchbar max-h-96 space-y-3 divide-y-2 overflow-y-auto dark:divide-gray-600">
                                    {data?.data?.reviews?.map((review) => {
                                        return (
                                            <div className="flex gap-3 pt-3">
                                                <Avatar className="h-9 w-9" />
                                                <div className="space-y-1">
                                                    <p className="font-semibold">{review?.authorAttribution?.displayName}</p>
                                                    <div className="flex gap-2">
                                                        <p>{(review?.rating ?? 0).toFixed(1)}</p>
                                                        <StarRating rating={review?.rating || 0} size="small" />
                                                        <span>{review?.relativePublishTimeDescription}</span>
                                                    </div>
                                                    <div
                                                        className="prose whitespace-pre-wrap break-words dark:prose-invert"
                                                        dangerouslySetInnerHTML={{
                                                            __html: review?.originalText?.text ?? '',
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                    {!data?.data?.reviews?.length && <p className="py-3 text-mediumGray">{strings.no_data}</p>}
                                </div>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-center">
                                <GoogleRatingIcon className="text-8xl" />
                            </div>
                            <p className="text-center">{strings.google_ratings_note}</p>
                            <div className="flex justify-center">
                                <Button size="small" onClick={() => setOpenRatingModal(true)}>
                                    {strings.fetch_ratings}
                                </Button>
                            </div>
                        </>
                    ))}
            </div>
        </Card>
    );
};

export default GoogleRatings;
