import Modal from '@partials/MaterialModal/Modal';
import Table from '@partials/Table/PageTable';
import { DashbordTableComponentProps } from './DashbordTableComponent';

export interface DashbordTableViewAllModalProps<T> extends DashbordTableComponentProps<T> {
    openModal: boolean;
    onClose: () => void;
}

function DashbordViewAllModal<T>({ openModal, onClose, headings, data, renderColumn, title }: DashbordTableViewAllModalProps<T>) {
    
    return (
        <Modal open={openModal} title={title} handleClose={onClose} size="medium">
            <div className="p-4 pt-2">
                <Table>
                    <Table.Head>
                        {headings.map((heading, index) => (
                            <Table.Td key={index}>{heading}</Table.Td>
                        ))}
                    </Table.Head>
                    <Table.Body>
                        {data?.map((item, index) => (
                            <tr key={index}>
                                {headings.map((heading, colIndex) => (
                                    <Table.Td key={colIndex}>{renderColumn(item, colIndex, index)?.at(colIndex)}</Table.Td>
                                ))}
                            </tr>
                        ))}
                    </Table.Body>
                </Table>
            </div>
        </Modal>
    );
}

export default DashbordViewAllModal;
