import LoginSideImage from '@components/auth/LoginSideImage';
import PrescriptionLoginTermsText from '@components/auth/PrescriptionLoginTermsText';
import Button from '@components/form/Button';
import InfoCard from '@components/form/InfoCard';
import Input from '@components/form/Input';
import InputPassword from '@components/form/InputPassword';
import RouterLink from '@components/link/RouterLink';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import useLocalStorage from '@hooks/useLocalStorage';
import strings from '@lang/Lang';
import PrescriptionLogoWithName from '@partials/Icons/PrescriptionLogoWithName';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import { DeviceTokens, LoginFormValues, loginValidation } from '@validations/login';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

const PrescriptionLogin = () => {
    const { loginUser, mutate } = useAuth();

    const { storedValue: deviceTokens, setStorageValue: setDeviceTokens } = useLocalStorage<DeviceTokens[]>('device_tokens', []);
    const [verify, setVerify] = useState(false);

    const [resend, setResend] = useState(0);
    const [resendLoading, setResendLoading] = useState(false);
    const timer = () => setResend((val) => val - 1);
    const { setStorageValue: setUpgradeModalShown } = useLocalStorage('upgrade_plan_showed', false);
    const { setStorageValue: setPersonalIdModalShown } = useLocalStorage('duplicate_personal_Id_showed', false);
    const { setStorageValue: setPasswordExpirationModalShown } = useLocalStorage('password_expiration_modal_shown', false);
    const { setStorageValue: setIsPrescriber } = useLocalStorage('is_prescriber', false);

    useEffect(() => {
        if (!verify || resend <= 0) return;
        const id = setInterval(timer, 1000);

        return () => clearInterval(id);
    }, [resend, verify]);
    const navigate = useNavigate();

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        getValues,
        setError,
    } = useForm<LoginFormValues>({
        resolver: loginValidation,
    });

    return (
        <div className="flex h-[100svh] w-full items-end justify-center bg-white dark:bg-black sm:items-center">
            <LoginSideImage />
            <div className="z-10 mx-8 mb-16 w-full max-w-md rounded-2xl bg-white px-8 pt-8 dark:bg-black md:mb-12 lg:rounded-none lg:pt-12 xl:max-w-xl xl:px-16">
                <PrescriptionLogoWithName />
                <form
                    className="space-y-5 py-6"
                    onSubmit={handleSubmit(async (values) => {
                        const isTokensValid = Array.isArray(deviceTokens);
                        const my2faStored = isTokensValid ? deviceTokens.find((deviceToken) => deviceToken.email === values.email) : undefined;
                        const my2faSaved = my2faStored?.save === true;

                        const data = await loginUser({
                            ...values,
                            device_token: !verify && !my2faSaved ? undefined : my2faStored?.token || undefined,
                            platform: 'PRESCRIPTION',
                        });

                        if (!data) {
                            setError('server', { message: 'Something went wrong. Please try again.' });
                            return;
                        }

                        setUpgradeModalShown(false);
                        setPersonalIdModalShown(false);
                        if (data.status === '4') {
                            setVerify(true);
                            setResend(69);
                            if (isTokensValid) {
                                const hasToken = deviceTokens.find((deviceToken) => deviceToken.email === values.email);
                                const newDeviceTokens = hasToken
                                    ? deviceTokens.map((deviceToken) => {
                                          if (deviceToken.email === values.email) {
                                              return {
                                                  email: deviceToken.email,
                                                  token: data?.data?.device_token || '',
                                                  save: deviceToken.save,
                                              };
                                          }
                                          return deviceToken;
                                      })
                                    : [...deviceTokens, { email: values.email, token: data?.data?.device_token || '', save: false }];
                                setDeviceTokens(newDeviceTokens);
                            } else {
                                setDeviceTokens([{ email: values.email, token: data?.data?.device_token || '', save: false }]);
                            }
                            toast.success(data.message || 'otp send to your email.');
                        }
                        if (data.status === '2' || data.status === '3') {
                            setError('server', { message: data.message });
                        }

                        if (data.status === '1') {
                            setPasswordExpirationModalShown(false);
                            setIsPrescriber(true);
                            if (verify) {
                                setDeviceTokens(
                                    deviceTokens?.map((token) => (token.email === values.email ? { ...token, save: values.save_2fa } : token)),
                                );
                            }
                            await mutate();
                            navigate('/');
                            sessionStorage.removeItem('loggedOutShown');
                            toast.success(data.message || 'logged in successfully.');
                        }
                        if (data.status === '0') {
                            setError('server', { message: data.message });
                        }
                    })}
                >
                    <Input placeholder={strings.Email} label={strings.Email} type="email" {...register('email')} error={errors.email?.message} />
                    <div>
                        <InputPassword
                            placeholder={strings.Password}
                            label={strings.Password}
                            {...register('password')}
                            error={errors.password?.message}
                        />
                        <p className="mt-1 text-right text-sm">
                            <RouterLink to="/prescription/forgot-password">{strings.login_forgot_password}?</RouterLink>
                        </p>
                    </div>
                    {verify && (
                        <div className="pb-0">
                            <Input
                                label={strings.verification_code_in_email}
                                type="text"
                                inputMode="numeric"
                                autoComplete="one-time-code"
                                pattern="\d{4}"
                                placeholder="XXXX"
                                {...register('otp')}
                                error={errors.otp?.message}
                            />
                            {resend === 0 ? (
                                <ResendButton
                                    disabled={resendLoading}
                                    onClick={() => {
                                        const currentDevice = deviceTokens?.find((deviceToken) => deviceToken.email === getValues().email);
                                        if (Array.isArray(deviceTokens) && currentDevice) {
                                            setResendLoading(true);
                                            fetch(api.otpResend, {
                                                method: 'POST',
                                                headers: {
                                                    Accept: 'application/json',
                                                    'Content-Type': 'application/json',
                                                    'X-App-Locale': strings.getLanguage(),
                                                },
                                                credentials: 'include',
                                                body: JSON.stringify({
                                                    device_token: currentDevice?.token || undefined,
                                                }),
                                            })
                                                .then((response) => response.json())
                                                .then((data) => {
                                                    setResendLoading(false);
                                                    if (data.status === '4') {
                                                        setVerify(true);
                                                        setResend(69);
                                                        toast.success(data.message || 'otp send to your email.');
                                                    }
                                                    if (data.status === '0') {
                                                        setError('server', { message: data.message || undefined });
                                                    }
                                                });
                                        }
                                    }}
                                />
                            ) : (
                                <ResendIn resend={resend} />
                            )}
                            <Checkbox label={strings.dont_ask_again_on_this_device} {...register('save_2fa')} />
                        </div>
                    )}
                    <div className="pb-4">
                        <PrescriptionLoginTermsText />
                    </div>
                    <InfoCard variant="error" message={errors.server?.message} />
                    <Button fullWidth type="submit" loading={isSubmitting}>
                        {strings.LoginNow}
                    </Button>
                    <p className="text-center font-medium">
                        {strings.formatString(
                            strings.prescription_registration_text,
                            <span className="text-primary dark:text-primaryLight"><EMAIL></span>,
                        )}
                    </p>
                </form>
            </div>
        </div>
    );
};

export function ResendIn({ resend }: { resend?: number }) {
    return (
        <div className="text-right">
            <button className="text-sm text-gray-500 dark:text-gray-400" type="button" disabled>
                resend code in <span className="tabular-nums">{resend}</span>
            </button>
        </div>
    );
}

export function ResendButton({ disabled, onClick }: { disabled: boolean; onClick: () => void }) {
    return (
        <div className="text-right">
            <button
                className="text-sm text-primary disabled:text-gray-400 dark:text-primaryLight"
                type="button"
                disabled={disabled}
                onClick={onClick}
            >
                resend code?
            </button>
        </div>
    );
}

export default PrescriptionLogin;
