import Skeleton from '@components/Skeleton/Skeleton';
import Input from '@components/form/Input';
import TextArea from '@components/form/TextArea';
import api from '@configs/api';
import Settings from '@configs/settings';
import useAuth from '@hooks/useAuth';
import useTranslation from '@hooks/useTranslation';
import CopyIcon from '@icons/Copy';
import { SettingResponse } from '@interface/common';
import strings from '@lang/Lang';
import Checkbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import Switch from '@partials/MaterialSwitch';
import { useFormikContext } from 'formik';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import {
    BookingSettingBackGroungSectionItem,
    BookingSettingHeaderItem,
    BookingSettingTitleDescriptionItem,
    IBookingSettingValues,
} from '../BookingSettings';

export interface PortalProps {}

const Portal: React.FC<PortalProps> = () => {
    const { data: settingData, error } = useSWR<SettingResponse, Error>(api.setting);
    const loading = !settingData && !error;
    const { user } = useAuth();
    const [language] = useTranslation();

    const { errors, values, touched, setFieldValue, setFieldTouched, handleBlur, handleChange } = useFormikContext<IBookingSettingValues>();

    const PortalUrl = `${window.location.origin || ''}/booking/${user?.company?.encrypted_id.toString()}?lang=${language}`;
    const iFrameText = `<iframe width="100%" height="100%" style="min-height: 560px;" src="${PortalUrl}" title="Meridiq Booking System" frameborder="0" allowfullscreen></iframe>`;

    const onCopyClick = () => {
        navigator.clipboard
            .writeText(iFrameText)
            .then(() => {
                toast.success(strings.copiedToClipboard);
            })
            .catch(() => {
                toast.error(strings.failedToCopy);
            });
    };

    return (
        <div className="space-y-4">
            <BookingSettingHeaderItem title={strings.booking_policy} />
            <BookingSettingBackGroungSectionItem className="grid grid-flow-row grid-cols-1 gap-4 md:grid-cols-3">
                <BookingSettingSwitchItem
                    title={strings.magnetic_booking}
                    description={strings.magnetic_booking_desc}
                    keyName={Settings.MAGNETIC_BOOKING_ENABLED as any}
                />
            </BookingSettingBackGroungSectionItem>
            {loading ? (
                <Skeleton className="h-10 w-96" />
            ) : (
                <BookingSettingBackGroungSectionItem>
                    <div className="grid max-w-xl grid-flow-row gap-3">
                        <Checkbox
                            key="booking_policy_link_selected_off"
                            label={strings.use_text_below}
                            checked={values.booking_policy_link_selected === false}
                            name="booking_policy_link_selected"
                            onChange={async () => {
                                await setFieldValue('booking_policy_link_selected', false);
                                setFieldTouched('booking_policy_link_selected');
                            }}
                        />
                        <TextArea
                            placeholder={strings.enter_your_personal_data}
                            rows={8}
                            color="white"
                            value={values.booking_policy}
                            name="booking_policy"
                            disabled={values.booking_policy_link_selected === true}
                            onChange={handleChange}
                            onBlur={handleBlur}
                        >
                            {values.booking_policy}
                        </TextArea>
                        <Checkbox
                            key="booking_policy_link_selected_on"
                            label={strings.paste_your_own_url}
                            checked={values.booking_policy_link_selected === true}
                            name="booking_policy_link_selected"
                            onChange={async () => {
                                await setFieldValue('booking_policy_link_selected', true);
                                setFieldTouched('booking_policy_link_selected');
                            }}
                        />
                        <Input
                            name="booking_policy_link"
                            value={values.booking_policy_link}
                            onChange={handleChange}
                            color="white"
                            disabled={values.booking_policy_link_selected === false}
                            placeholder={strings.enter_url}
                        />
                    </div>
                </BookingSettingBackGroungSectionItem>
            )}
            <div className="space-y-4">
                <div className="space-y-1">
                    <BookingSettingHeaderItem title={strings.booking_widget} />
                    <h3 className="pb-1 text-sm text-mediumGray">{strings.booking_widget_note}</h3>
                </div>
                <BookingSettingBackGroungSectionItem>
                    <div className="relative grid max-w-xl grid-flow-row gap-2 pt-2">
                        <TextArea name="booking_widget" readOnly rows={5} value={iFrameText} color="white">
                            {iFrameText}
                        </TextArea>
                        <button onClick={onCopyClick}>
                            <p className="flex items-center space-x-4">
                                <CopyIcon className="absolute bottom-4 right-2.5 text-lg text-primary dark:text-primaryLight" />
                            </p>
                        </button>
                    </div>
                </BookingSettingBackGroungSectionItem>
            </div>
        </div>
    );
    function BookingSettingSwitchItem({ title, description, keyName }: { title: string; description: string; keyName: keyof IBookingSettingValues }) {
        return (
            <>
                <BookingSettingTitleDescriptionItem title={title} description={description} />
                <Switch
                    checked={values[keyName] === true}
                    onChange={(checked) => {
                        setFieldTouched(keyName);
                        setFieldValue(keyName, checked);
                    }}
                />
            </>
        );
    }
};

export default Portal;
