import { generateClientFullName } from '@/helper';
import Card from '@components/card';
import ReportIconItem from '@components/card/ReportMetricCard';
import AreaChartComponent from '@components/Charts/AreaChart';
import BarChartComponent, { BarChartData } from '@components/Charts/BarChart';
import BarHorizontalChartComponent from '@components/Charts/BarHorizontalChart';
import PieChartComponent from '@components/Charts/PieChart';
import Heading from '@components/heading/Heading';
import ReportTableComponent from '@components/Reports/ReportTableComponent';
import ReportTitleValueComponent from '@components/Reports/ReportTitleValueComp';
import api from '@configs/api';
import ClientsIcon from '@icons/Clients';
import PenDrawIcon from '@icons/PenDraw';
import ProcedureIcon from '@icons/Procedure';
import SMSEnvelopeIcon from '@icons/SMSEnvelope';
import { CommonModelResponse } from '@interface/common';
import { Client } from '@interface/model/client';
import strings from '@lang/Lang';
import ReportsProvider, { useReports } from '@provider/ReportsProvider';
import dayjs from 'dayjs';
import { FC, useMemo } from 'react';
import useSWRImmutable from 'swr/immutable';

export interface RecordReportsProps {}

export type RecordReportAPITypes =
    | 'client_statistics'
    | 'new_client_registrations'
    | 'procedures_performed'
    | 'most_procedures'
    | 'most_notes'
    | 'top_questionnaires'
    | 'client_age'
    | 'prescriptions_signed';

interface RecordReportResponse {
    total_clients: number;
    total_procedures: number;
    prescriptions_signed: number;
    sms_sent: number;
}

const today = dayjs();

const RecordReports: FC<RecordReportsProps> = ({}) => {
    const { endDate, period, startDate, selectedPractitioners } = useReports();

    const { data: statsResponse, isLoading: isStatsLoading } = useSWRImmutable<CommonModelResponse<RecordReportResponse>>(
        api.reports.record('client_statistics', startDate, endDate, period, selectedPractitioners),
    );
    const { data: clientRegistrationResponse } = useSWRImmutable<CommonModelResponse<BarChartData[]>>(
        api.reports.record('new_client_registrations', startDate, endDate, period, selectedPractitioners),
    );
    const clientRegistrationTotalCount = clientRegistrationResponse?.data.reduce((acc, curr) => acc + curr.value, 0);

    const { data: proceduresResponse } = useSWRImmutable<CommonModelResponse<BarChartData[]>>(
        api.reports.record('procedures_performed', startDate, endDate, period, selectedPractitioners),
    );
    const proceduresTotalCount = proceduresResponse?.data.reduce((acc, curr) => acc + curr.value, 0);

    const { data: clientAgeResponse } = useSWRImmutable<CommonModelResponse<Client[]>>(
        api.reports.record('client_age', startDate, endDate, period, selectedPractitioners),
    );
    const { data: prescriptionsSignedResponse } = useSWRImmutable<
        CommonModelResponse<{ prescriptions_by_mal: number; prescriptions_by_own: number }>
    >(api.reports.record('prescriptions_signed', startDate, endDate, period, selectedPractitioners));

    const ageMap = useMemo(() => {
        const map = [
            { name: '<20', value: 0, min: 0, max: 20 },
            { name: '21-30', value: 0, min: 21, max: 30 },
            { name: '31-40', value: 0, min: 31, max: 40 },
            { name: '41-50', value: 0, min: 41, max: 50 },
            { name: '51-60', value: 0, min: 51, max: 60 },
            { name: '61+', value: 0, min: 61, max: 199 },
        ];
        clientAgeResponse?.data
            .map((client) => {
                if (client.personal_id) {
                    return dayjs(client?.personal_id.split('-')?.at(0), 'YYYYMMDD', true);
                } else if (client.cpr_id) {
                    const d = dayjs(client.cpr_id.split('-')?.at(0), 'DDMMYY', true);
                    if (d.year() > today.year()) {
                        return d.subtract(100, 'years');
                    }
                    return d;
                } else if (client.social_security_number) {
                    return dayjs(client?.social_security_number, api.dateFormat, true);
                }
            })
            .map((d) => today.diff(d, 'years'))
            .map((age) => {
                map.map((a) => {
                    if (age >= a.min && age <= a.max) {
                        a.value += 1;
                    }
                });
                return age;
            });
        return map;
    }, [clientAgeResponse]);

    return (
        <div className="grid grid-cols-2 gap-4 lg:grid-cols-4">
            <ReportIconItem
                color="primary"
                icon={<ClientsIcon />}
                value={statsResponse?.data.total_clients ?? 0}
                title={strings.reports_strings.total_clients}
                loading={isStatsLoading}
            />
            <ReportIconItem
                color="yellow"
                icon={<ProcedureIcon />}
                value={statsResponse?.data.total_procedures ?? 0}
                title={strings.reports_strings.total_procedure}
                loading={isStatsLoading}
            />
            <ReportIconItem
                color="green"
                icon={<PenDrawIcon />}
                value={statsResponse?.data.prescriptions_signed ?? 0}
                title={strings.reports_strings.prescription_signed}
                loading={isStatsLoading}
            />
            <ReportIconItem
                color="orange"
                icon={<SMSEnvelopeIcon />}
                value={statsResponse?.data.sms_sent ?? 0}
                title={strings.reports_strings.sms_sent}
                loading={isStatsLoading}
            />

            <Card className="col-span-2">
                <ReportTitleValueComponent title={strings.reports_strings.new_client_registrations} value={clientRegistrationTotalCount} />
                <BarChartComponent data={clientRegistrationResponse?.data ?? []} labelName={strings.Clients} color="primary" />
            </Card>

            <Card className="col-span-2">
                <ReportTitleValueComponent title={strings.reports_strings.procedure_performed} value={proceduresTotalCount} />
                <AreaChartComponent data={proceduresResponse?.data ?? []} labelName={strings.Procedures} />
            </Card>

            <ReportTableComponent<BarChartData>
                api={api.reports.record('top_questionnaires', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.TITLE, strings.completed]}
                renderColumn={(item, index, rowIndex) => [rowIndex + 1, renderQuestionaryName(item.name), item.value]}
                title={strings.reports_strings.top_questionnaires}
            />
            <ReportTableComponent<Client>
                api={api.reports.record('most_procedures', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.TITLE, strings.completed]}
                renderColumn={(item, index, rowIndex) => [rowIndex + 1, generateClientFullName(item), item.treatments_count]}
                title={strings.reports_strings.most_procedures}
            />

            <ReportTableComponent<Client>
                api={api.reports.record('most_notes', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.TITLE, strings.completed]}
                renderColumn={(item, index, rowIndex) => [rowIndex + 1, generateClientFullName(item), item.general_notes_count]}
                title={strings.reports_strings.most_notes}
            />

            <div className="col-span-2 grid gap-4">
                <Card className="col-span-2">
                    <Heading text={strings.reports_strings.prescription_signed} />
                    <div className="flex">
                        <BarHorizontalChartComponent
                            data={[
                                { name: strings.reports_strings.by_mal, value: prescriptionsSignedResponse?.data.prescriptions_by_mal ?? 0 },
                                { name: strings.reports_strings.by_own, value: prescriptionsSignedResponse?.data.prescriptions_by_own ?? 0 },
                            ]}
                            labelName={strings.prescription}
                            color="primary"
                        />
                    </div>
                </Card>
                {/* <Card className="col-span-2">
                    <Heading text={'Procedure Performed'} />
                    <div className="flex">
                        <BarHorizontalChartComponent data={data3} labelName="Procedures" color="orange" />
                    </div>
                </Card> */}
            </div>
            <Card className="col-span-2">
                <Heading text={strings.reports_strings.client_age} />
                <PieChartComponent data={ageMap} />
            </Card>
        </div>
    );
};

export const renderQuestionaryName = (value?: string) => {
    if (value === 'App\\AestheticInterest') {
        return strings.Aestethicinterest;
    }
    if (value === 'App\\HealthQuestionary') {
        return strings.HealthQuestionnaire;
    }
    if (value === 'App\\Covid19') {
        return strings.Covid19Questionnaire;
    }

    return value;
};

const RecordReportsHoC: FC = () => <ReportsProvider title={strings.reports_strings.reports_record} section="record" children={<RecordReports />} />;

export default RecordReportsHoC;
