import { generateUserFullName } from '@/helper';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import useMatchMultipleRoutes from '@hooks/useMatchMultipleRoutes';
import { usePaginationSWR, usePaginationSWRResponse } from '@hooks/usePaginationSWR';
import { ClientPrescriptionPaginatedResponse } from '@interface/common';
import { ClientPrescription } from '@interface/model/prescription';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { FC, PropsWithChildren, createContext, lazy, useContext, useState } from 'react';

const ClientPrescriptionDeleteModal = lazy(() => import('@components/Clients/Client/Prescription/ClientPrescriptionDeleteModal'));
const ClientPrescriptionSignModal = lazy(() => import('@components/Clients/Client/Prescription/ClientPrescriptionSignModal'));
const ClientPrescriptionViewModal = lazy(() => import('@components/Clients/Client/Prescription/ClientPrescriptionViewModal'));
const ClientPrescriptionModal = lazy(() => import('@components/Clients/Client/Prescription/ClientPrescriptionModal'));
const ClientRecordsCancelModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelModal'));
const ClientRecordsCancelNoteViewModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelNoteViewModal'));

interface IContext extends usePaginationSWRResponse<ClientPrescriptionPaginatedResponse, Error> {
    openPrescriptionDeleteModal: boolean;
    setOpenPrescriptionDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
    openPrescriptionSignModal: boolean;
    setOpenPrescriptionSignModal: React.Dispatch<React.SetStateAction<boolean>>;
    openPrescriptionViewModal: boolean;
    setOpenPrescriptionViewModal: React.Dispatch<React.SetStateAction<boolean>>;
    openPrescriptionEditModal: boolean;
    setOpenPrescriptionEditModal: React.Dispatch<React.SetStateAction<boolean>>;
    selectedPrescription?: ClientPrescription;
    setSelectedPrescription: React.Dispatch<React.SetStateAction<ClientPrescription | undefined>>;
    openPrescriptionCancelModal: boolean;
    setOpenPrescriptionCancelModal: React.Dispatch<React.SetStateAction<boolean>>;
    openPrescriptionCancelNoteViewModal: boolean;
    setOpenPrescriptionCancelNoteViewModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const MainCardPrescriptionContext = createContext<IContext | null>(null);

const MainCardPrescrptionProvider: FC<PropsWithChildren> = ({ children }) => {
    const { clientId, mutateCounts } = useClient();

    const isClientPage = useMatchMultipleRoutes([
        'clients/:clientId',
        'settings/team/:userId/booking/:bookingId/client/:clientId',
        'calendar/booking/:bookingId/client/:clientId',
    ]);

    const [openPrescriptionDeleteModal, setOpenPrescriptionDeleteModal] = useState(false);
    const [openPrescriptionSignModal, setOpenPrescriptionSignModal] = useState(false);
    const [openPrescriptionViewModal, setOpenPrescriptionViewModal] = useState(false);
    const [openPrescriptionEditModal, setOpenPrescriptionEditModal] = useState(false);
    const [openPrescriptionCancelModal, setOpenPrescriptionCancelModal] = useState(false);
    const [openPrescriptionCancelNoteViewModal, setOpenPrescriptionCancelNoteViewModal] = useState(false);

    const [selectedPrescription, setSelectedPrescription] = useState<ClientPrescription>();

    const swr = usePaginationSWR<ClientPrescriptionPaginatedResponse, Error>(api.clientPrescription(clientId), { limit: isClientPage ? 3 : 20 });

    const prescriptionMutate = async () => {
        mutateCounts();
        swr.mutate();
    };

    return (
        <MainCardPrescriptionContext.Provider
            value={{
                openPrescriptionDeleteModal,
                setOpenPrescriptionDeleteModal,
                openPrescriptionSignModal,
                setOpenPrescriptionSignModal,
                openPrescriptionViewModal,
                setOpenPrescriptionViewModal,
                openPrescriptionEditModal,
                setOpenPrescriptionEditModal,
                selectedPrescription,
                setSelectedPrescription,
                openPrescriptionCancelModal,
                setOpenPrescriptionCancelModal,
                openPrescriptionCancelNoteViewModal,
                setOpenPrescriptionCancelNoteViewModal,
                ...swr,
            }}
        >
            <ModalSuspense>
                {openPrescriptionSignModal && (
                    <ClientPrescriptionSignModal
                        openModal={openPrescriptionSignModal}
                        setOpenModal={setOpenPrescriptionSignModal}
                        mutate={prescriptionMutate}
                        clientMutate={mutateCounts}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openPrescriptionViewModal && (
                    <ClientPrescriptionViewModal
                        openModal={openPrescriptionViewModal}
                        handleClose={() => {
                            setOpenPrescriptionViewModal(false);
                            setSelectedPrescription(undefined);
                        }}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openPrescriptionDeleteModal && (
                    <ClientPrescriptionDeleteModal
                        open={openPrescriptionDeleteModal}
                        handleClose={() => {
                            setOpenPrescriptionDeleteModal(false);
                            setSelectedPrescription(undefined);
                        }}
                        mutate={prescriptionMutate}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openPrescriptionEditModal && (
                    <ClientPrescriptionModal
                        openModal={openPrescriptionEditModal}
                        setOpenModal={setOpenPrescriptionEditModal}
                        setSelectedPrescription={setSelectedPrescription}
                        setOpenSignModal={setOpenPrescriptionSignModal}
                        mutate={prescriptionMutate}
                        selectedPrescription={selectedPrescription}
                    />
                )}
                {openPrescriptionCancelModal && (
                    <ClientRecordsCancelModal
                        openModal={openPrescriptionCancelModal}
                        setOpenModal={setOpenPrescriptionCancelModal}
                        mutate={prescriptionMutate}
                        url={api.clientPrescriptionUpdate(selectedPrescription?.client_id, selectedPrescription?.id.toString() || '')}
                    />
                )}
                {openPrescriptionCancelNoteViewModal && (
                    <ClientRecordsCancelNoteViewModal
                        openModal={openPrescriptionCancelNoteViewModal}
                        handleModelClose={() => setOpenPrescriptionCancelNoteViewModal(false)}
                        cancelledNoteDetails={selectedPrescription?.cancel_note}
                        cancelNoteDate={selectedPrescription?.cancelled_at}
                        cancelByuUserName={generateUserFullName(selectedPrescription?.cancelled_by)}
                    />
                )}
            </ModalSuspense>
            {children}
        </MainCardPrescriptionContext.Provider>
    );
};

export const useMainCardPrescription = () => {
    const context = useContext(MainCardPrescriptionContext);

    if (!context) {
        throw new Error('useMainCardPrescription must be used within a MainCardPrescriptionProvider');
    }
    return context;
};

export default MainCardPrescrptionProvider;
