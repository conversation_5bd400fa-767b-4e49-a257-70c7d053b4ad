{"name": "meridiq-web", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@h6s/calendar": "^2.0.1", "@headlessui/react": "^2.2.6", "@lucasmogari/react-pagination": "^2.1.0", "@stripe/react-stripe-js": "^3.8.0", "@stripe/stripe-js": "^7.6.1", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "calendar-link": "^2.4.0", "classix": "^2.2.2", "core-js": "^3.35.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dom-to-image": "^2.6.0", "dropzone": "^6.0.0-beta.2", "formik": "^2.4.1", "heic2any": "^0.0.4", "history": "^5.3.0", "html2canvas": "^1.4.1", "nayuki-qr-code-generator": "^1.8.0", "print-js": "^1.6.0", "re-resizable": "^6.11.2", "react": "^19.1.0", "react-canvas-draw": "^1.2.1", "react-colorful": "^5.6.1", "react-compare-slider": "^3.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-ga4": "^2.1.0", "react-google-recaptcha": "^3.1.0", "react-gtm-module": "^2.0.11", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.61.1", "react-html5-camera-photo": "^1.5.5", "react-localization": "^2.0.6", "react-moveable": "^0.56.0", "react-otp-input": "^3.1.1", "react-router": "^6.30.1", "react-router-dom": "^6.30.1", "react-speech-recognition": "^4.0.1", "react-time-picker": "^7.0.0", "react-toastify": "^11.0.5", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^3.1.0", "regenerator-runtime": "^0.14.1", "signature_pad": "^5.0.10", "swr": "^2.3.4", "tinycolor2": "^1.6.0", "use-react-router-breadcrumbs": "^4.0.1", "uuid": "^11.1.0"}, "overrides": {"react-is": "19.0.0"}, "scripts": {"analyze": "npm run build && source-map-explorer 'build/**/*.js'", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@types/core-js": "^2.5.8", "@types/dom-to-image": "^2.6.4", "@types/dropzone": "^5.7.4", "@types/lodash": "^4.14.182", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-google-recaptcha": "^2.1.5", "@types/react-gtm-module": "^2.0.1", "@types/react-html5-camera-photo": "^1.5.1", "@types/react-speech-recognition": "^3.9.5", "@types/regenerator-runtime": "^0.13.5", "@types/tinycolor2": "^1.4.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react-swc": "^3.11.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "source-map-explorer": "^2.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-env-compatible": "^2.0.1", "vite-plugin-svgr": "^4.3.0"}}