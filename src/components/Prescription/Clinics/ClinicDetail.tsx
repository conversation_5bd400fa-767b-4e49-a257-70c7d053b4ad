import { commonFetch } from '@/helper';
import Card from '@components/card';
import Button from '@components/form/Button';
import CustomTabs from '@components/form/CustomTabs';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import { ClinicResponse } from '@interface/common';
import { Clinic } from '@interface/model/clinics';
import strings from '@lang/Lang';
import MaterialBreadcrumbs from '@partials/Breadcrumbs/MaterialBreadcrumbs';
import EditIcon from '@partials/Icons/Edit';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import React, { lazy } from 'react';
import { useParams } from 'react-router';
import useSWR from 'swr';
import useLocalStorage from '@hooks/useLocalStorage';

const ClinicFeesModal = lazy(() => import('./ClinicFeesModal'));
const ClinicPrescription = lazy(() => import('./ClinicPrescription'));
const ClinicStopServiceModal = lazy(() => import('./ClinicStopServiceModal'));
const PrescriptionClinicReport = lazy(() => import('./ClinicReport'));

export interface PrescriptionClientDetailProps {}

const PrescriptionClientDetail: React.FC<PrescriptionClientDetailProps> = () => {
    const { clinicId }: { clinicId?: string } = useParams();
    const { data, mutate, isLoading } = useSWR<ClinicResponse, Error>(api.clinicDetail(clinicId), commonFetch);
    const { setStorageValue: setTab, storedValue: tab } = useLocalStorage<number | null>('clinic_tab', 0);
    const [openModal, setOpenModal] = React.useState<boolean>(false);
    const [stopServiceModal, setStopServiceModal] = React.useState<boolean>(false);
    const [selectedClinic, setSelectedClinic] = React.useState<Clinic>();
    const tabs = [
        {
            text: strings.prescriptions,
            Component: <ClinicPrescription />,
        },
        {
            text: strings.reports,
            Component: <PrescriptionClinicReport />,
        },
    ];
    return (
        <>
            <ModalSuspense>
                {openModal && (
                    <ClinicFeesModal
                        openModal={openModal}
                        setOpenModal={setOpenModal}
                        selectedClinic={selectedClinic}
                        mutate={mutate}
                        //
                    />
                )}
                {stopServiceModal && (
                    <ClinicStopServiceModal
                        mutate={mutate}
                        handleClose={() => setStopServiceModal(false)}
                        open={stopServiceModal}
                        clinicId={data?.data?.id}
                    />
                )}
            </ModalSuspense>
            <div className="space-y-6">
                <div className="space-y-2">
                    <Heading className="hidden md:block" text={strings.clinic_detail}></Heading>
                    <MaterialBreadcrumbs />
                </div>
                <Card className="space-y-2 md:flex md:justify-between md:space-y-0">
                    {isLoading ? (
                        <SectionLoading />
                    ) : (
                        <>
                            <div className="space-y-2">
                                <Heading text={data?.data.company?.company_name} />
                                <p className="grid text-mediumGray md:block md:space-x-5">
                                    <span>
                                        +{data?.data.company?.country_code} {data?.data.company?.mobile_number}
                                    </span>{' '}
                                    <span>{data?.data.company?.email}</span>
                                </p>
                                <p className="hidden text-mediumGray md:block">{data?.data.company?.country}</p>
                            </div>
                            <div className="flex gap-3 md:block md:gap-0 md:space-y-4">
                                <Button
                                    variant="ghost"
                                    color="primary"
                                    size="small"
                                    className="w-full !bg-[#FFE7E3] !text-warning dark:!bg-[#382623]"
                                    onClick={() => {
                                        setStopServiceModal(true);
                                    }}
                                >
                                    {strings.stop_service}
                                </Button>
                                <Button
                                    variant="ghost"
                                    color="primary"
                                    size="small"
                                    className="w-full bg-lightPurple text-primary dark:bg-[#2c2e4d] dark:text-primaryLight"
                                    onClick={() => {
                                        setSelectedClinic(data?.data);
                                        setOpenModal(true);
                                    }}
                                >
                                    <div className="flex items-center gap-[6px]">
                                        <span>{strings.fees}:</span>
                                        <span>{Math.trunc(parseInt(data?.data?.price || '')).toString()}</span>
                                        <EditIcon />
                                    </div>
                                </Button>
                            </div>
                        </>
                    )}
                </Card>
                <Card>
                    <Heading className="hidden pb-8 md:block" variant="subHeader" text={strings.Details} />
                    <CustomTabs tabs={tabs} selectedIndex={tab} onChange={(index, mobile) => setTab(index === tab && mobile ? null : index)} />
                </Card>
            </div>
        </>
    );
};
export default PrescriptionClientDetail;
