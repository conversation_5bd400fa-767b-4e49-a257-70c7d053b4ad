@import './toastify.css';

:root,
body,
html {
    font-family: Inter, sans-serif;
    font-feature-settings:
        'liga' 1,
        'calt' 1; /* fix for Chrome */
    --primary: #5551ce;
    --secondary: #1a1b1c;
    --timeit-primary-color: var(--primary);
}

@supports (font-variation-settings: normal) {
    :root,
    body,
    html {
        font-family: InterVariable, sans-serif;
    }
}

@layer utilities {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
        color-scheme: dark;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
}

.dark ::selection {
    color: white;
    background: var(--primary);
}

html {
    @apply accent-amber-500;
}
html.dark {
    color-scheme: dark;
}

@tailwind base;
@tailwind components;

.side-bar-item.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: -3px;
    background-color: var(--primary);
    bottom: 0;
    height: 100%;
    width: 3px;
}

.login-doctor-image {
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
}

.login-doctor-image {
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
}

hr {
    @apply dark:border-lightGray;
}
@tailwind utilities;

/* Change Autocomplete styles in Chrome*/
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
    /* border: 1px solid transparent; */
    /* -webkit-text-fill-color: transparent; */
    /* -webkit-box-shadow: 0 0 0px 1000px #000 inset; */
    transition: background-color 5000s ease-in-out 0s;
}

/* Safari autofill */
input:-webkit-autofill,
input:-webkit-autofill:focus {
    -webkit-text-fill-color: #000;
}

.dark input:-webkit-autofill,
.dark input:-webkit-autofill:focus {
    -webkit-text-fill-color: #fff;
}

/* Remove Number Input arrows */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type='number'] {
    -moz-appearance: textfield;
}
/* .btn {
  @apply font-medium py-2 px-4 rounded-md cursor-pointer focus:outline-none focus:ring focus:ring-primary focus:ring-opacity-50 transition duration-150 ease-in-out;
}

.btn-primary {
  @apply bg-primary text-dark;
}

.btn-secondary {
  @apply bg-dark text-white focus:ring-dark focus:ring-opacity-25;
} */

/* .label-top {
  line-height: 0.05;
}
.label-transition {
  transition: font-size 0.05s, line-height 0.1s;
}
:global(label.text-xs) {
  font-size: 0.7rem;
} */

.prose-sm h1,
.prose-sm h2,
.prose-sm h3,
.prose-sm h4,
.prose-sm h5,
.prose-sm h6 {
    font-weight: 600;
}

/*
.prose p {
    margin-bottom: 1rem;
}

.prose ul:not(.ck) ,
.prose ol:not(.ck) {
    margin-bottom: 1rem;
    padding-left: 2rem !important;
    list-style-type: initial;
}
.prose ol:not(.ck){
    list-style-type: decimal;
} */

.visibility_hidden {
    /* display: none !important; */
    opacity: 0;
    position: absolute !important;
    z-index: -5;
    /* top: -9999px !important; */
    /* right: -9999px !important; */
}

.start-hero::after {
    content: '';
    position: fixed;
    top: 0;
    z-index: 1;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, #f5f5f5 40%, #f5f5f500 60%);
}
.dark .start-hero::after {
    background: linear-gradient(to right, #040404 40%, #1c1b1a00 60%);
}

.react-html5-camera-photo > .display-error {
    width: auto !important;
}

/* body {
    background-color: rgb(30, 30, 40);
}
.g-recaptcha {
    overflow: hidden;
    width: 298px;
    height: 74px;
}
iframe {
    margin: -5px 0px 0px -50px;
}

iframe body {
    background: #ef4444;
} */

.rc-anchor-normal {
    height: unset;
    width: unset;
}

.rc-anchor {
    border-radius: unset;
}

iframe[title='reCAPTCHA'] {
    border-radius: 4px;
    width: 302px;
    height: 76px;
}

#editorjs::selection {
    color: var(--secondary);
    background-color: var(--primary);
}

.dark .ce-block--selected .ce-block__content {
    background: #ffffff20;
}

.dark .ce-inline-toolbar {
    color: #040404;
}

table tr {
    position: relative;
}

/* table tr td:last-child,
table tr th:last-child {
	position: sticky;
	right: 0;
} */
/* MASONRY */

.masonry ul {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.masonry li {
    height: 220px;
    flex-grow: 1;
}

.masonry li:last-child {
    flex-grow: 10000;
    height: 0;
}

.masonry img {
    max-height: 100%;
    min-width: 100%;
    object-fit: cover;
    vertical-align: bottom;
}

@media (max-aspect-ratio: 1/1) {
    .masonry li {
        height: 20vh;
    }
}
@media (max-height: 480px) {
    .masonry li {
        height: 60vh;
    }
}
@media (max-aspect-ratio: 1/1) and (max-width: 480px) {
    .masonry ul {
        flex-direction: row;
    }

    .masonry li {
        height: auto;
        width: 100%;
    }

    .masonry img {
        width: 100%;
        max-height: 65vh;
        min-width: 0;
    }
}

.alternate-tr-mobile:nth-child(4n-1) {
    @apply bg-lightShadeBlue dark:bg-darkShadeGray;
}

.alternate-tr-desktop,
.alternate-tr {
    @apply hover:bg-primary/10 hover:dark:bg-primaryLight/[13%];
}
.alternate-tr-desktop:nth-child(4n) {
    @apply bg-lightShadeBlue hover:bg-primary/10 dark:bg-darkShadeGray hover:dark:bg-primaryLight/[13%];
}
.alternate-tr:nth-child(even) {
    @apply bg-lightShadeBlue hover:bg-primary/10 dark:bg-darkShadeGray hover:dark:bg-primaryLight/[13%];
}
canvas {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
}

img {
    @apply select-none;
}
.sliderHandle {
    @apply h-4 w-4 rounded-full bg-gray-200;
}
.downloading .sliderHandle {
    @apply hidden;
}

.hide-controls .moveable-control,
.hide-controls .moveable-line,
.hide-controls .moveable-rotation {
    @apply hidden;
}

.dz-preview.dz-image-preview,
.dz-details,
.dz-success-mark,
.dz-error-mark {
    @apply hidden;
}

.select fieldset {
    @apply border-none;
}

.topBar-search::-webkit-scrollbar {
    @apply w-0;
}
.topBar-search.visible::-webkit-scrollbar {
    @apply w-2;
}

.topBar-search::-webkit-scrollbar-track {
    @apply hover:bg-gray-100 dark:hover:bg-gray-800;
}

.topBar-search::-webkit-scrollbar-thumb {
    @apply rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.soft-searchbar::-webkit-scrollbar {
    @apply h-2 w-2;
}

.soft-searchbar::-webkit-scrollbar-track {
    @apply hover:bg-gray-100 dark:hover:bg-gray-800;
}

.soft-searchbar::-webkit-scrollbar-thumb {
    @apply rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600;
}

.soft-searchbar::-webkit-scrollbar-corner {
    @apply bg-transparent;
}

input[type='range']::-webkit-slider-thumb {
    -webkit-appearance: none;
    @apply h-4 w-4 rounded-full bg-primary;
}
.dark input[type='range']::-webkit-slider-thumb {
    -webkit-appearance: none;
    @apply bg-primaryLight;
}

.slider::-moz-range-thumb {
    -moz-appearance: none;
    @apply h-4 w-4 rounded-full bg-primary;
}
.dark .slider::-moz-range-thumb {
    -moz-appearance: none;
    @apply bg-primaryLight;
}

td {
    @apply align-middle;
}
.alternate-tr td {
    @apply first-of-type:rounded-none last-of-type:rounded-none;
}

.pagination-item {
    @apply flex h-8 w-8 cursor-pointer select-none items-center justify-center rounded-lg text-sm hover:bg-primary/10;
}
.pagination-item:disabled {
    @apply pointer-events-none text-gray-500;
}
.pagination-item.selected {
    @apply bg-primary text-white hover:bg-primary/90;
}

#top_bar_with_bg.scrolled {
    @apply backdrop-blur-[2px] lg:bg-white/90 lg:shadow lg:focus-within:bg-white dark:lg:bg-dimGray/90 dark:lg:focus-within:bg-dimGray;
}

@media (max-width: 768px) {
    .responsive-calendar {
        font-size: 8px !important;
    }
}

/* .DatePicker {
	z-index: 0;
}
.DatePicker__calendarContainer {
	z-index: 9999;
} */

.DatePicker__calendarContainer {
    z-index: 9999;
}

@layer base {
    html {
        -webkit-tap-highlight-color: transparent;
    }
}

.color-component .react-colorful {
    width: auto;
}

.color-component .react-colorful {
    height: 240px;
}

.color-component .react-colorful__saturation {
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.color-component .react-colorful__hue {
    height: 16px;
    border-radius: 6px;
}

.color-component .react-colorful__hue-pointer {
    width: 12px;
    height: 20px;
    border-radius: 0;
}

.desktop-sidebar {
    @apply w-72 overflow-x-hidden;
}
.desktop-sidebar.sticked {
    @apply w-16;
}
.desktop-sidebar::-webkit-scrollbar {
    width: 0;
    background: transparent;
}
.desktop-sidebar.sidebarmenu::-webkit-scrollbar {
    width: 0;
    background: transparent;
}
.mobile-sidebar,
.mobile-sidebar .sidebar-item,
.desktop-sidebar .sidebar-item,
.desktop-sidebar.sticked .sidebar-item {
    @apply py-2.5;
}
.mobile-sidebar .sidebar-item-parent {
    @apply pr-3;
}
.mobile-sidebar .sidebar-top-logo {
    @apply ml-3 mr-2;
}
/* .desktop-sidebar .sidebar-item-parent,
.desktop-sidebar.sticked .sidebar-item-parent {
    @apply pr-3;
} */

.desktop-sidebar .sidebar-logo,
.desktop-sidebar.sticked:hover .sidebar-logo {
    @apply hidden;
}
.desktop-sidebar .sidebar-logo-box,
.desktop-sidebar.sticked .sidebar-logo-box {
    @apply px-3.5;
}
.desktop-sidebar.sticked:hover .sidebar-logo-box {
    @apply px-2.5;
}
.desktop-sidebar.sticked .sidebar-logo {
    @apply block;
}
.desktop-sidebar.sticked .sidebar-pin-icon {
    @apply hidden;
}
.desktop-sidebar.sticked:hover .sidebar-pin-icon {
    @apply block;
}

/* .desktop-sidebar .sidebar-top-logo,
.desktop-sidebar.sticked .sidebar-top-logo {
	@apply mr-2;
} */
.desktop-sidebar.sticked .sidebar-top-logo {
    @apply mx-auto;
}

.desktop-sidebar .sidebar-item-text,
.desktop-sidebar .sidebar-item-chevron,
.desktop-sidebar.sticked .sidebar-item-text,
.desktop-sidebar.sticked .sidebar-item-chevron {
    @apply block;
}
.desktop-sidebar.sticked .sidebar-item-text,
.desktop-sidebar.sticked .sidebar-item-chevron {
    @apply hidden;
}

.desktop-sidebar .tooltip {
    position: relative;
    display: inline-block;
}
.desktop-sidebar .tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
    position: fixed;
    transition: all 300ms cubic-bezier(0.05, 0.7, 0.1, 1);
    transition-property: visibility, left, opacity;
    left: 4.5rem;
}

.desktop-sidebar .tooltip .tooltiptext {
    visibility: hidden;
    opacity: 0;
    font-size: medium;
    position: fixed;
    z-index: 50;
    left: 4rem;
}

.box-has-scroll {
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.14);
}

.dark .box-has-scroll {
    box-shadow: 0 -2px 16px rgba(50, 50, 50, 0.6);
}

.ProseMirror {
    @apply soft-searchbar relative h-full max-h-[17rem] min-h-[14rem] overflow-y-auto px-2 outline-none lg:max-h-[17rem] xl:max-h-[36rem] dark:xl:max-h-[31rem];
}

.gutter-stable {
    scrollbar-gutter: stable;
}

.react-time-picker {
    @apply h-full;
}

.react-time-picker input,
.react-time-picker select {
    @apply bg-transparent outline-none;
}

.react-time-picker__wrapper input:placeholder-shown {
    min-width: 20px;
}

.react-time-picker select {
    @apply focus:ring-1 focus:ring-primary dark:bg-darkGray;
}

.react-time-picker .react-time-picker__inputGroup {
    @apply flex;
}

.calendar-time-slot-splits {
    @apply absolute flex h-full w-full items-center justify-center border-dashed border-dimGray bg-white opacity-0 outline-none ring-primary hover:z-30 hover:border hover:opacity-100 hover:shadow-lg focus-visible:ring-1 dark:border-gray-200 dark:hover:bg-black;
}
.calendar-time-slot-splits-day {
    @apply pointer-events-auto absolute inset-0 flex h-full w-full items-center justify-center border-dashed border-dimGray bg-white opacity-0 outline-none ring-primary hover:z-30 hover:border hover:opacity-100 hover:shadow-lg focus-visible:ring-1 dark:border-gray-200 dark:hover:bg-black;
}
.icontooltip {
    position: relative;
    display: inline-block;
}

.icontooltip .icontooltiptext {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    z-index: 50;
    transform: translateX(-25%);
    margin-top: 2.5rem;
    font-size: medium;
    @apply pointer-events-none max-w-sm rounded bg-primary px-3 py-2 text-center text-sm text-white dark:bg-white dark:text-black;
}
.sidebar-top-logo .icontooltiptext {
    margin-top: 3rem;
}

.icontooltip:hover .icontooltiptext {
    opacity: 1;
    visibility: visible;
    transition-delay: 600ms;
}
.stripes {
    background: repeating-linear-gradient(45deg, #e9eaff, #e9eaff 2px, transparent 2px, transparent 11px), rgb(85 81 206 / 0.05);
}
.dark .stripes {
    background: repeating-linear-gradient(45deg, #282828, #282828 2px, transparent 2px, transparent 11px), hsl(210, 4%, 13%);
}
.services-sidebar {
    @apply overflow-x-hidden;
}

.services-sidebar.sticked .sidebar-item-text {
    @apply block;
}
.services-sidebar.sticked .sidebar-item-text {
    @apply hidden;
}
.desktop-schedule-service-sidebar-close {
    @apply absolute -right-[63px] bottom-1/2 z-20 -rotate-90;
}
.desktop-schedule-service-sidebar-open {
    @apply absolute bottom-1/2 right-[272px] z-20 -rotate-90;
}
.mobile-schedule-service-sidebar-close {
    @apply absolute -bottom-[21px] left-[120px] z-30;
}
.mobile-schedule-service-sidebar-open {
    @apply absolute -top-9 left-[130px] z-30;
}
.service-schedules::after {
    content: '';
    position: absolute;
    height: 35px;
    width: 50px;
    background: var(--primary);
    top: 0;
    left: -10px;
    z-index: 0;
    transform: skewX(-30deg);
    border-top-left-radius: 4px;
}
.service-schedules::before {
    content: '';
    position: absolute;
    height: 35px;
    width: 130px;
    background: var(--primary);
    top: 0;
    left: 10px;
    transform: skewX(30deg);
    z-index: 0;
    border-top-right-radius: 4px;
    border-top-left-radius: 10px;
}

.recharts-wrapper .recharts-cartesian-grid-horizontal line:first-child,
.recharts-wrapper .recharts-cartesian-grid-vertical line:first-child {
    stroke-opacity: 0 !important;
}

.recharts-cartesian-axis-tick-value {
    @apply select-none text-sm dark:fill-gray-400;
}

.modal-buttons > button {
    @apply md:min-w-40;
}
