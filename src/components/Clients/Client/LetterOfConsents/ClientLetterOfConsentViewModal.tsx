import { useHours } from '@provider/TimeProvider';
import * as React from 'react';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import api from '../../../../configs/api';
import { commonFetch, generateClientFullName, generateUserFullName } from '../../../../helper';
import { ClientResponse } from '../../../../interfaces/common';
import { ClientLetterOfConsent } from '../../../../interfaces/model/clientLetterOfConsent';
import strings from '../../../../lang/Lang';
import Checkbox from '../../../../partials/MaterialCheckbox/MaterialCheckbox';
import Modal from '../../../../partials/MaterialModal/Modal';
import ViewModalTextItem from '../../../../partials/ViewModal/ViewModalTextItem';
import cx from 'classix';

export interface ClientLetterOfConsentViewModalProps {
    selectedClientLetterOfConsent?: ClientLetterOfConsent;
    openModal?: boolean;
    handleClose?: () => void;
}

const ClientLetterOfConsentViewModal: React.FC<ClientLetterOfConsentViewModalProps> = ({
    selectedClientLetterOfConsent,
    openModal = false,
    handleClose = () => {},
}) => {
    const options = [
        {
            value: 2,
            text: strings.letter_of_consent_option1,
        },
        {
            value: 3,
            text: strings.letter_of_consent_option2,
        },
        {
            value: 4,
            text: strings.letter_of_consent_option3,
        },
        {
            value: 5,
            text: strings.letter_of_consent_option4,
        },
    ];
    const { clientId }: { clientId?: string } = useParams();
    const { renderDateTime } = useHours();
    const { data: client } = useSWR<ClientResponse, Error>(api.clientSingle(clientId), commonFetch);

    const { data } = useSWR<ClientResponse, Error>(api.clientSingle(clientId), commonFetch);

    return (
        <Modal open={openModal} title={strings.letter_of_consent} handleClose={handleClose} size="large">
            <div className="p-4">
                <ViewModalTextItem title={strings.TITLE} value={selectedClientLetterOfConsent?.consent_title} />
                {selectedClientLetterOfConsent?.is_publish_before_after_pictures === '0' ? (
                    <ViewModalTextItem title={strings.Are_we_allowed_to_publish_before_and_after_pictures} value={strings.No} />
                ) : (
                    ''
                )}
                {selectedClientLetterOfConsent?.is_publish_before_after_pictures === '1' ? (
                    <ViewModalTextItem title={strings.Are_we_allowed_to_publish_before_and_after_pictures} value={strings.Yes} />
                ) : (
                    ''
                )}
                {selectedClientLetterOfConsent?.is_publish_before_after_pictures !== '1' &&
                selectedClientLetterOfConsent?.is_publish_before_after_pictures !== '0' ? (
                    <ViewModalTextItem
                        title={strings.publishing_before_after_picture}
                        value={
                            options.find((letter) => letter.value.toString() === selectedClientLetterOfConsent?.is_publish_before_after_pictures)
                                ?.text || '-'
                        }
                    />
                ) : (
                    ''
                )}
                <ViewModalTextItem
                    title={strings.Signed_at}
                    show={!!selectedClientLetterOfConsent?.created_at}
                    value={`${renderDateTime(selectedClientLetterOfConsent?.created_at, { utc: true, isShowLocal: true })} ${strings.By} ${generateClientFullName(data?.data)}`}
                />
                <ViewModalTextItem
                    title={strings.client_signature}
                    show={!!selectedClientLetterOfConsent?.signature}
                    value={`${import.meta.env.REACT_APP_STORAGE_PATH}/${selectedClientLetterOfConsent?.signature}`}
                    image
                />
                {!selectedClientLetterOfConsent?.signature && (
                    <div className="mb-6">
                        <Checkbox
                            checked={true}
                            readOnly
                            name="consent_agreed"
                            label={strings.formatString(strings.i_accept_the_consent, generateClientFullName(client?.data)) as string}
                        />
                    </div>
                )}
                <ViewModalTextItem
                    title={strings.VerifiedSignAt}
                    show={!!selectedClientLetterOfConsent?.verified_signed_at}
                    value={`${renderDateTime(selectedClientLetterOfConsent?.verified_signed_at, { utc: true, isShowLocal: true }) || ''} ${strings.By} ${generateUserFullName(selectedClientLetterOfConsent?.verified_signed_by)}`}
                />
                <ViewModalTextItem
                    title={strings.practitioner_signature}
                    show={!!selectedClientLetterOfConsent?.verified_sign}
                    value={`${import.meta.env.REACT_APP_STORAGE_PATH}/${selectedClientLetterOfConsent?.verified_sign}`}
                    image
                />
                <ViewModalTextItem
                    title={strings.Information}
                    value={selectedClientLetterOfConsent?.letter}
                    show={!!selectedClientLetterOfConsent?.letter}
                    html
                />
            </div>
        </Modal>
    );
};

export default ClientLetterOfConsentViewModal;
