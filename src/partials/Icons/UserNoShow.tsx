import { SVGProps } from 'react';

const UserNoShowIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="1em" height="1em" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <ellipse cx={14} cy={6.9987} rx={4.66667} ry={4.66667} stroke="currentColor" strokeWidth={1.5} />
        <path
            d="M17.5 15.5496C16.4191 15.3035 15.2377 15.168 14 15.168C8.8453 15.168 4.66663 17.5185 4.66663 20.418C4.66663 23.3175 4.66663 25.668 14 25.668C20.6353 25.668 22.5534 24.48 23.1078 22.7513"
            stroke="currentColor"
            strokeWidth={1.5}
        />
        <ellipse cx={21} cy={18.6667} rx={4.66667} ry={4.66667} stroke="currentColor" strokeWidth={1.5} />
        <path
            d="M19.4442 17.1094L22.5553 20.2205M22.5557 17.1094L19.4446 20.2205"
            stroke="currentColor"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default UserNoShowIcon;
