import SMS1 from '@/utils/sms';
import Card from '@components/card';
import useAuth from '@hooks/useAuth';
import useLocalStorage from '@hooks/useLocalStorage';
import strings from '@lang/Lang';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { lazy, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';
const SMSUnavailableModal = lazy(() => import('@components/Clients/Client/SMS/SMSUnavailableModal'));

const SMSTemplates = lazy(() => import('./SMSTemplates'));
const SMSCredits = lazy(() => import('./SMSCredits'));

export interface SMSProps {}

const SMS: React.FC<SMSProps> = () => {
    const { getStoredValue: tab, setStorageValue: setTab } = useLocalStorage<number | null>('sms_tab_index', 0);
    const { pathname } = useLocation();
    const navigate = useNavigate();
    const {
        user,
        userType: { isSuperAdmin },
    } = useAuth();
    const isSMSAvailable = SMS1.isSupportedCountryCode(user);
    const [SMSModalOpen, setSMSModalOpen] = useState(!isSMSAvailable);

    useEffect(() => {
        if (pathname === smsRoutes[0]) setTab(0);
        if (pathname === smsRoutes[1]) setTab(1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pathname]);

    const tabs = [
        { text: strings.Template, Component: <SMSTemplates />, route: '/sms/templates' },
        ...(isSuperAdmin
            ? [{ text: strings.sms_credits, Component: !isSMSAvailable ? 'Not available' : <SMSCredits />, route: '/sms/credits' }]
            : []),
    ];

    const smsRoutes = tabs.map((v) => v.route);

    function onTabChange(index: number, mob: boolean) {
        if (mob) setTab(tab !== null ? null : index);
        navigate(smsRoutes[index]);
    }

    return (
        <>
            <ModalSuspense>
                {SMSModalOpen && !!user && (
                    <SMSUnavailableModal
                        open={SMSModalOpen}
                        handleClose={() => {
                            setSMSModalOpen(false);
                        }}
                    />
                )}
            </ModalSuspense>

            <Card className="p-2">
                {/* <CustomTabs tabs={tabs} onChange={onTabChange} selectedIndex={tab} /> */}
            </Card>
        </>
    );
};

export default SMS;
