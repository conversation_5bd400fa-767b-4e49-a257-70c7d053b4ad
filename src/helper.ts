import { Booking } from '@interface/model/booking';
import { Category } from '@interface/model/category';
import dayjs, { Dayjs } from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { toast } from 'react-toastify';
import { mutate } from 'swr';
import tinycolor from 'tinycolor2';
import api from './configs/api';
import { CommonModelResponse, SettingTypes } from './interfaces/common';
import { Client } from './interfaces/model/client';
import { Company, Unit } from './interfaces/model/company';
import { CompanyClientExtraField } from './interfaces/model/companyClientExtraField';
import { Setting } from './interfaces/model/setting';
import { User } from './interfaces/model/user';
import { AestheticInterestData } from './interfaces/questionary';
import strings from './lang/Lang';

import Settings from '@configs/settings';
import { ClientQuestionary } from '@interface/model/clientQuestionary';
import { CalendarHoliday } from '@interface/model/Holiday';
import { SubscriptionPlatform } from '@interface/model/plan';
import { promoCode } from '@interface/model/promoCode';
import { Receipt, ReceiptExportType } from '@interface/model/receipt';
import { Refund } from '@interface/model/refund';
import { TaxPercentageLevel } from '@provider/PaymentProvider';
import weekOfYear from 'dayjs/plugin/weekOfYear';

export const consentPermissions = (settings: Setting[], companyClientFields: CompanyClientExtraField[]) => {
    const firstList = settings
        .filter((v) => v.value === '1')
        .map((setting) => {
            switch (setting.key) {
                case 'SHOW_HEALTH_QUESTIONNAIRE':
                    return strings.HealthQuestionnaire;
                case 'SHOW_AESTHETIC_INTEREST':
                    return strings.Aestethicinterest;
                case 'SHOW_COVID_19':
                    return strings.Covid19Questionnaire;
                case 'SHOW_LETTER_OF_CONSENT':
                    return strings.LettersofConsents;
            }
            return '';
        })
        .filter((v) => v);

    const secondList = settings
        .filter((v) => v.value === '1')
        .map((setting) => {
            switch (setting.key) {
                case 'PORTAL_VIEW_OCCUPATION':
                    return strings.setting_occupation;
                case 'PORTAL_VIEW_DATE_OF_BIRTH':
                    return strings.setting_date_of_birth;
                case 'PORTAL_VIEW_CITY':
                    return strings.setting_city;
                case 'PORTAL_VIEW_PHONE':
                    return strings.setting_phone;
                case 'PORTAL_VIEW_STREET_ADDRESS':
                    return strings.setting_street_address;
                case 'PORTAL_VIEW_ZIPCODE':
                    return strings.setting_zip_code;
                case 'PORTAL_VIEW_STATE':
                    return strings.setting_state;
                case 'PORTAL_VIEW_COUNTRY':
                    return strings.setting_country;
                case 'PORTAL_VIEW_PROFILE':
                    return strings.setting_profile;
            }
            return '';
        })
        .filter((v) => v);

    const thirdList = companyClientFields
        .filter((f) => f.view)
        .map((fields) => {
            return fields.name;
        });

    const fourthList = [strings.setting_name, strings.setting_email, strings.setting_health_information];

    return [...fourthList, ...secondList, ...thirdList, ...firstList];
};

export const isValidDate = (date: string) => {
    return dayjs(date, 'YYYY-MM-DD').isValid();
};

export function getUrlExtension(url: string) {
    return url.split(/[#?]/)[0].split('.').pop()?.trim();
}

export function makeXMLRequest(
    method: string,
    url: string,
    formData: FormData,
    onprogress: (this: XMLHttpRequest, ev: ProgressEvent<EventTarget>) => any,
): Promise<XMLHttpRequest> {
    return new Promise(async function (resolve, reject) {
        var xhr = new XMLHttpRequest();
        xhr.open(method, url);
        xhr.onload = function () {
            resolve(xhr);
        };

        xhr.upload.onprogress = onprogress;

        xhr.setRequestHeader('Accept', 'application/json');
        xhr.setRequestHeader('X-App-Locale', strings.getLanguage());

        let token = getCookie('XSRF-TOKEN');

        if (!token) {
            // await callCsrfToken();
            token = getCookie('XSRF-TOKEN');
        }

        xhr.setRequestHeader('X-XSRF-TOKEN', token ?? '');

        xhr.withCredentials = true;

        xhr.onerror = function () {
            reject({
                status: this.status,
                statusText: xhr.statusText,
            });
        };

        xhr.send(formData);
    });
}

export async function generateCanvasImage(file: string): Promise<Blob | string> {
    const image = await toImage(file);
    // Resize the image
    let canvas = document.createElement('canvas');

    const expecedWidth = 1620;
    const width = expecedWidth; // 1580
    const height = expecedWidth * (image.height / image.width);

    canvas.width = width;
    canvas.height = height;
    canvas.getContext('2d')?.drawImage(image, 0, 0, width, height);

    if (canvas?.toBlob) {
        return await new Promise((resolve, reject) => {
            canvas.toBlob(
                (blob) => {
                    if (blob) {
                        return resolve(blob);
                    }
                    reject('error');
                },
                `jpeg`,
                0.7,
            );
        });
    } else {
        return canvas.toDataURL(`image/jpeg`, 0.7);
    }
}

export const toImage = async (string: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
        const image = new Image();
        image.src = string;
        image.onload = () => resolve(image);
        image.onerror = () => reject('not loaded');
    });

export function getAestheticInterest(aestheticInterests: AestheticInterestData, imageData: string = '', formData = new FormData()) {
    aestheticInterests.forEach((aestheticInterest, index) => {
        Object.keys(aestheticInterest).forEach((name) => {
            if (name === 'notes') {
                formData.set(`aesthetic_interest[${index}][notes]`, aestheticInterest.notes || '');
            } else if (name === 'answer_checkbox') {
                (aestheticInterest?.answer_checkbox || []).forEach((answerCheckbox, localIndex) => {
                    formData.set(`aesthetic_interest[${index}][answer_checkbox][${localIndex}]`, answerCheckbox.toString());
                });
            } else if (name === 'other') {
                formData.set(`aesthetic_interest[${index}][other]`, aestheticInterest.other || '');
            } else if (name === 'image') {
                if (imageData) {
                    if (aestheticInterest.image instanceof Blob) {
                        formData.set(`aesthetic_interest[${index}][image]`, aestheticInterest.image);
                    } else if (isDataURL(imageData || '')) {
                        formData.set(`aesthetic_interest[${index}][image]`, convertBase64ToFile(imageData));
                    } else {
                        formData.set(`aesthetic_interest[${index}][image]`, 'null');
                    }
                } else {
                    try {
                        if (aestheticInterest.image instanceof Blob) {
                            formData.set(`aesthetic_interest[${index}][image]`, aestheticInterest.image);
                        } else if (isDataURL(aestheticInterest.image || '')) {
                            formData.set(`aesthetic_interest[${index}][image]`, convertBase64ToFile(aestheticInterest.image || ''));
                        } else {
                            formData.set(`aesthetic_interest[${index}][image]`, aestheticInterest.image || 'null');
                        }
                    } catch (error) {
                        formData.set(`aesthetic_interest[${index}][image]`, 'null');
                    }
                }
            }
        });
    });

    return formData;
}

//eslint-disable-next-line
const isDataURLRegex = /^\s*data:([a-z]+\/[a-z]+(;[a-z\-]+\=[a-z\-]+)?)?(;base64)?,[a-z0-9\!\$\&\'\,\(\)\*\+\,\;\=\-\.\_\~\:\@\/\?\%\s]*\s*$/i;

export const onlyNumberAllowedRegex = /[`~!@#$%^&*()_\-=[\]{};':"\\|,.<>/?A-Za-z]+$/;

export function isDataURL(s: string) {
    return !!s.match(isDataURLRegex);
}

export function isClassComponent(component: any) {
    return typeof component === 'function' && !!component.prototype.isReactComponent;
}

export function isFunctionComponent(component: any) {
    return typeof component === 'function' && String(component).includes('return React.createElement');
}

export function isReactComponent(component: any) {
    return isClassComponent(component) || isFunctionComponent(component);
}

export function generateUserFullName(user?: User | null) {
    if (!user) return '';
    return generateFullName(user?.first_name || '', user?.last_name || '');
}

export function userRole(user?: User) {
    return user?.email === user?.company?.email ? strings.superUser : user?.user_role;
}

export function generateClientFullName(client?: Client) {
    return generateFullName(client?.first_name || '', client?.last_name || '');
}

export function generateClientFullNameWithEmail(client?: Client) {
    return `${generateFullName(client?.first_name || '', client?.last_name || '')} ${client?.email ? `(${client?.email})` : ''}`;
}

export function generateClientAvatarName(client?: Client) {
    return generateAvatarName(client?.first_name || '', client?.last_name || '');
}

export function generateFullName(first_name: string, last_name: string) {
    return `${first_name} ${last_name}`;
}

export function generateBookingClientName(first_name: string | null, last_name: string | null, client?: Client) {
    if (first_name === null && last_name === null && client === null) {
        return '';
    }
    if (first_name === null || last_name === null) {
        return generateClientFullName(client);
    } else if (client?.first_name === '' || client?.last_name === '') {
        return generateFullName(first_name, last_name);
    } else if (generateFullName(first_name, last_name) === generateClientFullName(client)) {
        return generateClientFullName(client);
    } else {
        return `${generateFullName(first_name, last_name)} (${generateClientFullName(client)})`;
    }
}

export function generateAvatarName(first_name: string, last_name: string) {
    return `${first_name.charAt(0)}${last_name.charAt(0)}`.toUpperCase();
}

export const questionaryTypeToName = (value?: string) => {
    if (value === 'App\\AestheticInterest') {
        return strings.Aestethicinterest;
    }
    if (value === 'App\\HealthQuestionary') {
        return strings.HealthQuestionnaire;
    }
    if (value === 'App\\Covid19') {
        return strings.Covid19Questionnaire;
    }

    return strings.ClientQuestionnaire;
};

export function getUnitKeyToValue(value: string) {
    if (value === 'usd') {
        return '$';
    }
    if (value === 'eur') {
        return '€';
    }
    if (value === 'sek') {
        return 'kr ';
    }
    if (value === 'gbp') {
        return '£';
    }
    return value;
}

export function getUnitWithValue(key: string, value: string) {
    if (!key) return value;

    const newValue = parseFloat(value);

    return newValue.toLocaleString(strings.getLanguage(), {
        style: 'currency',
        currency: key,
        currencyDisplay: 'symbol',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
    });
}

export function getValueWithoutCurrency(value: string | number) {
    const newValue = parseFloat(value.toString());

    return new Intl.NumberFormat(strings.getLanguage(), {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(newValue);
}

export function getUnitSymbolToKey(value: string) {
    if (value === '$') return 'USD';
    if (value === '€') return 'EUR';
    if (value === 'kr') return 'SEK';
    if (value === '£') return 'GBP';
    return value;
}

export const commonFetch = async (url: string, opt?: RequestInit) => {
    try {
        const response = await fetch(url, {
            headers: {
                Accept: 'application/json',
                'X-App-Locale': strings.getLanguage(),
            },
            credentials: 'include',
            ...opt,
        });

        const data = await response.json();

        if (response.status !== 200 || data.status !== '1') {
            const error = new Error(data.message || 'server error, contact server.');
            error.status = response.status;
            throw error;
        }

        return data;
    } catch (error) {
        if (!navigator.onLine) {
            return;
        }
        throw error;
    }
};

interface SaveSettingProps {
    key: SettingTypes | keyof typeof Settings;
    value: '1' | '0' | string;
}

export const getSignedUrl = async (url: string) => {
    const res = await fetch(api.getSignedUrl, {
        method: 'POST',
        headers: {
            Accept: 'application/json',
            'content-type': 'application/json',
            'X-App-Locale': strings.getLanguage(),
        },
        body: JSON.stringify({
            path: url.split('?')[0],
        }),
        credentials: 'include',
    });
    const data = (await res.json()) as CommonModelResponse<string>;

    try {
        return data.data;
    } catch (error) {
        console.log(error);
        return url;
    }
};

export const saveSetting = async ({ key, value }: SaveSettingProps) => {
    const response = await fetch(api.settingStore, {
        method: 'POST',
        headers: {
            Accept: 'application/json',

            'X-App-Locale': strings.getLanguage(),
            'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ key, value }),
    });

    const webStatus = response.status;

    const data = await response.json();
    return { ...data, webStatus };
};

interface SaveCompanyClientExtraFieldProps {
    id?: number;
    name?: string;
    required?: boolean;
    view?: boolean;
}

export const saveCompanyClientExtraField = async (value: SaveCompanyClientExtraFieldProps) => {
    const response = await fetch(
        value.id ? api.companyClientExtraFieldUpdate.replace(':id', value.id.toString()) : api.companyClientExtraFieldStore,
        {
            method: 'POST',
            headers: {
                Accept: 'application/json',
                'X-App-Locale': strings.getLanguage(),
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(value),
        },
    );

    const webStatus = response.status;

    const data = await response.json();
    return { ...data, webStatus };
};

export const deleteCompanyClientExtraField = async (id: number) => {
    const response = await fetch(api.companyClientExtraFieldDelete.replace(':id', id.toString()), {
        method: 'DELETE',
        headers: {
            Accept: 'application/json',

            'X-App-Locale': strings.getLanguage(),
            'Content-Type': 'application/json',
        },
        credentials: 'include',
    });

    const webStatus = response.status;

    const data = await response.json();
    return { ...data, webStatus };
};

export const handleQuestionnaireActiveChange = async ({ id, checked }: { id: number; checked: '1' | '0' }) => {
    const response = await fetch(api.questionnaireUpdate.replace(':id', id.toString()), {
        method: 'POST',
        headers: {
            Accept: 'application/json',

            'X-App-Locale': strings.getLanguage(),
            'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
            is_active: checked,
        }),
    });
    const webStatus = response.status;

    const data = await response.json();
    return { ...data, webStatus };
};

export const saveUnit = async ({ value }: { value: Unit }) => {
    const response = await fetch(api.companyUpdate, {
        method: 'POST',
        headers: {
            Accept: 'application/json',

            'X-App-Locale': strings.getLanguage(),
            'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ unit: value }),
    });

    const webStatus = response.status;

    const data = await response.json();
    return { ...data, webStatus };
};

// export const cancelSubscription = async () => {
//     const response = await fetch(api.subscriptionCancel, {
//         method: 'POST',
//         headers: {
//             "Accept": 'application/json',
//             'X-App-Locale': strings.getLanguage(),
//             'Content-Type': 'application/json',
//         },
//         credentials: 'include',
//     });

//     const webStatus = response.status;

//     const data = await response.json();
//     return { ...data, webStatus };
// };

export const toBase64 = async (file: File | Blob): Promise<string> =>
    new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
    });

export const heic2convert = async (file: File | Blob) => {
    return import('heic2any').then(async ({ default: heic2any }) => {
        return (await heic2any({
            blob: file as Blob,
            toType: 'image/jpeg',
        })) as Blob;
    });
};

export const convertBase64ToFile = (b64Data: string): File => {
    // convert base64/URLEncoded data component to raw binary data held in a string
    let byteString;
    if (b64Data.split(',')[0].indexOf('base64') >= 0) byteString = atob(b64Data.split(',')[1]);
    else byteString = unescape(b64Data.split(',')[1]);

    // separate out the mime component
    const mimeString = b64Data.split(',')[0].split(':')[1].split(';')[0];

    // write the bytes of the string to a typed array
    const ia = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i += 1) {
        ia[i] = byteString.charCodeAt(i);
    }

    const blob = new Blob([ia], { type: mimeString });
    return new File([blob], 'canvasImage.jpeg', { type: 'image/jpeg' });
};

export function renderAddress(company?: Company) {
    return [company?.street_address, company?.zip_code, company?.city, company?.state, company?.country]
        .filter((val) => val && val.length)
        .join(', ');
}

export function renderBookingAddress(booking?: Booking) {
    return [booking?.address, booking?.city, booking?.state, booking?.country].filter((val) => val && val.length).join(', ');
}

export type DetailType = string | undefined;
export function printDetails(values: DetailType[]) {
    return values
        .filter((val) => {
            return val?.length;
        })
        .join(',\n');
}

export function nFormatter(num: number, digits: number = 0) {
    var si = [
        { value: 1, symbol: '' },
        { value: 1e3, symbol: 'K' },
        { value: 1e6, symbol: 'M' },
        { value: 1e9, symbol: 'G' },
        { value: 1e12, symbol: 'T' },
        { value: 1e15, symbol: 'P' },
        { value: 1e18, symbol: 'E' },
    ];
    var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
    var i;
    for (i = si.length - 1; i > 0; i--) {
        if (num >= si[i].value) {
            break;
        }
    }
    return (num / si[i].value).toFixed(digits).replace(rx, '$1') + si[i].symbol;
}

export const clearSWRCache = () => mutate(() => true, undefined, { revalidate: false });

export function iOS() {
    return (
        ['iPad Simulator', 'iPhone Simulator', 'iPod Simulator', 'iPad', 'iPhone', 'iPod'].includes(navigator.platform) ||
        // iPad on iOS 13 detection
        (navigator.userAgent.includes('Mac') && 'ontouchend' in document)
    );
}

export function formatDate(date: string | undefined | null, formatter = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return undefined;
    dayjs.extend(utc);
    return dayjs.utc(date).format(formatter);
}
export function formatEditBookingDate(date: string | undefined | null, format: string = api.dateFormat) {
    if (!date) return undefined;
    return dayjs(date).format(format);
}

export function customParseDayJs(date: string | undefined | null, format: string) {
    dayjs.extend(customParseFormat);
    return dayjs(date, format);
}
export function weekNumber(date: Dayjs) {
    dayjs.extend(weekOfYear);
    // @ts-ignore
    return date.week();
}

export function timeZone() {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    return dayjs.tz.guess();
}

export function isHTML(str: string) {
    var doc = new DOMParser().parseFromString(str, 'text/html');
    return Array.from(doc.body.childNodes).some((node) => node.nodeType === 1);
}

export function deleteCookie(name: string) {
    document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

export function setCookie(name: string) {
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

export async function aDownload(filename: string, url: string) {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
}
export function generateCategoryName(category?: Category) {
    return generateCateName(category?.name || '');
}
export function generateCateName(category_name: string) {
    return `${category_name}`;
}

export const meridiqPrimaryColor = '#5551CE';
export function getTextColor(bgColor?: string) {
    if (!bgColor || bgColor.toLowerCase() === meridiqPrimaryColor) {
        return '#ffffff';
    }
    if (bgColor !== null) {
        return tinycolor(bgColor).isDark() ? '#FFFFFF' : '#000000';
    }
    return '#000000';
}

export function inIframe() {
    try {
        return window.self !== window.top;
    } catch (e) {
        return true;
    }
}

export function customToast(message: string, data?: Company) {
    toast.success(message || 'Thanks for your response.', {
        style: { backgroundColor: data?.theme || '#5551CE', color: data?.is_black_text ? '#000000' : '#ffffff' },
    });
}

export function checkIsGroupBooking(booking?: Booking) {
    return booking?.service?.category?.group_booking ? true : false;
}

export const calendarMonths = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
];

export function firstCapital(string: string) {
    if (!string) return string;

    return string[0].toUpperCase() + string.slice(1).toLowerCase();
}

export function isDecimal(value: string | number, places?: number) {
    if (places) {
        return /^\d*\.?\d{0,2}$/.test(value.toString());
    }

    return /^\d*\.?\d*$/.test(value.toString());
}

export function customRound(value: string | number, places: number = 2) {
    return Math.round((value as number) * Math.pow(10, places)) / Math.pow(10, places);
}

export function limitDecimalPlaces(value: string, count: number) {
    if (value.indexOf('.') === -1) {
        return value;
    }

    if (value.length - value.indexOf('.') > count) {
        var re = new RegExp('^-?\\d+(?:.\\d{0,' + (count || -1) + '})?');

        return value.match(re)?.[0];
    }

    return value;
}

export const canPOSRefund = (receipt?: Receipt) => {
    if (!receipt || receipt.paid_for_gift_card_exists) return false;
    return receipt.status === 'PAID' || receipt.status === 'PARTIALLY_REFUNDED';
};

export const canPOSRetry = (receipt?: Receipt) => {
    if (!receipt || receipt.paid_for_gift_card_exists) return false;
    return receipt.status === 'CANCELLED' || receipt.status === 'ABORTED';
};

export const isPOSProcessing = (receipt?: Receipt) => {
    if (!receipt) return false;
    return receipt.status === 'PROCESSING' || receipt.status === 'PENDING';
};

export const canPOSShowReceiptDownload = (receipt?: Receipt) => {
    if (!receipt || !receipt.paid_at) return false;
    return true;
};

export const canPOSReceiptDownload = (receipt?: Receipt) => {
    if (!receipt || !receipt.paid_at || parseInt(receipt.downloaded ?? '0') >= 2) return false;
    return true;
};

export const canShowRefundReceiptDownload = (refund?: Refund) => {
    if (!refund || !refund.refunded_at) return false;
    return true;
};

export const canRefundReceiptDownload = (refund?: Refund) => {
    if (!refund || !refund.refunded_at || parseInt(refund.downloaded ?? '0') >= 2) return false;
    return true;
};

export const utcToLocal = (date: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    dayjs.extend(utc);

    return dayjs.utc(date).local();
};

export const showOrganizationNumber = (country?: string) => {
    if (country === 'Sweden') {
        return true;
    }

    return false;
};

export const allowPOSAccess = (company?: Company) => {
    if (company?.country === 'Sweden') {
        return true;
    }

    return false;
};

export const allowOrganizationNumberEdit = (company?: Company) => {
    return !company?.ccu_register_id;
};

export const allowTaxInfoAccess = (company?: Company) => {
    if (company?.country === 'Sweden') {
        return true;
    }

    return false;
};

export const companyLocationRequired = (company?: Company) => {
    return !!company?.ccu_register_id;
};

export const validOrganizationNumber = (value: string) => {
    return /^\d{6}-\d{4}$/.test(value);
};

type TaxPercentageItem = {
    tax_percentage: number;
    quantity: number;
    price: number;
};

export const generateTaxPercentageLevel = (discountPercentage: number, items: TaxPercentageItem[]) => {
    const levels: { [name: string]: TaxPercentageLevel } = items.reduce(
        (p, n) => {
            const selling_price = n.price - n.price * (discountPercentage / 100);
            const price = selling_price / (1 + n.tax_percentage / 100);

            const tax = (selling_price - price) * n.quantity;
            const net = price * (n.quantity ?? 1);

            return {
                ...p,
                [n.tax_percentage]: {
                    percentage: getValueWithoutCurrency(n.tax_percentage),
                    tax: getValueWithoutCurrency(customRound(parseFloat(p[n.tax_percentage]?.tax?.toString() || '0') + tax)),
                    net: getValueWithoutCurrency(customRound(parseFloat(p[n.tax_percentage]?.net?.toString() || '0') + net)),
                    gross: getValueWithoutCurrency(customRound(parseFloat(p[n.tax_percentage]?.gross?.toString() || '0') + tax + net)),
                },
            };
        },
        {} as { [name: string]: TaxPercentageLevel },
    );

    return Object.values(levels).sort((a, b) => parseInt(b.percentage?.toString() ?? '0') - parseInt(a.percentage?.toString() ?? '0'));
};

export const printPDF = async (
    base64: string,
    options?: {
        documentTitle?: string;
        onPrintDialogClose?: () => void;
    },
) => {
    return await import('print-js').then(({ default: printJS }) => {
        return printJS({
            documentTitle: options?.documentTitle,
            printable: base64,
            type: 'pdf',
            base64: true,
            onPrintDialogClose: options?.onPrintDialogClose,
        });
    });
};

export const handleExportReceiptData = async ({
    lang,
    receipt,
    onClose,
    title,
    type,
}: {
    receipt: Receipt;
    lang?: string;
    onClose?: () => void;
    title?: string;
    type: ReceiptExportType;
}) => {
    const response = await fetch(
        api.pos.receipt.export(receipt.id, {
            type: type,
        }),
        {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'X-Time-Zone': timeZone(),
                'X-App-Locale': lang ?? 'en',
            },
            credentials: 'include',
        },
    );

    if (type === 'download') {
        const blob = await response.blob();

        const data = await toBase64(blob);
        if (isMobileDevice()) {
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(blob);
            a.setAttribute('download', title ?? 'receipt.pdf');
            a.click();
        } else {
            await printPDF(data.substring(data.indexOf(',') + 1), {
                // documentTitle: title`${pos_strings.receipt.receipt} - ${receipt.viva_receipt_id}.pdf`,
                documentTitle: title,
                onPrintDialogClose: onClose,
            });
        }
    }

    if (type === 'mail') {
        return await response.json();
    }
};

export function isMobileDevice(): boolean {
    const uaData = (navigator as any).userAgentData;

    if (uaData) {
        return uaData.mobile || ['iOS', 'Android'].includes(uaData.platform);
    }
    const userAgent = navigator.userAgent || '';
    // Detect Android using userAgent
    const isAndroid = /android/i.test(userAgent);

    // Detect iOS using userAgent
    const isIOS = /iphone|ipad|ipod/i.test(userAgent);

    // Detect Apple mobile device in modern Safari (iPadOS on Mac hardware)
    const isModernAppleTouchDevice = uaData?.platform === 'iOS' || (navigator.maxTouchPoints > 1 && /Macintosh|MacIntel/.test(userAgent));

    return isAndroid || isIOS || isModernAppleTouchDevice;
}

export const handleExportRefundReceiptData = async ({
    lang,
    refund,
    onClose,
    title,
    type,
}: {
    refund: Refund;
    lang?: string;
    onClose?: () => void;
    title?: string;
    type: ReceiptExportType;
}) => {
    const response = await fetch(
        api.pos.refund.export(refund.id, {
            type: type,
        }),
        {
            method: 'GET',
            headers: {
                Accept: 'application/json',
                'X-Time-Zone': timeZone(),
                'X-App-Locale': lang ?? 'en',
            },
            credentials: 'include',
        },
    );

    if (type === 'download') {
        const blob = await response.blob();

        const data = await toBase64(blob);

        await printPDF(data.substring(data.indexOf(',') + 1), {
            // documentTitle: title`${pos_strings.receipt.receipt} - ${receipt.viva_receipt_id}.pdf`,
            documentTitle: title,
            onPrintDialogClose: onClose,
        });
    }

    if (type === 'mail') {
        return await response.json();
    }
};

export async function regenPDF(clientId?: string | number, selectedQuestionary?: ClientQuestionary) {
    if (!selectedQuestionary?.should_regen_pdf) return selectedQuestionary;
    try {
        const response = await fetch(api.clientRegenQuetionerPDF(clientId, selectedQuestionary?.id), {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
                'X-App-Locale': strings.getLanguage(),
            },
        });

        const data = (await response.json()) as CommonModelResponse<ClientQuestionary>;
        if (data?.status !== '1') {
            return selectedQuestionary;
        }
        return data.data;
    } catch (error) {
        return selectedQuestionary;
    }
}

export async function downloadFile(
    fileName: string,
    url: string,
    options?: {
        newPage?: boolean;
    },
) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            // headers: {
            //     'X-App-Locale': strings.getLanguage(),
            // },
            // credentials: "include",
        });

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);

        if (options?.newPage) {
            a.target = '_blank';
        }
        const ext = getUrlExtension(fileName) ? '' : getUrlExtension(url);
        a.setAttribute('download', `${fileName}${ext ? '.' + ext : ''}`);
        a.click();
    } catch (error) {
        console.error(error);
    }
}
export function labelText(label: string) {
    if (label === 'RECORD') return strings.meridiq_susbscription;
    if (label === 'SMS') return 'SMS';
    if (label === 'MAL') return `${strings.meridiq_billing} (MAL)`;
}
export const randomColor = (): string => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
};
export function removeDuplicates(arr: CalendarHoliday[]) {
    const name = new Set();
    const finalResult = [];

    for (const obj of arr) {
        if (!name.has(obj.summary)) {
            name.add(obj.summary);
            finalResult.push(obj);
        }
    }

    return finalResult;
}

export default randomColor;
export function checkPromoCodeType(code: promoCode | undefined, type: SubscriptionPlatform) {
    return !!code?.applies_to?.includes(type);
}
export const getCookie = (name: string) => {
    const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
    return match ? decodeURIComponent(match[2]) : null;
};

// export const callCsrfToken = async () => {
//     try {
//         await window.originalFetch(api.csrfCookie, {
//             credentials: 'include',
//         });
//     } catch (error) {}
// };

// declare global {
//     interface Window {
//         originalFetch: typeof fetch;
//     }
// }

// export function setupCsrfFetch() {
//     const originalFetch = window.fetch;

//     window.originalFetch = originalFetch;

//     window.fetch = async (input: RequestInfo | URL, init: RequestInit = {}) => {
//         let url = typeof input === 'string' ? input : (input as Request).url;

//         // Only modify if it's your API
//         if (url.includes('/api/')) {
//             let token = getCookie('XSRF-TOKEN');

//             if (!token) {
//                 await callCsrfToken();
//                 token = getCookie('XSRF-TOKEN');
//             }

//             init.headers = {
//                 'X-XSRF-TOKEN': token ?? '',
//                 Accept: 'application/json',
//                 'Content-Type': 'application/json',
//                 ...(init.headers ?? {}),
//             };

//             init.credentials = 'include';

//             // Optional: if body is FormData, remove Content-Type to let browser handle it
//             if (init.body instanceof FormData && init.headers && Object.keys(init.headers).includes('Content-Type')) {
//                 delete (init.headers as Record<string, string>)['Content-Type'];
//             }
//         }

//         return originalFetch(input, init);
//     };
// }
