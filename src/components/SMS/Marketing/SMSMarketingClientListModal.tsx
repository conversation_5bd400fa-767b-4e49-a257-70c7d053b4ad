import { generateClientFullName } from '@/helper';
import Input from '@components/form/Input';
import Pagination from '@components/form/Pagination';
import Skeleton from '@components/Skeleton/Skeleton';
import api from '@configs/api';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import SearchIcon from '@icons/Search';
import { MarketingClientListPaginatedResponse } from '@interface/common';
import { MarketingClient } from '@interface/model/marketingClient';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import Modal from '@partials/MaterialModal/Modal';
import Table from '@partials/Table/PageTable';
export interface SMSMarketingClientListModalProps {
    openModal: boolean;
    handleClose: () => void;
    marketingId: number;
}
const SMSMarketingClientListModal: React.FC<SMSMarketingClientListModalProps> = ({ handleClose, openModal, marketingId }) => {
    const { data, loading, page, setPage, search, setSearch } = usePaginationSWR<MarketingClientListPaginatedResponse, Error>(
        api.sms.marketing.sendSMSClientList(marketingId),
        { limit: 10 },
    );
    return (
        <Modal open={openModal} title={strings.clients_list} handleClose={handleClose} size="large">
            <div className="space-y-4 p-5">
                <Input
                    placeholder={strings.Search}
                    value={search || ''}
                    icon={<SearchIcon className="text-mediumGray" />}
                    onChange={(event) => setSearch(event.target.value)}
                />
                <Table>
                    <Table.Head>
                        <Table.Th>{strings.No}</Table.Th>
                        <Table.Th>{strings.client_name}</Table.Th>
                        <Table.Th>{strings.PhoneNumber}</Table.Th>
                        <Table.Th>{strings.credit_used}</Table.Th>
                        <Table.Th>
                            {strings.sms} {strings.status}
                        </Table.Th>
                    </Table.Head>
                    <Table.Body>
                        {loading ? (
                            <Skeletons limit={5} />
                        ) : (
                            data?.data?.map((item, index) => (
                                <>
                                    <tr key={index} className="hidden md:table-row">
                                        <td className="px-4 py-2">{page === 0 ? index + 1 : page * 10 + index + 1}</td>
                                        {desktopView(item)}
                                    </tr>
                                    <tr key={`${index}_${item.id}`} className="md:hidden">
                                        <td className="w-6 px-4 py-2">{page === 0 ? index + 1 : page * 10 + index + 1}</td>
                                        {mobileView(item)}
                                    </tr>
                                </>
                            ))
                        )}

                        {!loading && !data?.data.length && <EmptyData cardHeight="!h-[20vh]" />}
                    </Table.Body>
                </Table>
                <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
            </div>
        </Modal>
    );
    function desktopView(item: MarketingClient) {
        return (
            <>
                <td className="max-w-xs px-4 py-2">
                    <span className="line-clamp-2">{generateClientFullName(item?.client)}</span>
                </td>
                <td className="px-4 py-2">
                    <span>{item?.number}</span>
                </td>
                <td className="px-4 py-2">
                    <span>{item?.total_message_count}</span>
                </td>
                <td className="px-4 py-2">
                    <span>{item?.statuses?.status || strings.pending}</span>
                </td>
            </>
        );
    }
    function mobileView(item: MarketingClient) {
        return (
            <td className="max-w-28 px-4 py-2">
                <span>{generateClientFullName(item?.client)}</span>
                <p>
                    <span>{item?.number}</span>
                </p>
                <p>
                    <span className="text-sm text-mediumGray dark:text-gray-500">{strings.credit_used}:</span> {item?.total_message_count}
                </p>
                <p>
                    <span className="text-sm text-mediumGray dark:text-gray-500">{strings.status}:</span> {item?.statuses?.status || strings.pending}
                </p>
            </td>
        );
    }
};
function Skeletons({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={`loading_${index}`}>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default SMSMarketingClientListModal;
