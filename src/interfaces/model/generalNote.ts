import { File } from "./File";
import { ClientPrescription } from "./prescription";
import { User } from "./user";

export interface GeneralNote {
    id: number;
    title: string;
    important: boolean;
    notes: string;
    notes_html?: string;
    sign?: string;
    filename?: string,
    filenames?: string[],
    prescriptions?: ClientPrescription[];
    signed_at?: string;
    client_id: number;
    sign_by_id?: number;
    created_at: string;
    updated_at?: string;
    signed_by?: User;
    file?: File,
    files?: File[],
    cancel_note?:string;
    cancelled_at?:string;
    is_cancelled?:boolean;
    cancelled_by?:User;
}