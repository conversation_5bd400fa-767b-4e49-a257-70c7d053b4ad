import { generateClientFullName } from '@/helper';
import api from '@configs/api';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { MarketingDetailClientListPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import Pagination from '@components/form/Pagination';
import Modal from '@partials/MaterialModal/Modal';
import Table from '@partials/Table/PageTable';
import Skeleton from '@components/Skeleton/Skeleton';
import Input from '@components/form/Input';
import SearchIcon from '@icons/Search';
import EmptyData from '@partials/Error/EmptyData';
import { Client } from '@interface/model/client';
import { condition } from '@provider/SMSMarketingProvider';
export interface SMSDetailsClientListViewModalProps {
    openModal: boolean;
    handleClose: () => void;
    filterCondition: condition[];
    serviceIds: number[];
}
const SMSDetailsClientListViewModal: React.FC<SMSDetailsClientListViewModalProps> = ({ filterCondition, handleClose, openModal, serviceIds }) => {
    const { data, loading, page, setPage, search, setSearch } = usePaginationSWR<MarketingDetailClientListPaginatedResponse, Error>(
        api.sms.marketing.clientListCount(
            false,
            filterCondition.filter((v) => v.type),
            serviceIds,
        ),
        { limit: 10 },
    );

    return (
        <Modal open={openModal} title={strings.clients_list} handleClose={handleClose} size="medium">
            <div className="space-y-4 p-5">
                <Input
                    placeholder={strings.Search}
                    value={search || ''}
                    icon={<SearchIcon className="text-mediumGray" />}
                    onChange={(event) => setSearch(event.target.value)}
                />
                <Table>
                    <Table.Head>
                        <Table.Th>{strings.No}</Table.Th>
                        <Table.Th>{strings.client_name}</Table.Th>
                        <Table.Th>{strings.PhoneNumber}</Table.Th>
                    </Table.Head>
                    <Table.Body>
                        {loading ? (
                            <Skeletons limit={5} />
                        ) : (
                            data?.data?.map((item, index) => (
                                <>
                                    <tr key={index} className="hidden md:table-row">
                                        <td className="px-4 py-2">{page === 0 ? index + 1 : page * 10 + index + 1}</td>
                                        {desktopView(item, index)}
                                    </tr>
                                    <tr key={`${index}_${item.id}`} className="md:hidden">
                                        <td className="px-4 py-2">{page === 0 ? index + 1 : page * 10 + index + 1}</td>
                                        {mobileView(item, index)}
                                    </tr>
                                </>
                            ))
                        )}
                        {!loading && !data?.data.length && <EmptyData cardHeight="!h-[20vh]" />}
                    </Table.Body>
                </Table>
                <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
            </div>
        </Modal>
    );
    function desktopView(item: Client, index: number) {
        return (
            <>
                <td className="max-w-xs px-4 py-2">
                    <span className="line-clamp-2">{generateClientFullName(item)}</span>
                </td>
                <td className="px-4 py-2">
                    <span>{item?.country_code && item?.country_code !== 'null' ? `+${item.country_code} ` : ''}</span>
                    <span>{item?.phone_number && item?.phone_number}</span>
                </td>
            </>
        );
    }
    function mobileView(item: Client, index: number) {
        return (
            <td className="max-w-xs px-4 py-2" key={index}>
                <span className="line-clamp-2">{generateClientFullName(item)}</span>
                <div>
                    <span>{item?.country_code && item?.country_code !== 'null' ? `+${item.country_code} ` : ''}</span>
                    <span>{item?.phone_number && item?.phone_number}</span>
                </div>
            </td>
        );
    }
};
function Skeletons({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={`loading_${index}`}>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default SMSDetailsClientListViewModal;
