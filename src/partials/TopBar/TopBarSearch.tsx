import Avatar from '@components/avatar/Avatar';
import { Combobox, ComboboxInput, ComboboxOption, ComboboxOptions, Transition } from '@headlessui/react';
import LoadingIcon from '@icons/Loading';
import cx from 'classix';
import React, { useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router';
import useSWRInfinite from 'swr/infinite';
import api from '../../configs/api';
import { commonFetch, generateClientFullName } from '../../helper';
import useDebounce from '../../hooks/useDebounce';
import { ClientsPaginatedResponse } from '../../interfaces/common';
import strings from '../../lang/Lang';
import SearchIcon from '../Icons/Search';

export interface TopBarSearchProps {}

const PAGE_SIZE = 8;

const TopBarSearch: React.FC<TopBarSearchProps> = () => {
    const [search, setSearch] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const debouncedSearch = useDebounce(search, 360);

    const { data, error, size, setSize } = useSWRInfinite<ClientsPaginatedResponse, Error>((pageIndex, previousPageData) => {
        if (!isOpen) return null;
        if (previousPageData?.data && previousPageData.next_page_url === null) return null;
        if (debouncedSearch) {
            return `${api.clients}?page=${pageIndex + 1}&per_page=${PAGE_SIZE}&search=${debouncedSearch}`;
        }
        return `${api.clients}?page=${pageIndex + 1}&per_page=${PAGE_SIZE}`;
    }, commonFetch);

    const isLoadingInitialData = !data && !error;
    const isLoadingMore = isLoadingInitialData || (size > 0 && data && typeof data[size - 1] === 'undefined');

    const isEmpty = data?.[0]?.data?.length === 0;
    const isReachingEnd = isEmpty || (data && data[data.length - 1]?.data.length < PAGE_SIZE);

    const searchClientBox = useRef<HTMLUListElement>(null);
    const lastSecondBoxRef = useRef<HTMLDivElement>(null);
    const navigate = useNavigate();

    const list = useMemo(() => data?.flatMap((v) => v?.data).filter((c) => !!c), [data]);

    async function onScroll() {
        if (
            !isLoadingMore &&
            !isReachingEnd &&
            searchClientBox?.current &&
            lastSecondBoxRef?.current &&
            searchClientBox.current.getBoundingClientRect().bottom > lastSecondBoxRef.current.getBoundingClientRect().top
        ) {
            setSize((size) => size + 1);
        }
    }

    function onClientClick(id: number | null) {
        if (id !== null) {
            navigate(`/clients/${id}`);
        }
    }

    return (
        <div className="relative w-full flex-grow">
            <Combobox<number> onChange={(c) => onClientClick(c)} immediate>
                <div className="relative flex items-center">
                    <span className={cx('pointer-events-none absolute left-2 hidden md:block', isOpen ? 'opacity-0' : '')}>
                        <SearchIcon className="text-2xl text-mediumGray" />
                    </span>

                    <ComboboxInput
                        type="text"
                        placeholder={`${strings.Search} ${strings.Clients.toLowerCase()}`}
                        className={cx(
                            'top_bar_search w-full rounded-t-xl border border-b-0 bg-transparent py-3 pl-3 pr-3 outline-none transition-all placeholder:text-mediumGray',
                            isOpen ? 'bg-white dark:border-gray-700 dark:bg-dimGray md:pl-6' : 'border-transparent md:pl-10',
                        )}
                        id="top_bar_client_search_input"
                        onChange={(event) => {
                            setSearch(event.currentTarget.value);
                            setSize(1);
                        }}
                    />
                </div>
                <Transition
                    className={cx(
                        'dark:ring-dark-400 absolute left-0 top-full max-h-96 w-full divide-y divide-primary/10 overflow-hidden rounded-b-xl border border-t-0 bg-white ring-gray-200 transition-all dark:bg-dimGray',
                        isOpen ? 'dark:border-gray-700' : 'border-transparent',
                    )}
                    as={'div'}
                    enterFrom="h-0 invisible"
                    enterTo="h-96 visible"
                    leaveFrom="h-96 visible"
                    leaveTo="h-0 invisible"
                    id="searchScroller"
                    beforeEnter={() => setIsOpen(true)}
                    afterLeave={() => {
                        setIsOpen(false);
                        setSearch('');
                    }}
                >
                    <ComboboxOptions
                        modal={false}
                        className={'soft-searchbar relative h-96 overflow-y-auto px-2 pb-3'}
                        onScroll={onScroll}
                        ref={searchClientBox}
                    >
                        {list?.map((client, index) => (
                            <ComboboxOption value={client.id} key={index} className="block w-full">
                                {({ focus }) => (
                                    <div ref={list.length - 2 === index ? lastSecondBoxRef : undefined}>
                                        <div
                                            className={cx(
                                                'flex w-full cursor-pointer items-center space-x-2 break-all rounded-lg border px-3 py-2 hover:border-gray-200 hover:bg-primary/5 active:bg-primary/10 dark:hover:border-gray-700 dark:active:bg-primaryLight/15 md:px-4 md:py-4',
                                                focus ? 'border-gray-200 bg-primary/5 dark:border-gray-700' : 'border-transparent',
                                            )}
                                        >
                                            <Avatar
                                                className="h-10 w-10"
                                                key={client?.profile_picture ? `${api.storage}${client?.profile_picture}` : `image_top_bar${index}`}
                                                src={client?.profile_picture ? `${api.storage}${client?.profile_picture}` : undefined}
                                                alt={`${generateClientFullName(client)} ${strings.ProfilePicture}`}
                                            />
                                            <p>{generateClientFullName(client)}</p>
                                        </div>
                                    </div>
                                )}
                            </ComboboxOption>
                        ))}
                        {isEmpty && <p className="pt-4 text-center text-gray-600 dark:text-gray-400">{strings.no_data}</p>}
                        {isLoadingInitialData && <TopBarSearchSkeleton />}
                        {!isLoadingInitialData && isLoadingMore && !isReachingEnd && <TopBarSearchSkeleton />}
                    </ComboboxOptions>
                </Transition>
            </Combobox>
        </div>
    );
};

function TopBarSearchSkeleton() {
    return (
        <div className="flex items-center justify-center space-x-2 py-4">
            <LoadingIcon className="text-xl text-primary" />
            <p className="text-mediumGray">{strings.Loading}...</p>
        </div>
    );
}
export default TopBarSearch;
