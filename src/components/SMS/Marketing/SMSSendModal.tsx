import Button from '@components/form/Button';
import InfoCard from '@components/form/InfoCard';
import Select from '@components/form/Select';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import useDebounce from '@hooks/useDebounce';
import { CompanySMSCreditsResponse, MarketingCreditCountResponse, SMSTemplatesResponse } from '@interface/common';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';
import { useSMSMarketing } from '@provider/SMSMarketingProvider';
import useSWR from 'swr';
import SMSCustomTextArea from '../Templates/SMSCustomTextArea';
import ServerError from '@partials/Error/ServerError';
import cx from 'classix';
import React from 'react';
import useAuth from '@hooks/useAuth';

export interface SMSSendModalProps {
    openModal: boolean;
    handleClose: () => void;
}

const keys = [
    { key: 'clinic_name', name: 'Clinic name' },
    { key: 'clinic_location', name: 'Clinic location' },
    { key: 'clinic_phone', name: 'Clinic phone' },
    { key: 'practitioner_name', name: 'Practitioner name' },
    { key: 'client_first_name', name: 'Client first name' },
    { key: 'client_last_name', name: 'Client last name' },
    { key: 'client_cell_phone', name: 'Client cell phone' },
    { key: 'client_email', name: 'Client email' },
    { key: 'booking_portal_url', name: 'Booking portal url', type: 'booking' },
    { key: 'booking_portal_service_url', name: 'Booking portal service url', type: 'booking_service' },
    // { key: 'booking_date', name: 'Booking date', type: 'booking' },
    // { key: 'booking_start_time', name: 'Booking start time', type: 'booking' },
    // { key: 'booking_end_time', name: 'Booking end time', type: 'booking' },
    // { key: 'service_name', name: 'Service name', type: 'booking' },
    // { key: 'service_duration', name: 'Service duration', type: 'booking' },
    // { key: 'service_price', name: 'Service price', type: 'booking' },
];

const SMSSendModal: React.FC<SMSSendModalProps> = ({ openModal, handleClose }) => {
    const { data: smsData, isLoading: isSMSLoading } = useSWR<SMSTemplatesResponse>(api.sms.templates.activeList);
    const { formik, filterCondition } = useSMSMarketing();
    const {
        userType: { isSuperAdmin: isSuperUser, isUser },
    } = useAuth();
    const [creditError, setCreditError] = React.useState(false);
    const { values, handleChange, handleBlur, isSubmitting, errors, touched, handleSubmit, setFieldValue, setFieldTouched } = formik ?? {};
    function findSMS(id: number) {
        return smsData?.data.find((s) => s.id === id)?.title ?? '';
    }
    function onChipClick(name: string) {
        setFieldValue('description', values?.description + `{{${name}}}`);
    }
    const debouncedValues = useDebounce(values, 1000);

    const { data, isLoading, error } = useSWR<MarketingCreditCountResponse, Error>(
        () => {
            const isValid = debouncedValues.client_type && debouncedValues.message_name;

            if (!isValid) {
                return null;
            }

            return api.sms.marketing.listFilter(
                debouncedValues.description,
                filterCondition.filter((v) => v.type),
                debouncedValues.service,
            );
        },
        {
            revalidateOnFocus: false,
            refreshInterval: 0,
            errorRetryCount: 0,
        },
    );
    const { data: creditData } = useSWR<CompanySMSCreditsResponse>(api.sms.myCredits);
    return (
        <Modal open={openModal} handleClose={handleClose} size="large" title={strings.send_sms}>
            <div className="grid grid-cols-1 gap-6 divide-x p-4 dark:divide-gray-700 lg:grid-cols-2">
                <div className="space-y-4">
                    <Select
                        value={values?.template_id || ''}
                        onChange={(value) => {
                            setFieldValue('template_id', value);
                            setFieldTouched('description');
                            setFieldValue('description', smsData?.data.find((s) => s.id === parseInt(value))?.description);
                        }}
                        loading={isSMSLoading}
                        placeholder={strings.Select}
                        label={strings.Template}
                        displayValue={(value) => findSMS(parseInt(value?.toString() ?? '0'))}
                    >
                        {smsData?.data.map((item, index) => <Select.Option key={index} value={item.id} children={item.title} />)}
                    </Select>
                    <SMSCustomTextArea
                        label={strings.sms_text}
                        onBlur={handleBlur}
                        required
                        name="description"
                        value={values?.description}
                        onChange={handleChange}
                        placeholder={strings.type_sms}
                        error={touched?.description && errors?.description}
                    />
                    <div className="lg:hidden">
                        <Heading text={strings.insert_value} />
                        <div className="soft-searchbar mt-5 grid grid-flow-col grid-rows-2 gap-x-3 gap-y-2.5 overflow-auto">
                            {keys.map((e, i) => (
                                <SMSChip key={i} item={e} onClick={(name) => onChipClick(name)} />
                            ))}
                        </div>
                    </div>
                    <InfoCard variant="warning" message={strings.sms_warning_text} />
                </div>
                <div className="hidden px-6 lg:block">
                    <Heading text={strings.insert_value} />
                    <div className="mt-5 flex flex-wrap gap-x-3 gap-y-2.5">
                        {keys.map((e, i) => (
                            <SMSChip
                                key={i}
                                item={e}
                                onClick={(name) => onChipClick(name)}
                                hidden={
                                    (e.type === 'booking' && !filterCondition?.find((v) => v.type === 'booking')?.key) ||
                                    (e.type === 'booking_service' && values?.service?.length !== 1)
                                }
                            />
                        ))}
                    </div>
                </div>
            </div>
            <div className="mt-4 px-4">
                <ServerError error={errors?.server} />
                {creditError && (
                    <div className="rounded-md bg-error bg-opacity-10 px-4 py-3 text-xs text-error">
                        {strings.insufficient_credits_to_send_SMS}{' '}
                        {isSuperUser && (
                            <span className="cursor-pointer underline" onClick={() => window.open('/settings/credits', '_blank')}>
                                {strings.click_buy_credits.toLowerCase()}
                            </span>
                        )}
                    </div>
                )}
            </div>
            {mobileButton()}
            <div className="hidden flex-wrap justify-between p-4 md:flex">
                <Button onClick={handleClose} variant="ghost">
                    {strings.back}
                </Button>
                <div className="flex items-center gap-4">
                    <p className="p-2">
                        {strings.available_credits}: {creditData?.data.credits}
                    </p>
                    <Button
                        type="submit"
                        onClick={() => {
                            creditData?.data && data?.data && creditData?.data?.credits < parseInt(data?.data)
                                ? setCreditError(true)
                                : handleSubmit();
                        }}
                        loading={isSubmitting || isLoading}
                        disabled={!!error?.message}
                    >
                        {strings.formatString(strings.send_sms_credits, data?.data || 0)}
                    </Button>
                </div>
            </div>
        </Modal>
    );
    function mobileButton() {
        return (
            <div className="block space-y-2 px-4 pb-4 md:hidden">
                <p className="p-2">
                    {strings.available_credits}: {creditData?.data.credits}
                </p>
                <div className="grid grid-cols-2 items-center gap-4">
                    <Button onClick={handleClose} variant="ghost">
                        <span className="text-sm md:text-balance"> {strings.back}</span>
                    </Button>
                    <Button
                        type="submit"
                        onClick={() => {
                            creditData?.data && data?.data && creditData?.data?.credits < parseInt(data?.data)
                                ? setCreditError(true)
                                : handleSubmit();
                        }}
                        loading={isSubmitting || isLoading}
                    >
                        <span className="text-xs md:text-balance">{strings.formatString(strings.send_sms_credits, data?.data || 0)}</span>
                    </Button>
                </div>
            </div>
        );
    }
};
interface Item {
    key: string;
    name: string;
}

const SMSChip = ({ item, onClick, hidden }: { item: Item; onClick: (name: string) => void; hidden?: boolean }) => {
    return (
        <span
            className={cx('cursor-pointer whitespace-pre rounded-full bg-primary/10 px-3 py-1.5 text-sm dark:bg-primaryLight/20', hidden && 'hidden')}
            onClick={() => onClick(item.key)}
        >
            <p>{item.name}</p>
        </span>
    );
};

export default SMSSendModal;
