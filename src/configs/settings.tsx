const Settings = {
    SHOW_HEALTH_QUESTIONNAIRE: 'SHOW_HEALTH_QUESTIONNAIRE',
    SHOW_AESTHETIC_INTEREST: 'SHOW_AESTHETIC_INTEREST',
    CLIENT_ACCESS_FULL_ACCESS: 'CLIENT_ACCESS_FULL_ACCESS',
    SHOW_LETTER_OF_CONSENT: 'SHOW_LETTER_OF_CONSENT',
    SHOW_COVID_19: 'SHOW_COVID_19',
    SEND_CLIENT_WELCOME_EMAIL: 'SEND_CLIENT_WELCOME_EMAIL',
    SHOW_2FA: 'SHOW_2FA',
    PORTAL_VIEW_DATE_OF_BIRTH: 'PORTAL_VIEW_DATE_OF_BIRTH',
    PORTAL_VIEW_CITY: 'PORTAL_VIEW_CITY',
    PORTAL_VIEW_PHONE: 'PORTAL_VIEW_PHONE',
    PORTAL_VIEW_STREET_ADDRESS: 'PORTAL_VIEW_STREET_ADDRESS',
    R<PERSON><PERSON><PERSON>RED_CONSENT: 'REQUIRED_CONSENT',
    PORTAL_VIEW_OCCUPATION: 'PORTAL_VIEW_OCCUPATION',
    PORTAL_REQUIRED_OCCUPATION: 'PORTAL_REQUIRED_OCCUPATION',
    PORTAL_REQUIRED_DATE_OF_BIRTH: 'PORTAL_REQUIRED_DATE_OF_BIRTH',
    PORTAL_REQUIRED_CITY: 'PORTAL_REQUIRED_CITY',
    PORTAL_REQUIRED_PHONE: 'PORTAL_REQUIRED_PHONE',
    PORTAL_REQUIRED_STREET_ADDRESS: 'PORTAL_REQUIRED_STREET_ADDRESS',
    PORTAL_VIEW_ZIPCODE: 'PORTAL_VIEW_ZIPCODE',
    PORTAL_REQUIRED_ZIPCODE: 'PORTAL_REQUIRED_ZIPCODE',
    PORTAL_VIEW_STATE: 'PORTAL_VIEW_STATE',
    PORTAL_REQUIRED_STATE: 'PORTAL_REQUIRED_STATE',
    PORTAL_VIEW_COUNTRY: 'PORTAL_VIEW_COUNTRY',
    PORTAL_REQUIRED_COUNTRY: 'PORTAL_REQUIRED_COUNTRY',
    PORTAL_VIEW_PROFILE: 'PORTAL_VIEW_PROFILE',
    PORTAL_REQUIRED_PROFILE: 'PORTAL_REQUIRED_PROFILE',
    SUPER_USER_MAIL_WHEN_CLIENT_REGISTER: 'SUPER_USER_MAIL_WHEN_CLIENT_REGISTER',
    NUMBER_OF_EMPLOYEES: 'NUMBER_OF_EMPLOYEES',
    TIME_INCREMENTS: 'TIME_INCREMENTS',
    MINIMUM_LEAD_TIME: 'MINIMUM_LEAD_TIME',
    MAXIMUM_LEAD_TIME: 'MAXIMUM_LEAD_TIME',
    INTERNAL_BOOKING_CONFIRMATION: 'INTERNAL_BOOKING_CONFIRMATION',
    INTERNAL_BOOKING_CANCELLATION: 'INTERNAL_BOOKING_CANCELLATION',
    INTERNAL_BOOKING_RESCHEDULE: 'INTERNAL_BOOKING_RESCHEDULE',
    CLIENT_EMAIL_REMINDER: 'CLIENT_EMAIL_REMINDER',
    BOOKING_POLICY_LINK: 'BOOKING_POLICY_LINK',
    BOOKING_POLICY: 'BOOKING_POLICY',
    BOOKING_SECURITY: 'BOOKING_SECURITY',
    BOOKING_PORTAL_TEXT: 'BOOKING_PORTAL_TEXT',
    BOOKING_POLICY_LINK_SELECTED: 'BOOKING_POLICY_LINK_SELECTED',
    DATE_FORMAT: 'DATE_FORMAT',
    CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE: 'CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE',
    SHOW_CUSTOM_FIELD: 'SHOW_CUSTOM_FIELD',
    GTM_ID: 'GTM_ID',
    BOOKING_CANCELLATION_AND_MODIFY_POLICY: 'BOOKING_CANCELLATION_AND_MODIFY_POLICY',
    SMS_CLIENT_BOOKING_REMINDER_TIME: 'SMS_CLIENT_BOOKING_REMINDER_TIME',
    SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID: 'SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID',
    SMS_CLIENT_BOOKING_CONFIRMATION_ON: 'SMS_CLIENT_BOOKING_CONFIRMATION_ON',
    SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID: 'SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID',
    SMS_CLIENT_BOOKING_CANCELLATION_ON: 'SMS_CLIENT_BOOKING_CANCELLATION_ON',
    SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID: 'SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID',
    SMS_INTERNAL_BOOKING_CONFIRMATION_ON: 'SMS_INTERNAL_BOOKING_CONFIRMATION_ON',
    SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID: 'SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID',
    SMS_INTERNAL_BOOKING_CANCELLATION_ON: 'SMS_INTERNAL_BOOKING_CANCELLATION_ON',
    SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID: 'SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID',
    Z_REPORT_EMAIL: 'Z_REPORT_EMAIL',
    POS_WAITING_LIST: 'POS_WAITING_LIST',
    SMS_WAITLIST_JOINED: 'SMS_WAITLIST_JOINED',
    CALENDAR_CUSTOM_TIME_SLOTS: 'CALENDAR_CUSTOM_TIME_SLOTS',
    LOC_SIGNATURE: 'LOC_SIGNATURE',
    NRS_RATING: 'NRS_RATING',
    BANK_ID_VERIFICATION_ENABLED: 'BANK_ID_VERIFICATION_ENABLED',
    CALENDAR_CUSTOM_START_TIME: 'CALENDAR_CUSTOM_START_TIME',
    CALENDAR_CUSTOM_END_TIME: 'CALENDAR_CUSTOM_END_TIME',
    BOOKING_NOTIFICATION_TO_ITS_PRACTITIONER: 'BOOKING_NOTIFICATION_TO_ITS_PRACTITIONER',
    QUESTIONNAIRE_OR_LOC_NOTIFICATION: 'QUESTIONNAIRE_OR_LOC_NOTIFICATION',
    RESCHEDULE_NOTIFICATION_TO_ITS_PRACTITIONER: 'RESCHEDULE_NOTIFICATION_TO_ITS_PRACTITIONER',
    NOTE_MADE_ON_BOOKING_NOTIFICATION_TO_ITS_PRACTITIONER: 'NOTE_MADE_ON_BOOKING_NOTIFICATION_TO_ITS_PRACTITIONER',
    PAYMENT_FAILED_NOTIFICATION: 'PAYMENT_FAILED_NOTIFICATION',
    LANGUAGE: 'LANGUAGE',
    CUSTOMER_LANGUAGE: 'CUSTOMER_LANGUAGE',
    POS_LICENSE: 'POS_LICENSE',
    POS_SWISH_QR: 'POS_SWISH_QR',
    VIVA_WALLET_SOURCE_CODE: 'VIVA_WALLET_SOURCE_CODE',
    BOOKING_EXTRAS_STEPS_REMINDER: 'BOOKING_EXTRAS_STEPS_REMINDER',
    BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME: 'BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME',
    BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID: 'BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID',
    SMS_SENDER_ID: 'SMS_SENDER_ID',
    ONLINE_PAYMENT: 'ONLINE_PAYMENT',
    EMAIL_CLIENT_BOOKING_ON: 'EMAIL_CLIENT_BOOKING_ON',
    EMAIL_CLIENT_BOOKING_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_CONFIRMATION_ON: 'EMAIL_CLIENT_BOOKING_CONFIRMATION_ON',
    EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_CANCELLATION_ON: 'EMAIL_CLIENT_BOOKING_CANCELLATION_ON',
    EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_RESCHEDULED_ON: 'EMAIL_CLIENT_BOOKING_RESCHEDULED_ON',
    EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_UPDATED_ON: 'EMAIL_CLIENT_BOOKING_UPDATED_ON',
    EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_UPDATED_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_REMINDER_ON: 'EMAIL_CLIENT_BOOKING_REMINDER_ON',
    EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_PRACTITIONER_CHANGED_ON: 'EMAIL_CLIENT_BOOKING_PRACTITIONER_CHANGED_ON',
    EMAIL_CLIENT_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID',
    EMAIL_CLIENT_SIGNED_LOC_ON: 'EMAIL_CLIENT_SIGNED_LOC_ON',
    EMAIL_CLIENT_SIGNED_LOC_TEMPLATE_ID: 'EMAIL_CLIENT_SIGNED_LOC_TEMPLATE_ID',
    EMAIL_CLIENT_SEND_AFTER_CARE_ON: 'EMAIL_CLIENT_SEND_AFTER_CARE_ON',
    EMAIL_CLIENT_SEND_AFTER_CARE_TEMPLATE_ID: 'EMAIL_CLIENT_SEND_AFTER_CARE_TEMPLATE_ID',
    EMAIL_INTERNAL_BOOKING_CONFIRMATION_ON: 'EMAIL_INTERNAL_BOOKING_CONFIRMATION_ON',
    EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID: 'EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID',
    EMAIL_INTERNAL_BOOKING_CANCELLATION_ON: 'EMAIL_INTERNAL_BOOKING_CANCELLATION_ON',
    EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID: 'EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID',
    EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_ON: 'EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_ON',
    EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID: 'EMAIL_INTERNAL_BOOKING_PRACTITIONER_CHANGED_TEMPLATE_ID',
    EMAIL_INTERNAL_BOOKING_RESCHEDULED_ON: 'EMAIL_INTERNAL_BOOKING_RESCHEDULED_ON',
    EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID: 'EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID',
    EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_ON: 'EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_ON',
    EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID: 'EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID',
    EMAIL_CLIENT_REGISTRATION_TEMPLATE_ID: 'EMAIL_CLIENT_REGISTRATION_TEMPLATE_ID',
    HIPPA_TERMS: 'HIPPA_TERMS',
    HIPPA_TERMS_SV: 'HIPPA_TERMS_SV',
    BOOKING_PLAN: 'BOOKING_PLAN',
    BOOKING_SUBSCRIPTION_USERS: 'BOOKING_SUBSCRIPTION_USERS',
    HOLIDAY_CALENDARS: 'HOLIDAY_CALENDARS',
    SHOW_LETTER_OF_CONSENT_IDS: 'SHOW_LETTER_OF_CONSENT_IDS',
    MAGNETIC_BOOKING_ENABLED: 'MAGNETIC_BOOKING_ENABLED',
};

export type SettingKey = keyof typeof Settings;

export default Settings;
