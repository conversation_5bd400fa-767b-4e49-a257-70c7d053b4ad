import Skeleton from '@components/Skeleton/Skeleton';
import Input from '@components/form/Input';
import Pagination from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import useClient from '@hooks/useClient';
import SearchIcon from '@icons/Search';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import Card from '@partials/Paper/PagePaper';
import Table from '@partials/Table/PageTable';
import MainCardBookingProvider, { useMainCardBooking } from '@provider/MainCard/MainCardBookingProvider';
import React from 'react';
import { useNavigate } from 'react-router';
import BackToClientDashboard from '../sections/BackToClientDashboard';
import ClientBookingRecordListItem from './ClientBookingRecordListItem';

export interface ClientBookingRecordProps {}

const ClientBookingRecordView: React.FC<ClientBookingRecordProps> = () => {
    const navigate = useNavigate();
    const { clientId } = useClient();

    const {
        setOpenBookingModal,
        setDeleteBookingOpen,
        setBookingHistoryOpen,
        setSelectedBooking,
        data,
        loading,
        orderBy,
        orderDirection,
        handleOrder,
        page,
        setPage,
        search,
        setSearch,
    } = useMainCardBooking();

    return (
        <>
            <BackToClientDashboard />
            <Card className="space-y-4">
                <div className="flex flex-wrap items-center justify-between space-y-2 md:space-y-0">
                    <Heading text={strings.booking_records} variant="bigTitle" />
                    <Input
                        placeholder={strings.Search}
                        value={search || ''}
                        icon={<SearchIcon className="text-mediumGray" />}
                        onChange={(event) => setSearch(event.target.value)}
                    />
                </div>
                <Table>
                    <Table.Head>
                        <Table.ThSort sort={orderBy === 'service_name' && orderDirection} onClick={() => handleOrder('service_name')}>
                            {strings.service}
                        </Table.ThSort>
                        <Table.ThSort sort={orderBy === 'practitioner_name' && orderDirection} onClick={() => handleOrder('practitioner_name')}>
                            {strings.practitioner}
                        </Table.ThSort>
                        <Table.ThSort sort={orderBy === 'booked_time' && orderDirection} onClick={() => handleOrder('booked_time')}>
                            {strings.booked_time_slot}
                        </Table.ThSort>
                        <Table.ThSort sort={orderBy === 'status' && orderDirection} onClick={() => handleOrder('status')}>
                            {strings.status}
                        </Table.ThSort>
                        <Table.Th children={strings.Actions} className="text-right" />
                    </Table.Head>
                    <Table.Body>
                        {data?.data.map((booking) => (
                            <ClientBookingRecordListItem
                                key={booking.id}
                                onProfileClick={() => navigate(`/clients/${clientId}/booking/${booking?.id}`)}
                                onEditClick={() => {
                                    setSelectedBooking(booking);
                                    setOpenBookingModal(true);
                                }}
                                onDeleteClick={() => {
                                    setSelectedBooking(booking);
                                    setDeleteBookingOpen(true);
                                }}
                                onHistoryClick={() => {
                                    setSelectedBooking(booking);
                                    setBookingHistoryOpen(true);
                                }}
                                booking={booking}
                            />
                        ))}
                        {loading && <ClientBookingRecordSkeleton limit={10} />}
                        {!loading && !data?.data.length && <EmptyData cardHeight="!h-[61vh]" />}
                    </Table.Body>
                </Table>
                <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
            </Card>
        </>
    );
};

function ClientBookingRecordSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}

const ClientBookingRecordViewHoC: React.FC = () => <MainCardBookingProvider children={<ClientBookingRecordView />} />;
export default ClientBookingRecordViewHoC;
