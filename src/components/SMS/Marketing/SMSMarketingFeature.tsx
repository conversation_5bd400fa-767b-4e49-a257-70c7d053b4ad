import Button from '@components/form/Button';
import MarketingEmpty from '@icons/MarketingEmpty';
import strings from '@lang/Lang';

export interface SMSMarketingFeatureProps {
    onClick: () => void;
}
const SMSMarketingFeature: React.FC<SMSMarketingFeatureProps> = ({ onClick }) => {
    return (
        <tr>
            <td className={`h-[60vh]`} colSpan={10}>
                <div className="flex h-full justify-center pt-10 md:pt-20">
                    <div className="items-center justify-center gap-8 md:flex">
                        <div className="flex justify-center">
                            <MarketingEmpty className="text-[150px] md:text-[292px]" />
                        </div>
                        <div className="max-w-lg space-y-3 p-4 md:p-0">
                            <p className="font-semibold">{strings.how_marketing_sms_works}</p>
                            <p>
                                <span className="font-semibold">1. {strings.client_segmentation}</span>:{' '}
                                <span className="text-sm">{strings.client_segmentation_note}</span>
                            </p>
                            <p>
                                <span className="font-semibold">2. {strings.personalisation}</span>:{' '}
                                <span className="text-sm">{strings.personalisation_note}</span>
                            </p>{' '}
                            <p>
                                <span className="font-semibold">3. {strings.template_library}</span>:{' '}
                                <span className="text-sm">{strings.template_library_note}</span>
                            </p>{' '}
                            <p>
                                <span className="font-semibold">4. {strings.credit_management}</span>:{' '}
                                <span className="text-sm">{strings.credit_management_note}</span>
                            </p>
                            <Button className="!mt-8" onClick={onClick}>
                                {strings.send_marketing_sms}
                            </Button>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    );
};
export default SMSMarketingFeature;
