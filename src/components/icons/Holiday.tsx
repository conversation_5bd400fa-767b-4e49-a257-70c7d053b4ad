import * as React from "react"
import { SVGProps } from "react"
const HolidayIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="2em"
    height="2em"
    viewBox="0 0 186 173"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M136.12 9.02701C136.12 9.02701 109.235 -6.01257 78.3813 5.40509C47.5274 16.8228 27.8704 46.562 11.3066 70.2453C-5.25714 93.9286 -2.71736 121.316 16.6905 136.907C36.0984 152.499 53.6829 137.371 83.9132 141.63C114.143 145.889 121.501 151.564 149.362 138.821C177.222 126.079 188.576 86.7245 173.945 53.6883C159.315 20.6525 136.12 9.02701 136.12 9.02701Z"
      fill="#5551CE"
    />
    <path
      opacity={0.8}
      d="M136.12 9.02701C136.12 9.02701 109.235 -6.01257 78.3813 5.40509C47.5274 16.8228 27.8704 46.562 11.3066 70.2453C-5.25714 93.9286 -2.71736 121.316 16.6905 136.907C36.0984 152.499 53.6829 137.371 83.9132 141.63C114.143 145.889 121.501 151.564 149.362 138.821C177.222 126.079 188.576 86.7245 173.945 53.6883C159.315 20.6525 136.12 9.02701 136.12 9.02701Z"
      fill="white"
    />
    <path
      d="M182.072 168.463C182.072 170.486 141.89 172.126 92.3232 172.126C42.7563 172.126 2.57471 170.486 2.57471 168.463C2.57471 166.439 42.7563 164.799 92.3232 164.799C141.89 164.799 182.072 166.439 182.072 168.463Z"
      fill="#5551CE"
    />
    <path
      opacity={0.5}
      d="M182.072 168.463C182.072 170.486 141.89 172.126 92.3232 172.126C42.7563 172.126 2.57471 170.486 2.57471 168.463C2.57471 166.439 42.7563 164.799 92.3232 164.799C141.89 164.799 182.072 166.439 182.072 168.463Z"
      fill="white"
    />
    <path
      d="M169.817 131.503C167.965 133.641 167.476 135.961 167.375 136.581L162.471 139.331V126.337L162.567 126.468L170.377 120.752C171.361 120.906 175.152 121.335 178.607 119.475C182.616 117.316 185.547 109.914 185.547 109.914C185.547 109.914 175.986 108.835 172.747 112.844C170.119 116.099 170.03 119.251 170.09 120.323L162.471 125.9V116.303C163.387 115.898 167.064 114.008 168.275 109.297C169.663 103.9 162.261 93.2598 162.261 93.2598C162.261 93.2598 154.55 104.209 156.093 109.76C157.399 114.465 160.911 115.953 161.955 116.299V137.109L156.456 132.526C156.583 131.431 156.681 128.001 153.625 124.563C149.925 120.4 140.055 120.708 140.055 120.708C140.055 120.708 144.219 129.035 147.766 131.811C150.865 134.237 155.142 133.247 156.172 132.96L161.956 137.78V144.106H153.359L156.866 166.028H167.388L170.896 144.106H162.471V139.922L167.857 136.902C169.168 137.265 172.828 138.016 176.294 136.283C180.612 134.124 184.159 129.498 184.159 129.498C184.159 129.498 173.827 126.877 169.817 131.503Z"
      fill="#5551CE"
    />
    <path
      opacity={0.5}
      d="M169.817 131.503C167.965 133.641 167.476 135.961 167.375 136.581L162.471 139.331V126.337L162.567 126.468L170.377 120.752C171.361 120.906 175.152 121.335 178.607 119.475C182.616 117.316 185.547 109.914 185.547 109.914C185.547 109.914 175.986 108.835 172.747 112.844C170.119 116.099 170.03 119.251 170.09 120.323L162.471 125.9V116.303C163.387 115.898 167.064 114.008 168.275 109.297C169.663 103.9 162.261 93.2598 162.261 93.2598C162.261 93.2598 154.55 104.209 156.093 109.76C157.399 114.465 160.911 115.953 161.955 116.299V137.109L156.456 132.526C156.583 131.431 156.681 128.001 153.625 124.563C149.925 120.4 140.055 120.708 140.055 120.708C140.055 120.708 144.219 129.035 147.766 131.811C150.865 134.237 155.142 133.247 156.172 132.96L161.956 137.78V144.106H153.359L156.866 166.028H167.388L170.896 144.106H162.471V139.922L167.857 136.902C169.168 137.265 172.828 138.016 176.294 136.283C180.612 134.124 184.159 129.498 184.159 129.498C184.159 129.498 173.827 126.877 169.817 131.503Z"
      fill="white"
    />
    <path
      d="M148.447 93.6677H30.422C27.0993 93.6677 24.4058 90.9742 24.4058 87.6515V13.2721C24.4058 9.94935 27.0993 7.25586 30.422 7.25586H148.447C151.77 7.25586 154.463 9.94935 154.463 13.2721V87.6515C154.463 90.9742 151.77 93.6677 148.447 93.6677Z"
      fill="#5551CE"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      opacity={0.7}
      d="M148.447 93.6677H30.422C27.0993 93.6677 24.4058 90.9742 24.4058 87.6515V13.2721C24.4058 9.94935 27.0993 7.25586 30.422 7.25586H148.447C151.77 7.25586 154.463 9.94935 154.463 13.2721V87.6515C154.463 90.9742 151.77 93.6677 148.447 93.6677Z"
      fill="#5551CE"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path d="M79.2601 55.5605H64.0415V64.0959H79.2601V55.5605Z" fill="white" />
    <path d="M79.2601 65.6426H64.0415V74.1775H79.2601V65.6426Z" fill="white" />
    <path d="M96.0259 55.5605H80.8074V64.0959H96.0259V55.5605Z" fill="white" />
    <path d="M112.792 45.4785H97.5735V54.0134H112.792V45.4785Z" fill="white" />
    <path d="M112.792 35.3965H97.5735V43.9319H112.792V35.3965Z" fill="white" />
    <path d="M112.792 16.7617H97.5735V23.7662H112.792V16.7617Z" fill="white" />
    <path d="M96.0259 16.7617H80.8074V23.7662H96.0259V16.7617Z" fill="white" />
    <path d="M96.0259 65.6426H80.8074V74.1775H96.0259V65.6426Z" fill="white" />
    <path d="M96.0259 25.3145H80.8074V33.8493H96.0259V25.3145Z" fill="white" />
    <path d="M96.0259 35.3965H80.8074V43.9319H96.0259V35.3965Z" fill="white" />
    <path d="M96.0259 45.4785H80.8074V54.0134H96.0259V45.4785Z" fill="white" />
    <path d="M112.792 65.6426H97.5735V74.1775H112.792V65.6426Z" fill="white" />
    <path d="M45.7282 25.3145H29.5054V33.8493H45.7282V25.3145Z" fill="white" />
    <path d="M45.7282 45.4785H29.5054V54.0134H45.7282V45.4785Z" fill="white" />
    <path d="M45.7282 55.5605H29.5054V64.0959H45.7282V55.5605Z" fill="white" />
    <path d="M45.7282 35.3965H29.5054V43.9319H45.7282V35.3965Z" fill="white" />
    <path d="M147.328 16.7617H131.105V23.7662H147.328V16.7617Z" fill="white" />
    <path d="M45.7282 65.6426H29.5054V74.1775H45.7282V65.6426Z" fill="white" />
    <path d="M129.558 25.3145H114.339V33.8493H129.558V25.3145Z" fill="white" />
    <path d="M112.792 55.5605H97.5735V64.0959H112.792V55.5605Z" fill="white" />
    <path d="M129.558 55.5605H114.339V64.0959H129.558V55.5605Z" fill="white" />
    <path d="M129.558 35.3965H114.339V43.9319H129.558V35.3965Z" fill="white" />
    <path d="M129.558 45.4785H114.339V54.0134H129.558V45.4785Z" fill="white" />
    <path d="M129.558 65.6426H114.339V74.1775H129.558V65.6426Z" fill="white" />
    <path d="M112.792 25.3145H97.5735V33.8493H112.792V25.3145Z" fill="white" />
    <path d="M147.328 65.6426H131.105V74.1775H147.328V65.6426Z" fill="white" />
    <path d="M129.558 16.7617H114.339V23.7662H129.558V16.7617Z" fill="white" />
    <path d="M147.328 55.5605H131.105V64.0959H147.328V55.5605Z" fill="white" />
    <path d="M129.558 75.7266H114.339V84.7473H129.558V75.7266Z" fill="white" />
    <path d="M147.328 45.4785H131.105V54.0134H147.328V45.4785Z" fill="white" />
    <path d="M96.0259 75.7266H80.8074V84.7473H96.0259V75.7266Z" fill="white" />
    <path d="M112.792 75.7266H97.5735V84.7473H112.792V75.7266Z" fill="white" />
    <path d="M147.328 35.3965H131.105V43.9319H147.328V35.3965Z" fill="white" />
    <path d="M147.328 75.7266H131.105V84.7473H147.328V75.7266Z" fill="white" />
    <path d="M45.7282 75.7266H29.5054V84.7473H45.7282V75.7266Z" fill="white" />
    <path d="M147.328 25.3145H131.105V33.8493H147.328V25.3145Z" fill="white" />
    <path d="M45.7282 16.7617H29.5054V23.7662H45.7282V16.7617Z" fill="white" />
    <path d="M62.4942 16.7617H47.2756V23.7662H62.4942V16.7617Z" fill="white" />
    <path d="M62.4942 55.5605H47.2756V64.0959H62.4942V55.5605Z" fill="white" />
    <path d="M79.2601 16.7617H64.0415V23.7662H79.2601V16.7617Z" fill="white" />
    <path d="M79.2601 35.3965H64.0415V43.9319H79.2601V35.3965Z" fill="white" />
    <path d="M62.4942 65.6426H47.2756V74.1775H62.4942V65.6426Z" fill="white" />
    <path d="M79.2601 25.3145H64.0415V33.8493H79.2601V25.3145Z" fill="white" />
    <path d="M62.4942 25.3145H47.2756V33.8493H62.4942V25.3145Z" fill="white" />
    <path d="M79.2601 45.4785H64.0415V54.0134H79.2601V45.4785Z" fill="white" />
    <path d="M62.4942 45.4785H47.2756V54.0134H62.4942V45.4785Z" fill="white" />
    <path d="M62.4942 75.7266H47.2756V84.7473H62.4942V75.7266Z" fill="white" />
    <path d="M62.4942 35.3965H47.2756V43.9319H62.4942V35.3965Z" fill="white" />
    <path d="M79.2601 75.7266H64.0415V84.7473H79.2601V75.7266Z" fill="white" />
    <path
      d="M36.6673 12.0505C37.8792 12.0505 38.8615 11.0681 38.8615 9.85631C38.8615 8.64448 37.8792 7.66211 36.6673 7.66211C35.4555 7.66211 34.4731 8.64448 34.4731 9.85631C34.4731 11.0681 35.4555 12.0505 36.6673 12.0505Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M46.2807 9.85631C46.2807 8.64418 45.2981 7.66211 44.0865 7.66211C42.8744 7.66211 41.8923 8.6447 41.8923 9.85631C41.8923 11.0679 42.8749 12.0505 44.0865 12.0505C45.2981 12.0505 46.2807 11.0679 46.2807 9.85631Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M36.7075 11.2879C36.071 11.2879 35.5552 10.7721 35.5552 10.1356V2.35737C35.5552 1.72087 36.071 1.20508 36.7075 1.20508C37.344 1.20508 37.8598 1.72087 37.8598 2.35737V10.1356C37.8598 10.7721 37.344 11.2879 36.7075 11.2879Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M44.1975 11.2879C43.561 11.2879 43.0452 10.7721 43.0452 10.1356V2.35737C43.0452 1.72087 43.561 1.20508 44.1975 1.20508C44.834 1.20508 45.3497 1.72087 45.3497 2.35737V10.1356C45.3497 10.7721 44.834 11.2879 44.1975 11.2879Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M137.844 9.85631C137.844 8.64418 136.862 7.66211 135.65 7.66211C134.438 7.66211 133.456 8.6447 133.456 9.85631C133.456 11.0679 134.438 12.0505 135.65 12.0505C136.862 12.0505 137.844 11.0679 137.844 9.85631Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M143.069 12.0505C144.281 12.0505 145.263 11.0681 145.263 9.85631C145.263 8.64448 144.281 7.66211 143.069 7.66211C141.857 7.66211 140.875 8.64448 140.875 9.85631C140.875 11.0681 141.857 12.0505 143.069 12.0505Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M135.69 11.2879C135.054 11.2879 134.538 10.7721 134.538 10.1356V2.35737C134.538 1.72087 135.054 1.20508 135.69 1.20508C136.327 1.20508 136.842 1.72087 136.842 2.35737V10.1356C136.842 10.7721 136.326 11.2879 135.69 11.2879Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M143.18 11.2879C142.544 11.2879 142.028 10.7721 142.028 10.1356V2.35737C142.028 1.72087 142.544 1.20508 143.18 1.20508C143.817 1.20508 144.332 1.72087 144.332 2.35737V10.1356C144.332 10.7721 143.816 11.2879 143.18 11.2879Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M86.1583 12.0505C87.3701 12.0505 88.3525 11.0681 88.3525 9.85631C88.3525 8.64448 87.3701 7.66211 86.1583 7.66211C84.9465 7.66211 83.9641 8.64448 83.9641 9.85631C83.9641 11.0681 84.9465 12.0505 86.1583 12.0505Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M93.5775 12.0505C94.7893 12.0505 95.7717 11.0681 95.7717 9.85631C95.7717 8.64448 94.7893 7.66211 93.5775 7.66211C92.3657 7.66211 91.3833 8.64448 91.3833 9.85631C91.3833 11.0681 92.3657 12.0505 93.5775 12.0505Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M86.1984 11.2879C85.5619 11.2879 85.0461 10.7721 85.0461 10.1356V2.35737C85.0461 1.72087 85.5619 1.20508 86.1984 1.20508C86.8349 1.20508 87.3507 1.72087 87.3507 2.35737V10.1356C87.3507 10.7721 86.8349 11.2879 86.1984 11.2879Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M93.6884 11.2879C93.0519 11.2879 92.5361 10.7721 92.5361 10.1356V2.35737C92.5361 1.72087 93.0519 1.20508 93.6884 1.20508C94.3249 1.20508 94.8407 1.72087 94.8407 2.35737V10.1356C94.8407 10.7721 94.3249 11.2879 93.6884 11.2879Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
    <path
      d="M40.0405 36.1406L32.3613 42.9048"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M34.7241 36.1406L40.0404 42.454"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M124.988 26.5137L117.308 33.2783"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M119.671 26.5137L124.987 32.827"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M74.688 26.5137L67.0088 33.2783"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M69.3716 26.5137L74.6879 32.827"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M142.543 26.3242L134.864 33.0889"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M137.226 26.3242L142.543 32.6376"
      stroke="#263238"
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M91.3905 48.2715C91.3905 48.2715 93.7585 51.2729 94.0035 51.9326C94.249 52.5928 94.4249 53.4037 94.4249 53.4037L95.9568 54.5456C96.1606 54.6973 96.1961 54.9882 96.0362 55.1847L94.869 56.6191C94.869 56.6191 95.9578 60.2209 95.9011 60.7429C95.8449 61.2649 92.0636 62.5451 92.0636 62.5451L91.7123 65.3051L84.5422 65.2159L84.5154 60.092L81.6497 53.3944C81.6507 53.3949 84.2333 48.5531 91.3905 48.2715Z"
      fill="#FF8B7B"
    />
    <path
      d="M83.1894 48.4395C83.1894 48.4395 80.4831 48.569 80.5001 52.3193C80.5171 56.0697 84.3118 59.542 84.5166 60.0918C84.7208 60.6417 86.7546 60.3869 88.1849 59.8551C89.6152 59.3233 90.1398 58.3773 90.1398 58.3773C90.1398 58.3773 88.7719 56.3817 89.0484 55.7782C89.3248 55.1748 90.7928 55.7555 90.7928 55.7555C90.7928 55.7555 91.4118 55.4002 91.6604 55.0572C91.9085 54.7147 93.2315 53.2209 92.3134 52.4354C91.3952 51.6498 89.968 51.1789 90.6025 50.1916C91.2369 49.2044 93.5064 48.2358 92.8395 46.1045C92.1725 43.9732 90.6886 44.0243 89.4507 44.735C88.2123 45.4458 87.8754 43.1923 85.4677 44.4627C83.06 45.7326 82.3817 47.6127 83.1894 48.4395Z"
      fill="#263238"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M112.172 48.582C113.372 49.1875 116.297 50.377 118.862 50.8345C118.862 50.8345 115.555 48.5944 112.358 48.0368C112.078 47.9878 111.919 48.4541 112.172 48.582Z"
      fill="#263238"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M111.56 65.5376L115.498 55.4502C115.498 55.4502 113.927 52.9063 113.953 52.1367C113.98 51.3672 116.417 48.6071 116.417 48.6071C116.417 48.6071 116.618 47.1026 117.689 47.8216C118.761 48.5406 118.63 49.7512 118.63 49.7512C118.63 49.7512 119.73 51.3305 119.976 51.932C120.222 52.5334 117.884 55.8598 117.884 55.8598C117.884 55.8598 117.506 65.0043 116.447 68.1677C115.389 71.3316 115.159 71.9753 114.475 72.9001C113.791 73.8244 110.528 72.1997 110.528 72.1997L111.56 65.5376Z"
      fill="#FF8B7B"
    />
    <path
      d="M93.5398 162.876L99.962 165.204C100.591 165.432 100.939 166.162 100.634 166.757C100.547 166.925 100.413 167.065 100.209 167.138C99.391 167.428 90.9397 170.335 88.4494 169.285C88.4494 169.285 87.5947 168.884 87.5999 167.929C87.6123 165.734 88.2255 162.277 88.2255 162.277C88.2255 162.277 91.3611 163.741 93.5398 162.876Z"
      fill="#263238"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M100.634 166.757C100.78 166.474 100.773 166.162 100.663 165.887C98.8851 166.749 95.1156 168.362 91.6789 168.428C89.5497 168.469 88.3247 168.29 87.616 168.082C87.6985 168.924 88.4495 169.286 88.4495 169.286C90.9398 170.336 99.3906 167.429 100.209 167.139C100.412 167.066 100.547 166.927 100.634 166.757Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M75.7686 160.926C75.7686 160.926 75.1889 166.243 75.502 166.767C75.815 167.292 76.7316 167.653 78.3213 167.547C79.911 167.441 80.3437 166.295 80.4882 165.913C80.6326 165.532 80.2622 162.052 80.2504 161.446C80.239 160.84 75.7686 160.926 75.7686 160.926Z"
      fill="#263238"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M80.5032 164.918C80.0137 165.861 79.3886 166.465 77.7705 166.496C76.6027 166.519 75.811 166.129 75.4092 165.868C75.4087 166.321 75.4345 166.655 75.502 166.768C75.8151 167.292 76.7317 167.653 78.3214 167.547C79.9111 167.441 80.3438 166.295 80.4882 165.913C80.5305 165.801 80.5279 165.416 80.5032 164.918Z"
      fill="white"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M96.974 99.7393C95.9739 100.323 94.1846 100.907 92.2447 101.425L78.0226 98.1367C78.0226 98.1367 77.0787 106.113 77.105 107.485C77.1313 108.857 77.6069 133.546 77.6069 133.546L75.6716 161.3C75.6716 161.3 76.2467 162.661 77.8926 162.629C79.5386 162.597 80.6253 162.027 80.6253 162.027L86.721 133.147L88.0693 163.805C88.0693 163.805 88.908 164.612 90.8283 164.575C92.7486 164.538 93.5398 162.876 93.5398 162.876C93.5398 162.876 94.4863 150.997 95.8919 142.523C97.2974 134.05 97.4733 110.659 97.4733 110.659L96.974 99.7393Z"
      fill="#5551CE"
    />
    <path
      d="M86.721 133.147L85.1607 112.544L81.5259 109.047"
      stroke="#263238"
      strokeWidth={0.5}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M79.1023 88.4315C79.2529 87.9487 79.0718 87.4267 78.6535 87.143C77.0721 86.0707 73.2454 83.8744 72.0245 86.7696C72.0245 86.7696 71.6382 87.7919 71.2792 90.2342C70.9388 92.5501 71.0641 108.233 71.078 109.849C71.0786 109.939 71.0693 110.024 71.0497 110.111C70.912 110.722 70.3528 113.402 70.7412 115.006C71.1822 116.824 72.0209 118.228 73.4492 118.607C74.8774 118.986 75.6573 117.347 75.435 116.336C75.2127 115.326 75.747 114.539 75.5242 113.529C75.3447 112.712 74.5679 110.583 74.2765 109.796C74.2033 109.599 74.1862 109.39 74.2265 109.183C74.6278 107.118 76.8792 95.552 77.053 94.9939C77.2052 94.5117 78.5416 90.228 79.1023 88.4315Z"
      fill="#FF8B7B"
    />
    <path
      d="M74.6819 69.8218L70 87.0117C70 87.0117 73.9175 90.1833 78.7866 90.09L81.9231 73.7929C81.9231 73.7929 78.4039 65.9301 74.6819 69.8218Z"
      fill="#263238"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M82.4396 64.1832C82.4396 64.1832 75.5465 67.6035 74.9007 69.1653C74.3684 70.4522 75.9627 79.5849 75.9937 81.1864C76.0246 82.788 77.3982 96.5747 77.7701 99.2378C78.1419 101.901 87.0492 103.039 90.4468 102.012C93.8448 100.985 96.9912 100.592 96.9747 99.7381C96.9582 98.8839 96.4981 91.6298 96.6848 90.2376C96.8715 88.8455 98.3075 78.4037 98.3075 78.4037L111.26 75.7211C111.26 75.7211 111.859 75.36 111.875 73.0152C111.898 69.6635 111.534 66.3996 111.534 66.3996C111.534 66.3996 109.537 66.9964 106.409 66.7194C103.281 66.4424 99.5769 65.6022 98.9192 65.7884C98.2616 65.9746 93.7778 65.0327 92.2789 64.8481C90.2209 64.5943 84.9773 62.8525 82.4396 64.1832Z"
      fill="#263238"
      stroke="#263238"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
export default HolidayIcon
