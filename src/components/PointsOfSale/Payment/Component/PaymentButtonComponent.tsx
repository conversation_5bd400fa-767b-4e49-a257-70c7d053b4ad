import Button from '@components/form/Button';
import { POSPaymentMethod } from '@interface/model/receipt';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import FortnoxIcon from '@partials/Icons/Fortnox';
import SwishIcon from '@partials/Icons/Swish';
import VivaWalletIcon from '@partials/Icons/VivaWallet';

type Props = {
    showTerminal?: boolean;
    total?: string;
    vivaLoading?: boolean;
    swishLoading?: boolean;
    fortnoxLoading?: boolean;
    terminalLoading?: boolean;
    onClickSwish?: () => Promise<void>;
    onClickViva?: () => Promise<void>;
    onClickFortnox?: () => Promise<void>;
    onClickTerminal?: () => Promise<void>;
    disabled?: boolean;
    showViva?: boolean;
    showSwish?: boolean;
    showFortnox?: boolean;
    processing?: boolean | POSPaymentMethod;
    payDirect?: boolean;
};

const PaymentButtonComponent = ({
    total,
    onClickSwish,
    onClickViva,
    onClickFortnox,
    onClickTerminal,
    showTerminal = false,
    terminalLoading = false,
    swishLoading = false,
    vivaLoading = false,
    fortnoxLoading = false,
    disabled = false,
    showViva = false,
    showSwish = false,
    showFortnox = false,
    processing = false,
    payDirect = false,
}: Props) => {
    if (payDirect) {
        return (
            <Button fullWidth disabled={disabled || !!processing} loading={vivaLoading} onClick={onClickViva}>
                {pos_strings.payment.pay(total ?? '0')}
            </Button>
        );
    }

    return (
        <div className="flex flex-wrap gap-4">
            <Button
                size="small"
                variant="filled"
                className="!m-0 !flex-1 !p-1"
                loadingClassName='left-4'
                fullWidth
                show={showViva}
                disabled={disabled || !!processing || terminalLoading}
                loading={vivaLoading}
                onClick={() => {
                    if (showTerminal && onClickTerminal) {
                        onClickTerminal();
                        return;
                    }

                    if (onClickViva) onClickViva();
                }}
            >
                {processing === 'viva' ? (
                    `${strings.processing}...`
                ) : (
                    <div className="flex items-center space-x-4">
                        <VivaWalletIcon className="h-8 w-8" />
                        <div className="flex flex-col items-start ">
                            <p>{total}</p>
                            <p>{pos_strings.viva.viva}</p>
                        </div>
                    </div>
                )}
            </Button>
            <Button
                size="small"
                variant="filled"
                className="!m-0 !flex-1 !p-1"
                loadingClassName='left-4'
                fullWidth
                show={showSwish}
                disabled={disabled || !!processing}
                loading={swishLoading}
                onClick={onClickSwish}
            >
                {processing === 'swish' ? (
                    `${strings.processing}...`
                ) : (
                    <div className="flex items-center space-x-4">
                        <SwishIcon className="h-4 w-4" />
                        <div className="flex flex-col items-start ">
                            <p>{total}</p>
                            <p>{pos_strings.swish.swish}</p>
                        </div>
                    </div>
                )}
            </Button>
            <Button
                size="small"
                variant="filled"
                className="!m-0 !flex-1 !p-1"
                loadingClassName='left-4'
                fullWidth
                show={showFortnox}
                disabled={disabled || !!processing}
                loading={fortnoxLoading}
                onClick={onClickFortnox}
            >
                {processing === 'fortnox_invoice' ? (
                    `${strings.processing}...`
                ) : (
                    <div className="flex items-center space-x-4">
                        <FortnoxIcon className="h-8 w-8" />
                        <div className="flex flex-col items-start ">
                            <p>{total}</p>
                            <p>{pos_strings.fortnox.fortnox_invoice}</p>
                        </div>
                    </div>
                )}
            </Button>
        </div>
    );
};

export default PaymentButtonComponent;
