import useTheme from '@hooks/useTheme';
import { CartesianGrid, Line, LineChart, ResponsiveContainer, <PERSON>ltip, XAxis, YAxis } from 'recharts';
import { chartColors } from './PieChartSingleColor';

export interface PieChartProps {
    data: LineMultiChartData[];
    labels?: [string, string];
    color?: 'primary' | 'violet';
}

export type LineMultiChartData = { name: string; value: number; value2: number };

const colors = {
    primary: chartColors.primary,
    violet: chartColors.violet,
};

const LineMultiChartComponent: React.FC<PieChartProps> = ({ data, labels = ['', ''], color = 'primary' }) => {
    const { isDark } = useTheme();

    return (
        <ResponsiveContainer width="100%" height={350}>
            <LineChart data={data}>
                <CartesianGrid strokeDasharray="6 4" vertical={false} opacity={isDark ? 0.5 : 1} />
                <XAxis tickLine={false} dataKey="name" />
                <YAxis width={undefined} tickLine={false} />
                <Tooltip
                    cursor={{ fill: isDark ? '#FFFFFF0D' : '#0000000D' }}
                    labelClassName="dark:text-black"
                    wrapperClassName="rounded-xl border-0"
                />
                <Line
                    dataKey="value2"
                    name={labels[1]}
                    stroke={chartColors.violet}
                    strokeWidth={2}
                    activeDot={{ r: 6, fill: 'white', strokeWidth: 2, stroke: chartColors.violet }}
                />
                <Line
                    dataKey="value"
                    name={labels[0]}
                    stroke={chartColors.primary}
                    strokeWidth={2}
                    activeDot={{ r: 6, fill: 'white', strokeWidth: 2, stroke: chartColors.primary }}
                    animationBegin={500}
                />
            </LineChart>
        </ResponsiveContainer>
    );
};

export default LineMultiChartComponent;
