import { DynamicQuestionaryValues } from '@components/Clients/Client/Questionaries/DynamicQuestionary/ClientDynamicQuestionariesModal';
import api from '@configs/api';
import { UserDocumentResponse } from '@interface/common';
import { DocumentVersion } from '@interface/model/document';
import strings from '@lang/Lang';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Modal from '@partials/MaterialModal/Modal';
import { Formik } from 'formik';
import useSWR from 'swr';
import { getInitialValuesToSet } from './DocumentReportsModal';
import DocumentVersionViewComponent from './version/DocumentVersionViewComponent';

export interface DocumentReportsModalProps {
    openModal: boolean;
    onClose: () => void;
    selectedDocumentId: number;
    version?: DocumentVersion;
}

const DocumentViewModal: React.FC<DocumentReportsModalProps> = ({ openModal, onClose: onClose, selectedDocumentId, version: defaultVersion }) => {
    const handleClose = () => {
        onClose();
    };

    const { data, isLoading } = useSWR<UserDocumentResponse, Error>(api.documents.view(selectedDocumentId) + `?view_company_document=1`, {
        revalidateIfStale: false,
        revalidateOnMount: true,
    });

    const version = defaultVersion ?? data?.data.version;

    return (
        <Modal
            open={openModal}
            title={<span className="line-clamp-2 break-all px-4">{data?.data.title}</span>}
            handleClose={handleClose}
            size="medium"
        >
            {isLoading ? (
                <SectionLoading />
            ) : (
                <div className="space-y-2 p-4">
                    <Formik<DynamicQuestionaryValues>
                        initialValues={version ? { data: getInitialValuesToSet(version.questions, version) } : { data: [] }}
                        enableReinitialize
                        onSubmit={() => {}}
                    >
                        <div className="">
                            {version && <DocumentVersionViewComponent version={version} />}
                            {!version?.questions.length ? strings.NoQuestions : ''}
                            {isLoading ? <SectionLoading /> : ''}
                        </div>
                    </Formik>
                </div>
            )}
        </Modal>
    );
};

export default DocumentViewModal;
