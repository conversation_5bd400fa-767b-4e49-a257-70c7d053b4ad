import ViewAllButton from '@components/form/ViewAllButton';
import useClient from '@hooks/useClient';
import strings from '@lang/Lang';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Table from '@partials/Table/PageTable';
import { useMainCardBooking } from '@provider/MainCard/MainCardBookingProvider';
import * as React from 'react';
import { useNavigate } from 'react-router';
import ClientEmptyRecordView from '../ClientEmptyRecordView';
import ClientBookingRecordListItem from './ClientBookingRecordListItem';

export interface ClientBookingRecordProps {}

const ClientBookingRecord: React.FC<ClientBookingRecordProps> = () => {
    const navigate = useNavigate();
    const { clientId } = useClient();
    const {
        setOpenBookingModal,
        setDeleteBookingOpen,
        setBookingHistoryOpen,
        setSelectedBooking,
        data,
        loading,
        orderBy,
        orderDirection,
        handleOrder,
    } = useMainCardBooking();

    if (loading) {
        return <SectionLoading />;
    }

    if (data?.data.length === 0) {
        return <ClientEmptyRecordView onClick={() => setOpenBookingModal(true)} />;
    }

    return (
        <>
            <Table>
                <Table.Head>
                    <Table.ThSort sort={orderBy === 'service_name' && orderDirection} onClick={() => handleOrder('service_name')}>
                        {strings.service}
                    </Table.ThSort>
                    <Table.ThSort sort={orderBy === 'practitioner_name' && orderDirection} onClick={() => handleOrder('practitioner_name')}>
                        {strings.practitioner}
                    </Table.ThSort>
                    <Table.ThSort sort={orderBy === 'booked_time' && orderDirection} onClick={() => handleOrder('booked_time')}>
                        {strings.booked_time_slot}
                    </Table.ThSort>
                    <Table.ThSort sort={orderBy === 'status' && orderDirection} onClick={() => handleOrder('status')}>
                        {strings.status}
                    </Table.ThSort>
                    <Table.Th children={strings.Actions} className="text-right" />
                </Table.Head>
                <Table.Body>
                    {data?.data.map((booking) => (
                        <ClientBookingRecordListItem
                            key={booking.id}
                            onProfileClick={() =>
                                navigate(
                                    `/clients/${
                                        booking.client ? booking.client_id : booking.clients?.length && booking.clients[0].id
                                    }/booking/${booking?.id}`,
                                )
                            }
                            onEditClick={() => {
                                setSelectedBooking(booking);
                                setOpenBookingModal(true);
                            }}
                            onDeleteClick={() => {
                                setSelectedBooking(booking);
                                setDeleteBookingOpen(true);
                            }}
                            onHistoryClick={() => {
                                setSelectedBooking(booking);
                                setBookingHistoryOpen(true);
                            }}
                            booking={booking}
                        />
                    ))}
                    <tr>
                        <td colSpan={5}>{data?.data.length !== 0 && <ViewAllButton to={`/clients/${clientId}/booking-records`} />}</td>
                    </tr>
                </Table.Body>
            </Table>
        </>
    );
};

export default ClientBookingRecord;
