import MaintenanceDocListItem from './Maintenance/MaintenanceListItem';
import Skeleton from '@components/Skeleton/Skeleton';
import Button from '@components/form/Button';
import Pagination from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import { MaintenanceDocument } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import AddRoundIcon from '@partials/Icons/AddRound';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import Card from '@partials/Paper/PagePaper';
import Table from '@partials/Table/PageTable';
import * as React from 'react';
import api from '../../../configs/api';
import { usePaginationSWR } from '../../../hooks/usePaginationSWR';
import { MaintenancePaginatedResponse } from '../../../interfaces/common';
import MaterialBreadcrumbs from '@partials/Breadcrumbs/MaterialBreadcrumbs';
import { useParams } from 'react-router';

export interface MaintenanceDeviceProps {}
const MaintenanceViewModal = React.lazy(() => import('./Maintenance/MaintenanceViewModal'));
const MaintenanceModal = React.lazy(() => import('./Maintenance/MaintenanceModal'));

const Maintenance: React.FC<MaintenanceDeviceProps> = () => {
    const { medicalDeviceId } = useParams();
    const [openModal, setOpenModal] = React.useState(false);
    const [viewOpen, setViewOpen] = React.useState(false);
    const { data, mutate, page, orderBy, setPage, loading, orderDirection, handleOrder } = usePaginationSWR<MaintenancePaginatedResponse, Error>(
        api.management_device.maintenance.list(medicalDeviceId),
    );

    const [selectedMaintenance, setSelectedMaintenance] = React.useState<MaintenanceDocument>();

    return (
        <div>
            <ModalSuspense>
                {openModal && <MaintenanceModal openModal={openModal} setOpenModal={setOpenModal} mutate={mutate} medicalId={medicalDeviceId} />}
                {viewOpen && selectedMaintenance && (
                    <MaintenanceViewModal openModal={viewOpen} setOpenModal={setViewOpen} maintenance={selectedMaintenance} />
                )}
            </ModalSuspense>
            <div className="space-y-2">
                <MaterialBreadcrumbs />
                <Card>
                    <div className="mb-4 flex items-center justify-between">
                        <Heading text={strings.maintenance_doc} />
                        <Button
                            onClick={() => {
                                setSelectedMaintenance(undefined);
                                setOpenModal(true);
                            }}
                        >
                            <div className="flex items-center">
                                <AddRoundIcon className="mr-1" />
                                {strings.add}
                            </div>
                        </Button>
                    </div>

                    <Table>
                        <Table.Head>
                            <Table.ThSort
                                sort={orderBy === 'title' && orderDirection}
                                onClick={() => handleOrder('title')}
                                children={strings.TITLE}
                            />
                            <Table.Th children={strings.added_on} />
                            <Table.Th children={strings.Actions} className="text-right" />
                        </Table.Head>
                        <Table.Body>
                            {data?.data?.map((medical) => (
                                <MaintenanceDocListItem
                                    key={medical.id}
                                    onViewClick={() => {
                                        setSelectedMaintenance(medical);
                                        setViewOpen(true);
                                    }}
                                    maintenanceDoc={medical}
                                />
                            ))}
                            {loading ? <MaintenanceDeviceSkeleton limit={10} /> : <></>}
                            {!loading && !data?.data.length && <EmptyData cardHeight="h-[65vh]" />}
                        </Table.Body>
                    </Table>
                    <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
                </Card>
            </div>
        </div>
    );
};

function MaintenanceDeviceSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={index}>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default Maintenance;
