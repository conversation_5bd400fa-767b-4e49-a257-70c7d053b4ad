import api from '@configs/api';
import useClient from '@hooks/useClient';
import useMatchMultipleRoutes from '@hooks/useMatchMultipleRoutes';
import { usePaginationSWR, usePaginationSWRResponse } from '@hooks/usePaginationSWR';
import { UserBookingResponse } from '@interface/common';
import { Booking } from '@interface/model/booking';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { FC, PropsWithChildren, createContext, lazy, useContext, useState } from 'react';

const BookingModal = lazy(() => import('@components/Calendar/Booking/BookingModal'));
const BookingCalendarDetailDeleteModal = lazy(() => import('@components/Calendar/Booking/BookingCalendarDeleteModal'));
const ClientBookingHistoryModal = lazy(() => import('@components/Clients/Client/Booking/ClientBookingHistoryModal'));

interface IContext extends usePaginationSWRResponse<UserBookingResponse, Error> {
    openBookingModal: boolean;
    setOpenBookingModal: React.Dispatch<React.SetStateAction<boolean>>;
    deleteBookingOpen: boolean;
    setDeleteBookingOpen: React.Dispatch<React.SetStateAction<boolean>>;
    bookingHistoryOpen: boolean;
    setBookingHistoryOpen: React.Dispatch<React.SetStateAction<boolean>>;
    selectedBooking?: Booking;
    setSelectedBooking: React.Dispatch<React.SetStateAction<Booking | undefined>>;
}

const MainCardBookingContext = createContext<IContext | null>(null);

const MainCardBookingProvider: FC<PropsWithChildren> = ({ children }) => {
    const { clientId, mutateCounts ,client} = useClient();

    const isClientPage = useMatchMultipleRoutes([
        'clients/:clientId',
        'settings/team/:userId/booking/:bookingId/client/:clientId',
        'calendar/booking/:bookingId/client/:clientId',
    ]);

    const [openBookingModal, setOpenBookingModal] = useState(false);
    const [deleteBookingOpen, setDeleteBookingOpen] = useState(false);
    const [bookingHistoryOpen, setBookingHistoryOpen] = useState(false);
    const [selectedBooking, setSelectedBooking] = useState<Booking>();

    const swr = usePaginationSWR<UserBookingResponse, Error>(api.clientBookingListItem(clientId), { limit: isClientPage ? 3 : 20 });

    const bookingMutate = async () => {
        mutateCounts();
        swr.mutate();
    };

    return (
        <MainCardBookingContext.Provider
            value={{
                openBookingModal,
                setOpenBookingModal,
                deleteBookingOpen,
                setDeleteBookingOpen,
                bookingHistoryOpen,
                setBookingHistoryOpen,
                selectedBooking,
                setSelectedBooking,
                ...swr,
            }}
        >
            <ModalSuspense>
                {openBookingModal && (
                    <BookingModal
                        selectedBooking={selectedBooking}
                        openModal={openBookingModal}
                        setOpenModal={(v) => {
                            setSelectedBooking(undefined);
                            setOpenBookingModal(v);
                        }}
                        mutate={bookingMutate}
                        clientId={clientId}
                        client={client}
                    />
                )}
                {deleteBookingOpen && (
                    <BookingCalendarDetailDeleteModal
                        open={deleteBookingOpen}
                        handleClose={() => {
                            setSelectedBooking(undefined);
                            setDeleteBookingOpen(false);
                        }}
                        mutate={bookingMutate}
                        selectedBooking={selectedBooking}
                        selectedClients={selectedBooking?.clients}
                    />
                )}
                {bookingHistoryOpen && (
                    <ClientBookingHistoryModal
                        openModal={bookingHistoryOpen}
                        setOpenModal={setBookingHistoryOpen}
                        selectedBooking={selectedBooking}
                    />
                )}
            </ModalSuspense>
            {children}
        </MainCardBookingContext.Provider>
    );
};

export const useMainCardBooking = () => {
    const context = useContext(MainCardBookingContext);

    if (!context) {
        throw new Error('useMainCardBooking must be used within a MainCardBookingProvider');
    }
    return context;
};

export default MainCardBookingProvider;
