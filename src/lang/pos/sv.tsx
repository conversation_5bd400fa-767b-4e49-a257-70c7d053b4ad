const data = {
    add_service_product: '<PERSON>ägg till tjänst/produkt',
    point_of_sale: 'Kassasystem',
    posTermsText: 'Genom att klicka här, godkänner du våra {0}.',
    tax: 'Moms',
    net: 'Netto',
    gross: '<PERSON><PERSON><PERSON>',
    export: 'Exportera',
    SAVE: 'SPARA',
    total: 'Totalt',
    apply_discount: 'Tillämpa rabatt',
    discount: 'Rabatt',
    value: 'Värde',
    select_service_product: 'Välj tjänst/produkt',
    items: 'Artikel/artiklar',
    added: 'Lagt till',
    in_stock: (v: string | number) => `${v} i lager`,
    abort: `Avbryt`,
    action: 'Åtgärd',
    no_item_found: 'Ingen artikel hittades',
    reference_note: 'Referensanteckning',
    reference_placeholder: 'Alla specifika anpassningsförfrågningar eller speciella detaljer om din beställning är välkomna i referensanteckningen.',
    download_receipt: 'Ladda ner kvitto',
    adjusted_total: 'Justerat totalt',
    other: 'Övrigt',
    batch: 'Batch',
    paid_at: 'Betald vid',
    refund_at: 'Återbetalad vid',
    qty: 'Antal',
    print: 'Skriv ut',
    items_added: 'Tillagda artikel/artiklar',

    hold_on: 'Vänta',
    registered_company_mismatch: 'Fel på registrerat företag',
    notice_account_registered_with: (country: string) => `Vi har observerat att du har registrerat ditt konto med - ${country}`,
    for_now_pos_works_with: (country: string) => `För tillfället fungerar kassasystemet endast för ${country}.`,
    verify_details: 'Verifiera detaljer',

    viva: {
        viva: 'Viva',
        viva_wallet: 'Viva Wallet',
        connect: 'Anslut',
        connected: 'Ansluten',
        connect_to_vivawallet: 'Anslut till Viva Wallet',
        merchant_id: 'Merchant ID',
        unique_assigned_to_you: 'Detta är den unika identifieraren som tilldelats dig av Viva Wallet.',
        connected_to_vivawallet: 'Du är ansluten till Viva Wallet',
        for_payment_solutions: 'För betallösning',
        join_waitlist: 'Gå med i väntelistan',
        joined_waitlist: 'Gick med i väntelistan.',
        join_the_waitlist: 'Gå med i väntelistan!',
        already_joined_the_waitlist: 'Redan anmält sig till väntelistan',
        successful_connected: 'Anslutning lyckades!',
        verification_is_currently_in_progress: 'Verifiering pågår',
        checkout_company_logo: 'Ladda upp företagslogotyp',
        checkout_company_logo_desc: 'Lägg till din företagslogotyp till kassasidan för onlinebetalningar',
    },

    fortnox: {
        fortnox: 'Fortnox',
        fortnox_invoice: 'Fortnox Faktura',
        connect: 'Anslut',
        connected: 'Ansluten',
        connect_to_fortnox: 'Anslut till Fortnox',
        connected_to_fortnox: 'Du är ansluten till Fortnox',
        for_invoice_solutions: 'För fakturalösning',
        successful_connected: 'Anslutning lyckades!',
        verification_is_currently_in_progress: 'Verifiering pågår',
        fortnox_connect_message: 'Vänligen anslut till Fortnox för att använda fakturabetalningar',
        checkout: {
            add_your_company_logo: 'Lägg till din företagslogotyp på betalningssidan',
            step_1: (url: string) =>
                `<strong>Steg 1:</strong> Logga in på <a href="${url}"  target="_blank" class='dark:text-primaryLight text-primary'>Viva</a> och välj rätt konto.`,
            step_2: `<strong>Steg 2:</strong> Navigera till <strong>Försäljning</strong> i menyn till vänster:`,
            step_3: `<strong>Steg 3:</strong> Gå till <strong>Onlinebetalningar > Webbplatser/Appar</strong>, som visas nedan:`,
            step_4: (paymentSourceId: number | string) =>
                `<strong>Steg 4:</strong> Välj beskrivningen <strong>MERIDIQ</strong> (ID: ${paymentSourceId}).`,
            step_5: `<strong>Steg 5:</strong> Ladda upp företagslogotypen i den angivna sektionen:`,
            step_6: `<strong>Steg 6:</strong> Markera alla kryssrutor och klicka på <strong>Uppdatera</strong>.`,
            finish: `Nu kommer din företagslogotyp att visas på betalningssidan!`,
        },

        register_for_viva: 'Registrera dig för Viva Wallet',
        what_is_next: 'Vad händer sen...',
        verify_detail_message_1: 'Din information kommer att skickas till Viva Wallet.',
        verify_detail_message_2: 'Förvänta dig en uppföljning från Viva Wallet för ytterligare dokumentverifiering.',
        verify_detail_message_3: 'Vi uppskattar ditt samarbete för att säkerställa en smidig process.',
        verify_detail_message_4: 'Om du har några frågor, kontakta oss.',
    
    },

    swish: {
        swish: 'Swish',
        swish_qr: 'Swish QR',
        info_upload_swish_qr: 'Ladda upp din swish qr som kommer att visas när du betalar med swish.',
    },

    infrasec: {
        infrasec: 'Infrasec',
        for_tax_compliance: 'För skatteefterlevnad',
        connect: 'Anslut',
        connected: 'Ansluten',

        no_changes_allowed_organization_number: 'Organisationsnummer blir fastställt vid inlämning; inga ändringar tillåtna.',
        validation: {
            organization_number_is_required: 'Organisationsnummer är obligatoriskt',
            organization_number_invalid_format: 'Organisationsnummer måste vara i formatet 000000-0000',
            agree_to_terms_and_conditions: 'vänligen godkänn villkor och bestämmelser',
        },

        register_for_infrasec: 'Registrera dig för Infrasec',
        what_is_next: 'Vad händer sen...',
        verify_detail_message_1: 'Din registrering hos Infrasec är klar.',
        verify_detail_message_2: 'Du kan nu börja använda våra tjänster direkt.',
        verify_detail_message_3: 'Vi är glada att ha dig med oss!',
        verify_detail_message_4: 'Om du har några frågor, är du välkommen att kontakta oss.',
    },

    ecr: {
        ecr: 'ECR',
        ecr_message: 'Ett unikt identifikationsnummer tilldelat en specifik elektronisk kassaregister eller kassaterminal',
        ecr_id: 'ECR ID',
    },

    payment: {
        confirm_payment_manually: 'Bekräfta betalning manuellt',
        payment_collected: 'Betalning Inkasserad',

        payment: 'Betalning',

        payment_summary: 'Sammanfattning av betalning',
        subtotal: 'Delsumma',
        tax: 'Skatt',
        pay: (v: string) => `Betala ${v}`,

        amountHadBeenPaid: (v: string | number) => `Din betalning på ${v} har mottagits och bearbetats framgångsrikt.`,

        validation: {
            product_or_service_is_required: 'Produkt eller tjänst krävs',
            discount_value_is_required: 'Rabattvärde krävs',
            discount_value_must_be_numeric: 'Rabattvärdet måste vara numeriskt',
            discount_value_must_be_between_1_to_100: 'Rabattvärdet måste vara mellan 1 och 100',
            discount_value_must_be_less_than: (value: string) => `Rabattvärdet måste vara mindre än ${value}`,
            add_service_or_products_before_activating_gift_card: 'Vänligen lägg till tjänst eller produkt innan du aktiverar presentkortet',
            add_service_or_products_before_activating_discount: 'Vänligen lägg till tjänst eller produkt innan du aktiverar rabatten',

            gift_card_id_is_required: 'Presentkorts-ID krävs',
            gift_card_value_is_required: 'Presentkortets värde krävs',
            gift_card_value_must_be_integer: 'Måste vara ett heltal',
            gift_card_value_cannot_be_greater_than: (value: string) => `Presentkortets värde kan inte vara större än ${value}`,
            gift_card_id_greater_than_4: 'Presentkorts-ID måste vara längre än 4 bokstäver',

            please_select_a_terminal_device: 'Vänligen välj en terminalenhet',
        },
        items: 'Artiklar',
        qty: 'Antal.',
    },

    terminal: {
        select_terminal_device: 'Välj terminalenhet',
        no_terminal_devices_found: 'Inga terminalenheter hittades',
        remember_my_choice: 'Kom ihåg mitt val',

        pos_terminal_devices: 'Betalenheter',
        nick_name: 'Smeknamn',
        virtual_terminal_id: 'Virtuellt terminal-ID',
        virtual_terminal_id_error: 'Kan inte ändra virtuellt terminal-ID',
        update_terminal: 'Uppdatera betalterminal',
        terminal_device: 'Terminalenhet',
    },

    product: {
        products: 'Produkter',
        products_library: 'Produktbibliotek',

        product_category: 'Produktkategori',
        filter_by_category: 'Filtrera efter kategori',

        add_product: 'Lägg till produkt',
        update_product: 'Uppdatera produkt',

        ean_upc: 'EAN/UPC',
        product_name: 'Produktnamn',
        product_code: 'Produktkod',
        selling_price: 'Försäljningspris',
        base_price: 'Grundpris',
        tax_information: 'Skatteinformation',
        stock: 'Lager',
        inventory: 'Inventering',

        out_of_stock: 'Slut på lager',

        validation: {
            product_name_is_required: 'Produktnamn krävs',
            selling_price_is_required: 'Försäljningspris krävs',
            base_price_is_required: 'Grundpris krävs',
            tax_infomation_is_required: 'Skatteuppgifter krävs',
            inventory_stock_is_required: 'Inventering/lager krävs',

            selling_price_less_than_base_price: 'Försäljningspriset kan inte vara lägre än grundpriset',
            selling_price_greater: 'Försäljningspriset måste vara större än 0',
            base_price_greater: 'Grundpriset måste vara större än 0',
        },
    },

    receipt: {
        receipt: 'Kvitto',
        receipt_detail: 'Kvittodetaljer',
        payment_by: 'Betalning med',

        paid_by_gift_card: 'Betald med presentkort',
        paid_by_bank: 'Betald med kredit-/bankkort',
        paid_by_viva: 'Betald med Viva Wallet',
        paid_by_swish: 'Betald med Swish',

        print_receipt: 'Skriv ut kvitto',
        print_receipt_copy: 'Skriv ut',
        email_receipt_copy: 'E-post',

        status: {
            paid: 'Betald',
            partially_refunded: 'Delvis återbetald',
            aborted: 'Avbruten',
            pending: 'Avvaktar',
            processing: 'Bearbetas',
            refunded: 'Återbetald',
            cancelled: 'Avbruten',
        },
        transaction_type: 'Transaktionstyp',
    },

    refund: {
        refund: 'Återbetalning',
        refunds: 'Återbetalningar',
        refund_transaction: 'Återbetalningstransaktion',
        amount_to_be_refunded: 'Belopp att återbetalas',
        refund_amount_note: 'För en delvis återbetalning, ange ett belopp mindre än det totala',
        refund_in_gift_card: 'Återbetalning med presentkort',
        refund_in_bank: 'Återbetalning i bank (kredit/bankkort)',
        refund_in_swish: 'Återbetalning i swish',

        enter_refund_amount: 'Ange återbetalningsbelopp',
        refund_summary: 'Återbetalningssammanfattning',

        select_payment_mode: 'Välj betalningsläge',

        refund_completed: 'Återbetalning slutförd',
        refunded: 'Återbetalt',

        applied_discount: 'Tillämpad rabatt',

        amount_refunded: 'Belopp återbetalat',

        refundValue: (v: string) => `Återbetalning ${v}`,
        amountHadBeenRefunded: (v: string | number) => `En återbetalning på ${v} har behandlats för dig.`,

        refund_error: 'Återbetalningen kan inte ångras, en gång inskickad. Är du säker på att du vill göra det?',

        validation: {
            refund_amount_required: 'Återbetalningsbelopp krävs',
            refund_amount_must_be_integer: 'Återbetalningsbelopp måste vara ett heltal',
            refund_amount_cannot_be_greater_than: (v: string | number) => `Återbetalningsbeloppet får inte vara större än ${v}`,
            total_refund_amount_cannot_be_greater_than: (v: string | number) => `Total återbetalningsbelopp kan inte vara större än ${v}`,
            expiry_date_must_be_after: (v: string | number) => `Utgångsdatumet måste vara efter ${v}`,
        },

        status: {
            aborted: 'Avbruten',
            pending: 'Avvaktar',
            processing: 'Bearbetas',
            refunded: 'Återbetald',
            cancelled: 'Avbruten',
        },

        refund_confirmation_text: [
            'För att registrera återbetalningen klicka på "Återbetalning".',
            'För att slutföra själva överföringen till patienten behöver du logga in på rätt konto beroende på vilken betalningsmetod som använts:',
            '<b>Swish</b>: Logga in i din <b>internetbank</b>.',
            '<b>Klarna</b>: Logga in på ditt <b>Viva-konto</b>.',
            'Följ sedan dessa steg:',
            'Hitta den aktuella betalningen.',
            'Genomför återbetalningen manuellt.',
            'Hör gärna av dig om du behöver hjälp i något steg till <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><span class="text-primary dark:text-primaryLight"><EMAIL></span></a>!',
        ],
    },

    gift_card: {
        gift_card_id: 'Presentkorts-ID',
        card_id: 'Kort-ID',
        Avl_bal: 'Tillg. Bal.',

        available_balance: 'Tillgängligt saldo',
        use: 'Använd',

        gift_card: 'Presentkort',

        gift_cards: 'Presentkort',
        issue_gift_card: 'Utfärda presentkort',
        initial_value: 'Inledande värde',

        auto_generated: 'Auto-genererat',

        gift_card_number: 'Presentkortsnummer',

        card_number: 'Kortnummer',
        status: 'Status',
        validity: 'Giltighet',
        customer: 'Kund',
        remaining: 'Återstående / Värde',
        expiry_date: 'Utgångsdatum',

        validation: {
            inital_value_is_required: 'Inledande värde krävs',
            inital_value_must_be_greater_than_0: 'Inledande värde måste vara större än 0',
            expiry_date_is_required: 'Utgångsdatum krävs',
        },
    },

    report: {
        report: 'Rapport',
        zreport: 'Z-rapport',
        xreport: 'X-rapport',
        close_batch: 'Dagsavslut',
        closing_amount: 'Stängningsbelopp',
    },

    config: {
        batch: 'Omgång',

        z_report_email_to: 'Z-rapport till denna e-post',
        auto_z_report_to: 'Skicka automatiskt Z-rapport till denna e-post',
        auto_z_report_time: 'Dagsavslut utlöser en automatisk generering av en Z-rapport.',
    },

    please_enable_point_of_sale_first: 'Vänligen aktivera försäljningsstället först',
    enable_point_of_sale: 'Aktivera kassasystemet',
    enable_online_payment_checkbox: 'Aktivera onlinebetalning under Kassasystemets konfigurering',
};

export default data;
