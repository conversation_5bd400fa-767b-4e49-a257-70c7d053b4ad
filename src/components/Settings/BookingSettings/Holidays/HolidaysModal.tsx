import randomColor from '@/helper';
import Button from '@components/form/Button';
import Input from '@components/form/Input';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import SearchIcon from '@icons/Search';
import { CompanyHolidaysResponse } from '@interface/common';
import { Holiday } from '@interface/model/Holiday';
import strings from '@lang/Lang';
import CancelButton from '@partials/MaterialButton/CancelButton';
import MaterialCheckbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import Modal from '@partials/MaterialModal/Modal';
import cx from 'classix';
import React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import { HolidayItem } from '../tabs/Holidays';
import NoDataText from '@partials/Error/NoDataText';
interface CompanyHolidayProps {
    openModal?: boolean;
    handleClose: () => void;
    mutate: () => Promise<any>;
    holidayItems: HolidayItem[];
}

const HolidaysModal: React.FC<CompanyHolidayProps> = ({ openModal, handleClose, mutate, holidayItems }) => {
    const [search, setSearch] = React.useState<string>('');
    const [loading, setLoading] = React.useState<boolean>(false);
    const [countryName, setCountryName] = React.useState<string[]>(Array.isArray(holidayItems) ? holidayItems?.map((item) => item.name) : []);
    const navigate = useNavigate();

    const { data, isLoading } = useSWR<CompanyHolidaysResponse, Error>(api.calendarHolidays);
    const holidaysList = data?.data;
    const religiousHolidays = holidaysList?.filter((holiday) => holiday.is_religion);
    const countryHolidays = React.useMemo(() => {
        const isCountry = holidaysList?.filter((holiday) => holiday.is_country);
        const selectedCountryHoliday = [
            ...holidayItems.filter((item) => isCountry?.map((country) => country.name).includes(item.name)),
            ...(isCountry?.length ? isCountry?.filter((coun) => !holidayItems?.map((item) => item.name).includes(coun?.name)) : []),
        ];
        if (search === '') return selectedCountryHoliday;
        return holidaysList
            ?.filter((holiday) => holiday.is_country)
            .filter((country) => country.name.toLowerCase().replace(/\s+/g, '').includes(search.toLowerCase().replace(/\s+/g, '')));
    }, [search, holidaysList]);

    function addOrRemoveId(name: string) {
        const arr = [...countryName];
        if (arr.includes(name)) {
            arr.splice(arr.indexOf(name), 1); // remove array of value
            return arr;
        }
        arr.push(name); // add array of value
        return arr;
    }
    async function handleSubmit() {
        setLoading(true);
        const holidayValue = countryName.map((country) => ({
            calendarId: holidaysList?.find((couname: Holiday) => couname.name === country)?.calendarId,
            color: holidayItems?.find((item) => item.name === country)?.color || randomColor(),
            name: country,
        }));
        const response = await fetch(api.settingStoreV2, {
            method: 'POST',
            headers: {
                Accept: 'application/json',
                
                
                'X-App-Locale': strings.getLanguage(),
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                settings: [{ key: 'HOLIDAY_CALENDARS', value: JSON.stringify(holidayValue) }],
            }),
        });

        const data = await response.json();
        if (response.status === 401) {
            navigate('/');
        }
        if (data.status === '1') {
            toast.success(data.message);
            mutate();
        } else {
            toast.error(data.message || 'please try again');
        }
        handleClose();
        setLoading(false);
    }

    return (
        <Modal
            open={openModal}
            handleClose={handleClose}
            submitButton={
                <Button onClick={() => handleSubmit()} loading={loading}>
                    {strings.Submit}
                </Button>
            }
            cancelButton={<CancelButton onClick={handleClose} />}
        >
            <div className="p-5">
                {isLoading ? (
                    <div className="flex justify-center">
                        <LoadingIcon className="h-[20vh] text-xl" />
                    </div>
                ) : (
                    <div className="space-y-3">
                        <Input
                            placeholder={strings.Search}
                            value={search || ''}
                            icon={<SearchIcon className="text-mediumGray" />}
                            onChange={(event) => setSearch(event.target.value)}
                        />
                        <p className="font-semibold text-mediumGray">{strings.global_religious_holiday}</p>
                        {religiousHolidays?.map((item, index) => {
                            return (
                                <MaterialCheckbox
                                    label={cx(item?.name, strings.holidays)}
                                    key={index}
                                    onChange={() => {
                                        setCountryName(addOrRemoveId(item.name));
                                    }}
                                    checked={countryName.includes(item.name)}
                                />
                            );
                        })}
                        <p className="font-semibold text-mediumGray">{strings.country_holidays}</p>
                        {countryHolidays?.length ? (
                            countryHolidays?.map((item, index) => {
                                return (
                                    <MaterialCheckbox
                                        label={cx(item?.name, strings.holidays)}
                                        key={index}
                                        onChange={() => {
                                            setCountryName(addOrRemoveId(item.name));
                                        }}
                                        checked={countryName.includes(item.name)}
                                    />
                                );
                            })
                        ) : (
                            <NoDataText />
                        )}
                    </div>
                )}
            </div>
        </Modal>
    );
};
export default HolidaysModal;
