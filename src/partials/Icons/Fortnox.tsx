import * as React from 'react';

interface FortnoxIconProps {
    variant?: 'dark' | 'light';
    size?: number;
}
// interface FortnoxIconProps extends React.SVGProps<SVGSVGElement>{
//     variant?: 'dark' | 'light',
//     size?: number,
// }

function FortnoxIcon({ variant, size, ...props }: FortnoxIconProps) {
    return <img src="/fortnox.svg" alt="Fortnox" width={size ?? 52} height={size ?? 52} {...props} />;

    if (variant === 'dark') {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width={size ?? 52} height={size ?? 52} fill="none" viewBox="0 0 52 52" {...props}>
                <circle cx={26} cy={26} r={25.5} fill="#191E32" stroke="#E9EAFF" />
                <path fill="#fff" d="M15 16h22v4H15v-4zm0 8h16v4H15v-4zm0 8h20v4H15v-4zm22-12v16h4V20h-4z" />
                <text x="26" y="42" textAnchor="middle" fill="#fff" fontSize="8" fontFamily="Arial, sans-serif">
                    FORTNOX
                </text>
            </svg>
        );
    }

    return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size ?? 52} height={size ?? 52} fill="none" viewBox="0 0 52 52" {...props}>
            <circle cx={26} cy={26} r={25.5} fill="#fff" stroke="#E9EAFF" />
            <path fill="#1F263A" d="M15 16h22v4H15v-4zm0 8h16v4H15v-4zm0 8h20v4H15v-4zm22-12v16h4V20h-4z" />
            <text x="26" y="42" textAnchor="middle" fill="#1F263A" fontSize="8" fontFamily="Arial, sans-serif">
                FORTNOX
            </text>
        </svg>
    );
}

export default FortnoxIcon;
