import AddButton from '@components/form/AddButton';
import Button from '@components/form/Button';
import IconButton from '@components/form/IconButton';
import Select from '@components/form/Select';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import CalendarIcon from '@icons/Calendar';
import FilterIcon from '@icons/Filter';
import ListIcon from '@icons/List';
import SMSOutlineIcon from '@icons/SMSOutline';
import TodayCalendarIcon from '@icons/TodayCalendar';
import strings from '@lang/Lang';
import ChevronIcon from '@partials/Icons/Chevron';
import LeftArrowCircleIcon from '@partials/Icons/LeftArrowCircle';
import RightArrowCircleIcon from '@partials/Icons/RightArrowCircle';
import dayjs from 'dayjs';
import { Fragment, useEffect, useMemo } from 'react';
import CalendarSlotIntervalSelect from '../Custom/CalendarSlotIntervalSelect';
import { useClassicCalendar } from './ClassicCalendarProvider';
import ScheduleAutocompleteMultiple from '../Schedule/SelectTemplate/ScheduleAutocompleteMultiple';
import useAuth from '@hooks/useAuth';
import { CompanyUserResponse } from '@interface/common';
import useSWR from 'swr';
import { Dialog, DialogBackdrop, DialogPanel, Transition, TransitionChild } from '@headlessui/react';
import MoreIcon from '@partials/Icons/More';

export interface ClassicCalendarHeaderProps {}

const ClassicCalendarHeader: React.FC<ClassicCalendarHeaderProps> = () => {
    const {
        view,
        isListSelected,
        navigation: { toPrev, toNext, setToday },
        cursorDate,
        headers,
        toggleListSelected,
        setCategorySelectDropDownOpen,
        intervalSlot,
        setIntervalSlot,
        setAddBookingOpen,
        setBulkSMSSelectedDate,
        setBulkSMSModalOpen,
        setOpenHoursModalOpen,
        setSelectedUsers,
        selectedUsers,
        extraBarOpen,
        setExtraBarOpen,
    } = useClassicCalendar();
    const {
        user,
        userType: { isUser },
    } = useAuth();
    const { data: usersData, isLoading } = useSWR<CompanyUserResponse>(api.bookingPractitionersAll({ serviceIds: [] }));

    const date = dayjs(cursorDate);
    const text = useMemo(() => {
        if (view.isMonthView || view.isDayView) {
            return date.format(view.isMonthView ? 'MMMM YYYY' : 'DD MMMM YYYY');
        }

        const weekStartDay = dayjs(headers.weekDays[0].value);
        const weekEndDay = dayjs(headers.weekDays.at(-1)?.value);
        const isSameMonth = weekStartDay.month() === weekEndDay.month();
        return `${weekStartDay.format(isSameMonth ? 'DD' : 'DD MMM')} - ${weekEndDay.format('DD MMM YYYY')}`;

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [view.type, date.format(api.dateFormat)]);
    const handleChange = (value: string) => {
        if (value === 'isDayView') {
            view.showDayView();
        }
        if (value === 'isWeekView') {
            view.showWeekView();
        }
        if (value === 'isMonthView') {
            view.showMonthView();
        }
    };

    return (
        <div className="grid gap-2">
            <div className="grid gap-2 md:hidden">{mobileView()}</div>
            <div className="hidden md:block">{desktopView()}</div>
        </div>
    );
    function desktopView() {
        return (
            <>
                <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Heading text={strings.booking_calendar} />
                        {!isUser && (
                            <ScheduleAutocompleteMultiple
                                placeholder={strings.select_users}
                                require={true}
                                value={selectedUsers ?? []}
                                options={usersData?.data || []}
                                customText={strings.select_users}
                                userLength={2}
                                loading={isLoading}
                                image={true}
                                onChange={setSelectedUsers}
                            />
                        )}
                    </div>
                    <div className="flex items-center space-x-4">
                        <Button
                            size="small"
                            variant="outlined"
                            onClick={() => {
                                setOpenHoursModalOpen(true);
                            }}
                        >
                            {strings.open_hours}
                        </Button>
                        <AddButton
                            onClick={() => {
                                setAddBookingOpen(true);
                            }}
                            size="small"
                            disabled={user?.user_role === 'user' ? (user?.company?.is_booking_on ? false : true) : false}
                        />
                    </div>
                </div>
                <div className="flex flex-col-reverse justify-between px-2 xl:flex-row">
                    <div className="mb-1 flex items-center gap-2 md:mb-0 md:mt-2 xl:mt-0">
                        <Button className="h-full !px-2 text-xs md:!px-3 md:text-sm" onClick={setToday}>
                            {strings.today}
                        </Button>
                        <div className="flex items-center gap-1">
                            <IconButton onClick={toPrev} className="!text-xl">
                                <ChevronIcon side="left" />
                            </IconButton>
                            <IconButton onClick={toNext}>
                                <ChevronIcon side="right" />
                            </IconButton>
                        </div>
                        <Heading text={view.isDayView ? date.format('DD MMM (dddd)') : text} className="!text-xs md:mr-4 md:!text-xl" />
                    </div>
                    <div className="flex items-center gap-4">
                        <IconButton onClick={toggleListSelected} name={isListSelected ? strings.calendar : strings.list}>
                            {isListSelected ? <CalendarIcon className="text-2xl" /> : <ListIcon className="text-lg" />}
                        </IconButton>
                        <IconButton onClick={() => setCategorySelectDropDownOpen(true)} name={strings.filter}>
                            <FilterIcon className="text-xl" />
                        </IconButton>
                        {!view.isMonthView && !isListSelected && (
                            <div className="min-w-[10rem]">
                                <CalendarSlotIntervalSelect
                                    selected={intervalSlot.id}
                                    onChange={(id) => {
                                        setIntervalSlot(id);
                                    }}
                                />
                            </div>
                        )}
                        <div className="flex w-full items-center justify-evenly space-x-5 rounded-md bg-purpleGray px-6 dark:bg-primaryLight/10">
                            <ClassicCalendarHeaderButton
                                text={strings.Daily}
                                textMobile={strings.DailyShort}
                                onClick={() => view.showDayView()}
                                active={view.isDayView}
                            />
                            <ClassicCalendarHeaderButton
                                text={strings.Weekly}
                                textMobile={strings.WeeklyShort}
                                onClick={() => view.showWeekView()}
                                active={view.isWeekView}
                            />
                            <ClassicCalendarHeaderButton
                                text={strings.Monthly}
                                textMobile={strings.MonthlyShort}
                                onClick={() => view.showMonthView()}
                                active={view.isMonthView}
                            />
                        </div>
                    </div>
                </div>
            </>
        );
    }
    function mobileView() {
        return (
            <>
                <MobileExtraPanel />

                <div className="flex justify-between gap-4">
                    <div className="flex-grow">
                        {!isUser && (
                            <ScheduleAutocompleteMultiple
                                placeholder={strings.select_users}
                                require={true}
                                value={selectedUsers ?? []}
                                options={usersData?.data || []}
                                customText={strings.select_users}
                                userLength={2}
                                loading={isLoading}
                                image={true}
                                onChange={setSelectedUsers}
                            />
                        )}
                    </div>
                    <AddButton
                        onClick={() => {
                            setAddBookingOpen(true);
                        }}
                    />
                </div>
                <div className="flex items-center justify-between">
                    <div className="min-w-[10rem]">
                        <Select
                            className="!py-1 text-base md:!py-2"
                            value={view.type.charAt(0).toUpperCase() + view.type.slice(1)}
                            displayValue={(val) => val}
                            onChange={(val) => {
                                handleChange(val);
                            }}
                        >
                            <Select.Option value={'isDayView'}>{strings.Daily}</Select.Option>
                            <Select.Option value={'isWeekView'}>{strings.Weekly}</Select.Option>
                            <Select.Option value={'isMonthView'}>{strings.Monthly}</Select.Option>
                        </Select>
                    </div>

                    <Button size="small" variant={'outlined'} onClick={() => setCategorySelectDropDownOpen(true)}>
                        <FilterIcon className="text-xl text-primary dark:text-primaryLight" />
                    </Button>
                    <Button size="small" onClick={toggleListSelected} variant={'outlined'}>
                        {isListSelected ? (
                            <CalendarIcon className="text-xl text-primary dark:text-primaryLight" />
                        ) : (
                            <ListIcon className="text-lg text-primary dark:text-primaryLight" />
                        )}
                    </Button>
                    <IconButton onClick={() => setExtraBarOpen(true)} className="md:hidden">
                        <MoreIcon className="rotate-90" />
                    </IconButton>
                </div>
                <div className="mb-1 flex items-center justify-between">
                    <Heading text={view.isDayView ? date.format('DD MMM (dddd)') : text} className="!text-base" />
                    <IconButton onClick={setToday} children={<TodayCalendarIcon />} />
                    <IconButton
                        onClick={() => {
                            setBulkSMSSelectedDate(date);
                            setBulkSMSModalOpen(true);
                        }}
                    >
                        <SMSOutlineIcon />
                    </IconButton>
                    <div className="flex items-center gap-2 p-1">
                        <IconButton onClick={toPrev} className="!p-1" children={<LeftArrowCircleIcon />} />
                        <IconButton onClick={toNext} className="!p-1" children={<RightArrowCircleIcon />} />
                    </div>
                </div>
            </>
        );
    }

    function MobileExtraPanel() {
        return (
            <Dialog open={extraBarOpen} onClose={() => setExtraBarOpen(false)} className="relative z-50">
                <DialogBackdrop className="fixed inset-0 bg-black/30" />
                <DialogPanel className="fixed right-0 top-0 h-screen w-3/4 bg-white px-6 py-8 shadow-card dark:bg-dimGray">
                    <div className="flex flex-col items-start space-y-4">
                        <Button
                            size="small"
                            variant="outlined"
                            onClick={() => {
                                setExtraBarOpen(false);
                                setOpenHoursModalOpen(true);
                            }}
                        >
                            {strings.open_hours}
                        </Button>
                        <CalendarSlotIntervalSelect
                            selected={intervalSlot.id}
                            onChange={(id) => {
                                setIntervalSlot(id);
                            }}
                        />
                    </div>
                </DialogPanel>
            </Dialog>
        );
    }
};

const ClassicCalendarHeaderButton = ({
    text,
    onClick,
    active,
    textMobile,
}: {
    text: string;
    textMobile: string;
    onClick: () => void;
    active: boolean;
}) => {
    return (
        <button className="relative py-2" onClick={onClick}>
            <p className={`${active ? '' : 'text-mediumGray'}`}>
                <span className="hidden sm:block">{text}</span>
                <span className="block sm:hidden">{textMobile}</span>
            </p>
            {active && <span className="absolute bottom-0 left-0 h-1 w-full rounded-t-full bg-primary"></span>}
        </button>
    );
};

export default ClassicCalendarHeader;
