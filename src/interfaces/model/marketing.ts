

export type filterType = "joined" | "booking" | "age" | "upcoming_birthday" | "has_mobile_number";
interface filterValue {
    key: string;
    value: any;
    type: filterType;
}
export interface Marketing {
    id: number;
    name: string;
    content: string;
    credit_used: string;
    client_count: string;
    client_filter: filterValue[];
    created_at: string;
    updated_at: string;
    summary: string
    failed: number;
}
