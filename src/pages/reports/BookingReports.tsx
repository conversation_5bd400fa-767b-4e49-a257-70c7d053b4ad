import { customRound, generateUserFullName, getUnitKeyToValue } from '@/helper';
import Card from '@components/card';
import ReportIconItem from '@components/card/ReportMetricCard';
import AreaChartComponent from '@components/Charts/AreaChart';
import { BarChartData } from '@components/Charts/BarChart';
import LineMultiChartComponent, { LineMultiChartData } from '@components/Charts/LineMultiChart';
import { chartColors } from '@components/Charts/PieChartSingleColor';
import ReportTableComponent from '@components/Reports/ReportTableComponent';
import ReportTitleValueComponent from '@components/Reports/ReportTitleValueComp';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import CalendarIcon from '@icons/Calendar';
import ClientsIcon from '@icons/Clients';
import DollarIcon from '@icons/Dollar';
import { CommonModelResponse } from '@interface/common';
import { Category } from '@interface/model/category';
import { Service } from '@interface/model/service';
import { User } from '@interface/model/user';
import strings from '@lang/Lang';
import CrossIcon from '@partials/Icons/Cross';
import UserNoShowIcon from '@partials/Icons/UserNoShow';
import ReportsProvider, { useReports } from '@provider/ReportsProvider';
import cx from 'classix';
import { FC, useMemo } from 'react';
import useSWRImmutable from 'swr/immutable';

export type BookingReportAPITypes =
    | 'booking_statistics'
    | 'company_bookings'
    | 'booking_by_online_and_manual'
    | 'top_booking_services'
    | 'top_booking_categories'
    | 'top_practitioners'
    | 'booking_cancelled_and_no_show';

interface BookingReportStatsResponse {
    total_single_bookings: number;
    total_group_bookings: number;
    total_group_bookings_individual: number;
    total_bookings_revenue: number;
    total_cancelled_bookings: number;
    total_bookings_no_show: number;
}

export interface RecordReportsProps {}

type BookingOnlineAndManualResponse = {
    bookings_online: number;
    bookings_manual: number;
};

interface UserWithBookingsCount extends User {
    bookings_count: number;
    bookings_sum_price: number;
}

const BookingReports: FC<RecordReportsProps> = ({}) => {
    const { user } = useAuth();
    const { endDate, period, startDate, selectedPractitioners } = useReports();

    const { data: statsResponse, isLoading: isStatsLoading } = useSWRImmutable<CommonModelResponse<BookingReportStatsResponse>>(
        api.reports.booking('booking_statistics', startDate, endDate, period, selectedPractitioners),
    );

    const { data: companyBookingResponse } = useSWRImmutable<CommonModelResponse<BarChartData[]>>(
        api.reports.booking('company_bookings', startDate, endDate, period, selectedPractitioners),
    );
    const companyBookingTotalCount = companyBookingResponse?.data.reduce((acc, curr) => acc + curr.value, 0);

    // const { data: bookingOnlineAndManualResponse } = useSWRImmutable<CommonModelResponse<BookingOnlineAndManualResponse>>(
    //     api.reports.booking('booking_by_online_and_manual', startDate, endDate, period, selectedPractitioners),
    // );
    // const bookingOnlineAndManualData = useMemo(() => {
    //     if (bookingOnlineAndManualResponse?.data) {
    //         return [
    //             { name: strings.online, value: bookingOnlineAndManualResponse?.data.bookings_online },
    //             { name: strings.manual, value: bookingOnlineAndManualResponse?.data.bookings_manual },
    //         ];
    //     }
    //     return [];
    // }, [bookingOnlineAndManualResponse]);

    const { data: BookingCancelledNoShowResponse } = useSWRImmutable<CommonModelResponse<LineMultiChartData[]>>(
        api.reports.booking('booking_cancelled_and_no_show', startDate, endDate, period, selectedPractitioners),
    );

    const BookingCancelledNoShowDataStats = useMemo(() => {
        return {
            cancelled: BookingCancelledNoShowResponse?.data.reduce((acc, curr) => acc + curr.value, 0) ?? 0,
            no_show: BookingCancelledNoShowResponse?.data.reduce((acc, curr) => acc + curr.value2, 0) ?? 0,
        };
    }, [BookingCancelledNoShowResponse?.data]);

    const companyUnit = getUnitKeyToValue(user?.company?.unit ?? '');

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 xl:grid-cols-5">
                <ReportIconItem
                    color="primary"
                    icon={<CalendarIcon />}
                    value={statsResponse?.data.total_single_bookings ?? 0}
                    title={strings.reports_strings.total_single_booking}
                    loading={isStatsLoading}
                />
                <ReportIconItem
                    color="primary"
                    icon={<CalendarIcon />}
                    value={
                        <div className="flex space-x-3">
                            <p>{statsResponse?.data.total_group_bookings ?? 0}</p>
                            <span className="flex items-center space-x-1">
                                {statsResponse?.data.total_group_bookings ? (
                                    <>
                                        (<ClientsIcon />
                                        {statsResponse?.data.total_group_bookings_individual})
                                    </>
                                ) : (
                                    ''
                                )}
                            </span>
                        </div>
                    }
                    title={strings.reports_strings.total_group_booking}
                    loading={isStatsLoading}
                />
                <ReportIconItem
                    color="yellow"
                    icon={<DollarIcon />}
                    value={`${companyUnit} ${customRound(statsResponse?.data.total_bookings_revenue ?? 0, 0)}`}
                    title={strings.reports_strings.booking_revenue}
                    loading={isStatsLoading}
                />
                <ReportIconItem
                    color="green"
                    icon={<CrossIcon />}
                    value={statsResponse?.data.total_cancelled_bookings ?? 0}
                    title={strings.Cancelled}
                    loading={isStatsLoading}
                />
                <ReportIconItem
                    color="orange"
                    icon={<UserNoShowIcon />}
                    value={statsResponse?.data.total_bookings_no_show ?? 0}
                    title={strings.no_show}
                    loading={isStatsLoading}
                />
            </div>
            <div className="grid grid-cols-2 gap-4 lg:grid-cols-4">
                <Card className="col-span-2">
                    <ReportTitleValueComponent title={strings.bookings} value={companyBookingTotalCount} />
                    <AreaChartComponent data={companyBookingResponse?.data ?? []} labelName={strings.bookings} />
                </Card>
                <ReportTableComponent<UserWithBookingsCount>
                    api={api.reports.booking('top_practitioners', startDate, endDate, period, selectedPractitioners)}
                    className="col-span-2"
                    headings={[strings.No, strings.Name, strings.booked, strings.revenue]}
                    renderColumn={(item, index, rowIndex) => [
                        rowIndex + 1,
                        generateUserFullName(item),
                        item.bookings_count,
                        cx(companyUnit, `${customRound(item.bookings_sum_price ?? 0, 0)}`),
                    ]}
                    title={strings.reports_strings.top_practitioners}
                />

                <ReportTableComponent<Service>
                    api={api.reports.booking('top_booking_services', startDate, endDate, period, selectedPractitioners)}
                    className="col-span-2"
                    headings={[strings.No, strings.Name, strings.booked, strings.revenue]}
                    renderColumn={(item, index, rowIndex) => [
                        rowIndex + 1,
                        item.name,
                        item.bookings_count,
                        cx(companyUnit, `${customRound(item.bookings_sum_price ?? 0, 0)}`),
                    ]}
                    title={strings.reports_strings.top_booking_services}
                />
                <ReportTableComponent<Category>
                    api={api.reports.booking('top_booking_categories', startDate, endDate, period, selectedPractitioners)}
                    className="col-span-2"
                    headings={[strings.No, strings.Name, strings.booked, strings.revenue]}
                    renderColumn={(item, index, rowIndex) => [
                        rowIndex + 1,
                        item.name,
                        item.bookings_count,
                        cx(companyUnit, `${customRound(item.bookings_sum_price ?? 0, 0)}`),
                    ]}
                    title={strings.reports_strings.top_service_category}
                />
                <Card className="col-span-2 lg:col-span-4">
                    <ReportTitleValueComponent title={cx(strings.booking_cancelled, '/', strings.no_show)}>
                        <div className="flex space-x-3 text-sm">
                            <div className="flex items-center space-x-1">
                                <span className="inline-block size-3 rounded-full" style={{ backgroundColor: chartColors.primary }} />
                                <p>
                                    {strings.Cancelled} ({BookingCancelledNoShowDataStats.cancelled})
                                </p>
                            </div>
                            <div className="flex items-center space-x-1">
                                <span className="inline-block size-3 rounded-full" style={{ backgroundColor: chartColors.violet }} />
                                <p>
                                    {strings.no_show} ({BookingCancelledNoShowDataStats.no_show})
                                </p>
                            </div>
                        </div>
                    </ReportTitleValueComponent>
                    <LineMultiChartComponent
                        data={BookingCancelledNoShowResponse?.data ?? []}
                        color="violet"
                        labels={[strings.Cancelled, strings.no_show]}
                    />
                </Card>
            </div>
        </div>
    );
};

const BookingReportsHoC: FC = () => (
    <ReportsProvider section="booking" title={strings.reports_strings.reports_booking} children={<BookingReports />} />
);

export default BookingReportsHoC;
