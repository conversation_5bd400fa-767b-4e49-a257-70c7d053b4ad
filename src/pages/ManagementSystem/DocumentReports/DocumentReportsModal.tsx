import config from '@/config';
import { convertBase64ToFile } from '@/helper';
import {
    DynamicQuestionaryValues,
    DynamicQuestionaryValuesData,
} from '@components/Clients/Client/Questionaries/DynamicQuestionary/ClientDynamicQuestionariesModal';
import TipTapEditor from '@components/TipTap/TipTapEditor';
import Button from '@components/form/Button';
import InfoCard from '@components/form/InfoCard';
import Input from '@components/form/Input';
import Select from '@components/form/Select';
import api from '@configs/api';
import { AdminQuestionaryQuestionResponse, SMSPrescriberTemplatePaginatedResponse, UserDocumentResponse } from '@interface/common';
import { DocumentVersion, Question } from '@interface/model/document';
import { AdminQuestionaryQuestion } from '@interface/model/questionary';
import strings from '@lang/Lang';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import DynamicQuestionaryComponent from '@partials/Qestionary/DynamicQuestionaryComponent';
import { Formik, FormikErrors, getIn } from 'formik';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import useSWRImmutable from 'swr/immutable';
import DocumentReportSignModal from './DocumentReportSignModal';
import DocumentReportsQuestionaryComponent from './DocumentReportsQuestionaryComponent';
import dayjs from 'dayjs';

export interface DocumentReportsModalProps {
    openModal: boolean;
    onClose: () => void;
    mutate: () => Promise<any>;
    selectedDocumentId?: number;
}

export interface DocumentReportsModalForm {
    template_id?: number;
    title?: string;
}

export interface DynamicQuestionaryValuesExtra extends DynamicQuestionaryValues {
    title?: string;
    signature?: string;
    signature_modal_open?: boolean;
    process: string;
}

export function processOptions() {
    return [
        {
            value: 'PATIENT_SAFETY',
            label: strings.patient_safety,
        },
        {
            value: 'DEVIATION_MGMT',
            label: strings.deviation_mgmt,
        },
        {
            value: 'THE_BUSINESS',
            label: strings.the_business,
        },
    ];
}
function findProcessById(val: string) {
    return processOptions().find((item) => item.value === val);
}

const DocumentReportsModal: React.FC<DocumentReportsModalProps> = ({ openModal, onClose, mutate, selectedDocumentId }) => {
    const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
    const time = useRef(dayjs().valueOf());

    const handleClose = () => {
        onClose();
    };

    const { setValue, getValues, watch } = useForm<DocumentReportsModalForm>({ defaultValues: { template_id: selectedDocumentId } });

    const { data, isLoading } = useSWR<SMSPrescriberTemplatePaginatedResponse, Error>(api.generalTemplateList('MANAGEMENT_DOCUMENT'));

    const findQuestionaryTemplateById = (id?: string | number) => {
        if (!id) return undefined;
        return data?.data.find((template) => template.id === parseInt(id.toString())) ?? undefined;
    };

    const selectedQuestionary = findQuestionaryTemplateById(watch('template_id'));

    const { data: documentData, isLoading: documentLoading } = useSWRImmutable<UserDocumentResponse, Error>(
        !selectedDocumentId ? null : api.documents.view(selectedDocumentId) + `?${time.current}`,
        {
            revalidateOnMount: true,
        },
    );

    const version = documentData?.data.version;

    const {
        data: questionnaireQuestionData,
        error,
        isLoading: isLoadingQuestionsData,
    } = useSWR<AdminQuestionaryQuestionResponse, Error>(
        !selectedQuestionary ? null : api.generalTemplateQuestionList(selectedQuestionary?.id) + `?${time.current}`,
    );

    return (
        <Formik<DynamicQuestionaryValuesExtra>
            initialValues={
                selectedDocumentId
                    ? {
                          data: getInitialValuesToSet(documentData?.data.version?.questions, version),
                          title: documentData?.data.title,
                          process: documentData?.data.process ?? '',
                      }
                    : { data: [{ question: '', type: 'html_editor', text: '' }], title: '', process: '' }
            }
            validate={(values) =>
                validateClientDynamicQuestionnaire(
                    values,
                    (selectedDocumentId ? documentData?.data.version?.questions : questionnaireQuestionData?.data) ?? [],
                )
            }
            enableReinitialize={selectedDocumentId ? true : false}
            onSubmit={async (values, { setFieldError, setSubmitting, validateForm }) => {
                setSubmitting(true);
                const formData = new FormData();
                formData.set('title', values.title ?? '');
                formData.set('process', values.process ?? '');
                if (values.signature) {
                    formData.set('signature', convertBase64ToFile(values.signature));
                }
                if (getValues('template_id')) {
                    formData.set('general_template_id', getValues('template_id')?.toString() ?? '');
                }

                for (let index = 0; index < values.data.length; index++) {
                    const data = values.data[index];
                    const keys = Object.keys(data);
                    const hasAnswer = keys.includes('value');
                    if (hasAnswer) {
                        formData.set(`data[${index}][value]`, data.value === 1 ? 'yes' : data.value === 0 ? 'no' : '');
                    }

                    if (data.type === 'html_editor') {
                        formData.set(`data[${index}][text]`, data.text ?? '');
                    } else {
                        formData.set(hasAnswer ? `data[${index}][text]` : `data[${index}]`, data.text ?? '');
                    }
                }

                const response = await fetch(selectedDocumentId ? api.documents.update(selectedDocumentId) : api.documents.store, {
                    method: 'POST',
                    headers: {
                        'X-App-Locale': strings.getLanguage(),
                        Accept: 'application/json',
                    },
                    credentials: 'include',
                    body: formData,
                });

                const data = await response.json();
                if (data.status !== '1') {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                } else {
                    toast.success(data.message || 'dynamic questionary updated successfully.');
                    await mutate();
                    onClose();
                }
                setSubmitting(false);
            }}
        >
            {({
                handleSubmit,
                values,
                setFieldValue,
                setFieldTouched,
                handleChange,
                handleBlur,
                errors,
                touched,
                dirty,
                isSubmitting,
                validateForm,
            }) => {
                const signatureModalOpen = values.signature_modal_open === true;

                return (
                    <>
                        {signatureModalOpen && (
                            <DocumentReportSignModal openModal={signatureModalOpen} onClose={() => setFieldValue('signature_modal_open', false)} />
                        )}
                        <Modal
                            open={signatureModalOpen ? false : openModal}
                            title={
                                selectedDocumentId ? (
                                    <span className="line-clamp-2 break-all px-4">{documentData?.data.title}</span>
                                ) : (
                                    strings.document_reports
                                )
                            }
                            handleClose={handleClose}
                            size="large"
                            cancelButton={<CancelButton disabled={!!isSubmitting} onClick={handleClose} />}
                            submitButton={
                                <Button
                                    loading={isSubmitting}
                                    onClick={async () => {
                                        if (!dirty) {
                                            toast.success(strings.no_data_changed);
                                            handleClose();
                                            return;
                                        }

                                        const errors = await validateForm();

                                        if (!errors || Object.keys(errors).length === 0) {
                                            return setFieldValue('signature_modal_open', true);
                                        }
                                        return handleSubmit();
                                    }}
                                    children={values.signature ? strings.Submit : strings.Sign}
                                />
                            }
                        >
                            {selectedDocumentId && (documentLoading || isLoadingQuestionsData) ? (
                                <div className="mb-8 flex h-56 items-center justify-center">
                                    <SectionLoading />
                                </div>
                            ) : (
                                <div className="grid min-h-96 grid-cols-1 gap-x-6 gap-y-4 p-4 md:grid-cols-2">
                                    {!selectedDocumentId && (
                                        <>
                                            <div className="md:col-span-2">
                                                <Input
                                                    type="text"
                                                    onChange={handleChange}
                                                    onBlur={handleBlur}
                                                    label={strings.TITLE}
                                                    name="title"
                                                    required
                                                    value={values.title}
                                                    error={touched?.title && errors.title}
                                                />
                                            </div>
                                            <Select
                                                displayValue={(val) => findQuestionaryTemplateById(val)?.name ?? ''}
                                                loading={isLoading || isLoadingQuestions}
                                                disabled={isLoadingQuestions}
                                                defaultValue={getValues('template_id') ?? ''}
                                                label={strings.Template}
                                                placeholder={strings.Select}
                                                onChange={async (data) => {
                                                    setValue('template_id', parseInt(data));

                                                    if (!isNaN(parseInt(data.toString()))) {
                                                        const template = findQuestionaryTemplateById(data);
                                                        if (!values.title?.length) {
                                                            setFieldValue('title', template?.name ?? 'asdasad');
                                                        }
                                                        setFieldValue('process', template?.process ?? '');

                                                        setIsLoadingQuestions(true);
                                                        const d = await fetch(api.generalTemplateQuestionList(data), { credentials: 'include' }).then(
                                                            (res) => res.json(),
                                                        );
                                                        await setFieldValue('data', getInitialValuesToSet(d?.data));
                                                        setIsLoadingQuestions(false);
                                                    }
                                                    if (!data) {
                                                        setFieldValue('data', [{ question: '', type: 'html_editor', text: '' }]);
                                                    }
                                                }}
                                            >
                                                <Select.Option value="" children={strings.none} />
                                                {data?.data?.map((template) => (
                                                    <Select.Option key={template.id} children={template.name} value={template.id} />
                                                ))}
                                            </Select>
                                            <Select
                                                displayValue={(val) => findProcessById(val?.toString() ?? '')?.label ?? ''}
                                                value={values.process}
                                                label={strings.process}
                                                placeholder={strings.Select}
                                                required
                                                onChange={async (data) => {
                                                    setFieldTouched('process');
                                                    setFieldValue('process', data);
                                                }}
                                                error={touched?.process && errors.process}
                                            >
                                                {processOptions().map((process) => (
                                                    <Select.Option key={process.value} children={process.label} value={process.value} />
                                                ))}
                                            </Select>
                                        </>
                                    )}
                                    <div className="md:col-span-2">
                                        {selectedDocumentId ? (
                                            <DynamicQuestionaryComponent />
                                        ) : !!selectedQuestionary ? (
                                            <DocumentReportsQuestionaryComponent selectedQuestionary={selectedQuestionary} />
                                        ) : (
                                            <TipTapEditor
                                                onChange={async (data) => {
                                                    await setFieldValue(`data.0.text`, data.replace(/^<p><\/p>/, '').trim(), false);
                                                    setFieldTouched(`data.0.text`);
                                                }}
                                                data={getIn(values, 'data.0.text')}
                                                error={getIn(touched, `data.0`) ? getIn(errors, `data.0.text`) : undefined}
                                                helperText={getIn(touched, `data.0`) ? getIn(errors, `data.0.text`) : undefined}
                                            />
                                        )}
                                        <InfoCard variant="error" message={errors.server} />
                                    </div>
                                </div>
                            )}
                        </Modal>
                    </>
                );
            }}
        </Formik>
    );
};

export function getInitialValuesToSet(data?: AdminQuestionaryQuestion[] | Question[], version?: DocumentVersion): DynamicQuestionaryValuesData[] {
    if (!data?.length) return [];
    return data.map((question, index) => {
        if (question.type === config.questionTypes1.yes_no_textbox.value) {
            return {
                question: question.question,
                type: question.type,
                value: version?.formatted_response[index]?.value ? (version.formatted_response[index]?.value === 'yes' ? 1 : 0) : '',
                text: version?.formatted_response[index]?.text ? version.formatted_response[index]?.text : '',
            };
        }

        if (question.type === config.questionTypes1.yes_no.value) {
            return {
                question: question.question,
                type: question.type,
                value: version?.formatted_response[index]?.value ? (version.formatted_response[index]?.value === 'yes' ? 1 : 0) : '',
            };
        }

        if (question.type === config.questionTypes1.textbox.value) {
            return {
                question: question.question,
                type: question.type,
                text: typeof version?.formatted_response[index] === 'string' ? (version.formatted_response[index] as string) : '',
            };
        }

        if (question.type === config.questionTypes1.html_editor.value) {
            return {
                question: question.question,
                type: question.type,
                text: version?.formatted_response[index]?.text ? version.formatted_response[index]?.text : question.default,
            };
        }

        return {
            question: question.question,
            type: question.type,
            value: null,
            text: null,
        };
    });
}

interface ErrorType {
    value?: string;
    text?: string;
    files?: string;
}

function validateClientDynamicQuestionnaire(
    values: DynamicQuestionaryValuesExtra,
    questions: AdminQuestionaryQuestion[] | Question[],
): void | object | Promise<FormikErrors<DynamicQuestionaryValuesExtra>> {
    let errors: FormikErrors<DynamicQuestionaryValuesExtra> = {};

    if (!errors?.data) {
        errors.data = [];
    }

    let hasError = false;
    for (let i = 0; i < questions.length; i += 1) {
        const question = questions[i];
        if (question.type === config.questionTypes1.yes_no_textbox.value) {
            let error: ErrorType = {};
            if (question.required && !values?.data[i]?.value && values?.data[i]?.value !== 0) {
                error.value = strings.please_provide_valid_answer;
                hasError = true;
            }

            if (question.required && values?.data[i]?.value === 1 && !values?.data[i]?.text) {
                error.text = strings.more_info_required;
                hasError = true;
            }

            (errors.data as ErrorType[]).push(error);
        } else if (question.type === config.questionTypes1.yes_no.value) {
            let error: ErrorType = {};
            if (question.required && !values?.data[i]?.value && values?.data[i]?.value !== 0) {
                error.value = strings.please_provide_valid_answer;
                hasError = true;
            }

            (errors.data as ErrorType[]).push(error);
        } else if (question.type === config.questionTypes1.textbox.value) {
            let error: ErrorType = {};
            if (question.required && !values?.data[i]?.text) {
                error.text = strings.please_provide_some_explanation;
                hasError = true;
            }
            (errors.data as ErrorType[]).push(error);
        } else if (question.type === config.questionTypes1.file_upload.value) {
            let error: ErrorType = {};
            if (question.required && !values?.data[i]?.files?.length) {
                error.files = strings.please_provide_some_explanation;
                hasError = true;
            }
            (errors.data as ErrorType[]).push(error);
        } else if (question.type === config.questionTypes1.html_editor.value) {
            let error: ErrorType = {};
            if (question.required && !values?.data[i]?.text) {
                error.text = strings.please_provide_some_explanation;
                hasError = true;
            }

            (errors.data as ErrorType[]).push(error);
        } else {
            (errors.data as ErrorType[]).push({});
        }
    }

    if (!questions.length) {
        if (!values.data[0].text?.length) {
            (errors.data as ErrorType[]).push({ text: strings.text_is_required });
            hasError = true;
        }
    }

    if (!values.title) {
        errors.title = strings.title_is_required;
    }

    if (!values.process) {
        errors.process = strings.Required;
    }

    if (!hasError) {
        if (errors.title || errors.server || errors.process) return errors;
        return {};
    }

    return errors;
}

export default DocumentReportsModal;
