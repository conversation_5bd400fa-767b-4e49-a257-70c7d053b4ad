import Button from '@components/form/Button';
import Modal from '@components/modal/Modal';
import pos_strings from '@lang/pos/Lang';
import Error from '@partials/Error/Error';
import FortnoxIcon from '@partials/Icons/Fortnox';
import React from 'react';

interface FortnoxConnectionProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    link?: string;
    loading?: boolean;
    error?: string;
}

const FortnoxConnectionModal: React.FC<FortnoxConnectionProps> = ({ open, setOpen, link, loading, error }) => {
    return (
        <Modal
            open={open}
            handleClose={() => {
                setOpen(false);
            }}
        >
            <div className="flex flex-col items-center space-y-8 py-8">
                <FortnoxIcon size={80} />

                <div className="flex flex-col items-center justify-center space-y-2 text-center">
                    <p className="text-lg font-semibold">{`${pos_strings.fortnox.connect_to_fortnox}`}</p>
                    <p className="text-sm font-normal text-primaryLight">{`${pos_strings.fortnox.for_invoice_solutions}`}</p>
                </div>

                <Button
                    onClick={() => {
                        if (link) {
                            window.open(link);
                            setOpen(false);
                        }
                    }}
                    loading={loading}
                >
                    {pos_strings.fortnox.connect}
                </Button>
                <Error error={error} />
            </div>
        </Modal>
    );
};

export default FortnoxConnectionModal;
