import Button from '@components/form/Button';
import api from '@configs/api';
import { Videocall } from '@interface/model/videoCall';
import strings from '@lang/Lang';
import DeleteIcon from '@partials/Icons/Delete';
import DeleteModal from '@partials/MaterialModal/DeleteModal';
import { useState } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';

export interface ServiceDeleteModalProps {
    open?: boolean;
    handleClose?: () => void;
    video_call: Videocall;
    mutate: () => Promise<any>;
}

const VideoCallDeleteModal: React.FC<ServiceDeleteModalProps> = ({ open = false, handleClose = () => {}, video_call, mutate }) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const navigate = useNavigate();
    return (
        <DeleteModal
            open={open}
            handleClose={() => {
                if (isSubmitting) return;
                handleClose();
            }}
            icon={<DeleteIcon />}
            text={`${strings.Are_you_sure_you_want_to} ${(video_call.started_at ? strings.end_video_call : strings.cancel_video_call).toLowerCase()}?`}
            cancelText={strings.No}
            submitButton={
                <Button
                    loading={isSubmitting}
                    onClick={async () => {
                        setIsSubmitting(true);
                        const response = await fetch(`${api.videoCall.cancel(video_call.id)}`, {
                            method: 'DELETE',
                            headers: {
                                Accept: 'application/json',
                                'X-App-Locale': strings.getLanguage(),
                                'Content-Type': 'application/json',
                            },
                            credentials: 'include',
                        });

                        const data = await response.json();

                        if (response.status === 401) {
                            navigate('/');
                        }

                        if (data.status === '1') {
                            await mutate();
                            toast.success(data.message);
                            handleClose();
                        } else {
                            toast.error(data.message || 'server error, please contact admin.');
                        }
                        setIsSubmitting(false);
                        handleClose();
                    }}
                >
                    {strings.Yes}
                </Button>
            }
        />
    );
};

export default VideoCallDeleteModal;
