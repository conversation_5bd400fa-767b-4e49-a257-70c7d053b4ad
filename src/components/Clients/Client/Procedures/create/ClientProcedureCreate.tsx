import Button from '@components/form/Button';
import Heading from '@components/heading/Heading';
import api, { DateFormat } from '@configs/api';
import useClient from '@hooks/useClient';
import useDebounce from '@hooks/useDebounce';
import useLocalStorage from '@hooks/useLocalStorage';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import useQuery from '@hooks/useQuery';
import { useWindowSize } from '@hooks/useWindowSize';
import { ClientTreatment } from '@interface/model/clientTreatment';
import { ClientPrescription } from '@interface/model/prescription';
import strings from '@lang/Lang';
import FullPageError from '@partials/Error/FullPageError';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import { LinearProgressWithLabel } from '@partials/Loadings/LinearProgressWithLabel';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import RouteLeavingGuard from '@partials/MaterialModal/RouteLeavingGuard';
import { useHours } from '@provider/TimeProvider';
import { Formik } from 'formik';
import { Resizable } from 're-resizable';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate, useParams } from 'react-router';
import { toast } from 'react-toastify';
import useSWR, { useSWRConfig } from 'swr';
import useSWRImmutable from 'swr/immutable';
import { v4 as uuidv4 } from 'uuid';
import { convertBase64ToFile, isDataURL, isHTML, getCookie } from '../../../../../helper';
import { ClientProcedurePaginatedResponse, ClientTreatmentResponse, SettingResponse } from '../../../../../interfaces/common';
import BackToClientDashboard from '../../sections/BackToClientDashboard';
import ClientProcedureCanvasHandler from './ClientProcedureCanvasHandler';
import ClientProcedureCreateForm from './ClientProcedureCreateForm';
import ClientProcedureDrawingBoard from './ClientProcedureDrawingBoard';
import Card from '@components/card';
import { FieldError, Form, FormProvider, ResolverError, useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { flushSync } from 'react-dom';
import data from '@lang/sv';

// TODO: handle back press correctly.

export interface ClientProcedureCreateProps {
    procedureData?: ClientTreatmentResponse | null;
}

export type CanvasShape = 'circle' | 'cross' | 'pen' | 'filled_circle' | 'gradient_circle' | 'text' | 'customText';

export interface IClientProcedureCreateValues {
    name: string;
    date: string;
    treatments: {
        id: string | number;
        actual_cost: string | number;
    }[];
    images: {
        id: string;
        image: string;
        imageData: string | Blob;
        canvasData: string;
        fileId?: number;
        updated?: boolean;
    }[];
    deletedIds?: number[];
    prescriptions?: ClientPrescription[];
    selectedIndex: number;
    notes?: string;
    notes_html?: string;
    server?: string;
    progress?: number;
    shape: CanvasShape;
    brushRadius: number;
    text: string;
    customText: string;
    nrs_rating: string;
}

const imagesData = [
    {
        id: uuidv4(),
        image: '',
        imageData: '',
        canvasData: '',
        fileId: undefined,
        updated: undefined,
    },
];

const ClientProcedureSignModal = React.lazy(() => import('./../ClientProcedureVerifySignModal'));

const ClientProcedureCreate: React.FC<ClientProcedureCreateProps> = ({ procedureData }) => {
    const { clientProcedureId }: { clientProcedureId?: string } = useParams();
    const { clientId, mutateCounts } = useClient();
    const { mutate: swrMutate } = useSWRConfig();

    const query = useQuery();

    const hasCopy = query.has('copy');

    const { mutate: clientMutate, error } = useClient();
    const [selectedProcedure, setSelectedProcedure] = React.useState<ClientTreatment>();
    const [isSaveSign, setIsSaveSign] = React.useState(false);
    const { mutate } = usePaginationSWR<ClientProcedurePaginatedResponse, Error>(api.clientTreatments(clientId), {
        limit: 3,
    });

    const { data: settingData } = useSWR<SettingResponse>(api.setting);

    const enableNRS = settingData?.data.find((setting) => setting.key === api.NRS_RATING && setting.value === '1')?.value;

    const navigate = useNavigate();
    const [showUpgrade, setShowUpgrade] = useState(false);
    const [isFormSuccess, setIsFormSuccess] = useState(false);
    const [openModal, setOpenModal] = React.useState(false);
    const { dateFormat, parseDate, renderDate } = useHours();

    const { width } = useWindowSize();

    const { storedValue: storedWidth, setStorageValue: setStoredWidth } = useLocalStorage('procedure_side_width', '50%');

    useEffect(() => {
        if (isFormSuccess) {
            navigate(-1);
        }
        // eslint-disable-next-line
    }, [isFormSuccess]);

    const defaultValues: IClientProcedureCreateValues = useMemo(() => {
        return procedureData?.data
            ? {
                name: `${procedureData.data.name || ''}${hasCopy ? ' - Copy' : ''}`,
                date: renderDate(procedureData?.data.date) || '',
                selectedIndex: 0,
                images: !hasCopy
                    ? procedureData.data.files.map((file, index) => {
                        return {
                            id: uuidv4(),
                            image: file.url, // &id=${uuidv4()}
                            imageData: '',
                            canvasData: '',
                            fileId: file.id,
                            updated: false,
                        };
                    }) || imagesData
                    : imagesData,
                // notes: procedureData.data.notes || '',
                notes_html: `${procedureData.data?.notes_html
                        ? (procedureData.data?.notes_html ?? '')
                        : isHTML(procedureData.data?.notes ?? '')
                            ? procedureData.data?.notes
                            : `${procedureData.data?.notes ?? ''}`.replaceAll('\n', '<br/>')
                    }`,
                treatments:
                    procedureData.data.details.map((treatment) => {
                        return {
                            actual_cost: parseFloat(treatment.actual_cost),
                            id: treatment.treatment_id,
                        };
                    }) || [],
                shape: 'pen',
                brushRadius: 2,
                text: '1',
                customText: '',
                prescriptions: procedureData?.data.prescriptions,
                nrs_rating: procedureData.data.nrs_rating,
            }
            : {
                name: '',
                date: '',
                selectedIndex: 0,
                images: imagesData,
                notes: '',
                notes_html: '',
                treatments: [],
                shape: 'pen',
                brushRadius: 2,
                text: '1',
                customText: '',
                prescriptions: [],
                nrs_rating: '',
            };
    }, [procedureData?.data, clientProcedureId]);

    const methods = useForm<IClientProcedureCreateValues>({
        defaultValues,
        resolver: (values) => validateClientTreatmentStore(values, dateFormat),
        mode: 'onSubmit',
    });

    const {
        reset,
        handleSubmit,
        setError,
        watch,
        setValue,
        getValues,
        formState: { isSubmitting, errors, isDirty },
        control,
    } = methods;

    const formRef = useRef<HTMLFormElement>(null);

    if (error) {
        return <FullPageError code={error?.status || 500} message={error?.message || 'server error'} />;
    }

    return (
        <>
            <Helmet>
                <link rel="stylesheet" href={`${import.meta.env.PUBLIC_URL || ''}/css/procedure.css`} />
            </Helmet>
            <BackToClientDashboard />
            <FormProvider {...methods}>
                <Card className="mb-8">
                    <div className="mb-4">
                        <Heading text={strings.CreateProcedure} variant="bigTitle" />
                    </div>
                    <form
                        ref={formRef}
                        onSubmit={handleSubmit(async (values) => {
                            try {
                                // Create form data
                                const formData = new FormData();
                                if (values.notes_html) {
                                    formData.append('notes_html', values.notes_html ?? '');
                                } else {
                                    formData.append('notes', values.notes ?? '');
                                }
                                formData.append('date', values?.date ? parseDate(values?.date).format(api.dateFormat) : '');
                                formData.append('name', values?.name ? values?.name : '');
                                formData.append('description', values?.notes ? values?.notes : '');
                                formData.append('client_id', clientId!);
                                if (enableNRS && (values.nrs_rating || values.nrs_rating?.toString() === '0')) {
                                    formData.append('nrs_rating', values.nrs_rating);
                                }
                                formData.append('color', '#0066ff');

                                // Append treatments
                                for (let index = 0; index < values.treatments.length; index++) {
                                    formData.append(`treatments[${index}][id]`, values.treatments[index].id.toString());
                                    if (values.treatments[index].actual_cost) {
                                        formData.append(`treatments[${index}][actual_cost]`, values.treatments[index]?.actual_cost?.toString());
                                    }
                                }

                                // Append prescriptions
                                values.prescriptions?.map((v, i) => formData.append(`prescriptions[${i}]`, v.id.toString()));

                                // Handle images based on whether it's an update or new creation
                                if (clientProcedureId && !hasCopy) {
                                    for (let index = 0; index < values.images.length; index++) {
                                        if (values.images[index]?.updated && values.images[index]?.imageData instanceof Blob) {
                                            formData.append(`images[${index}]`, values.images[index].imageData);
                                            formData.append(
                                                `files[${index}]`,
                                                values.images[index]?.fileId ? (values.images[index]?.fileId?.toString() as string) : '-1',
                                            );
                                        } else if (
                                            values.images[index]?.updated &&
                                            typeof values.images[index].imageData == 'string' &&
                                            isDataURL(values.images[index].imageData as string)
                                        ) {
                                            formData.append(`images[${index}]`, convertBase64ToFile(values.images[index].imageData as string));
                                            formData.append(
                                                `files[${index}]`,
                                                values.images[index]?.fileId ? (values.images[index]?.fileId?.toString() as string) : '-1',
                                            );
                                        }
                                    }
                                } else {
                                    for (let index = 0; index < values.images.length; index++) {
                                        if (values.images[index]?.imageData instanceof Blob) {
                                            formData.append(
                                                `images[]`,
                                                new File([values.images[index].imageData], 'canvasImage.jpeg', { type: 'image/jpeg' }),
                                            );
                                        } else if (values.images[index].imageData && isDataURL(values.images[index].imageData as string)) {
                                            formData.append(`images[]`, convertBase64ToFile(values.images[index].imageData as string));
                                        }
                                    }
                                }

                                // Create a Promise-based XMLHttpRequest
                                const submitProcedure = () => {
                                    return new Promise(async (resolve, reject) => {
                                        const request = new XMLHttpRequest();

                                        const endpoint =
                                            clientProcedureId && !hasCopy
                                                ? api.clientProcedureUpdateMobile.replace(':id', clientProcedureId)
                                                : api.clientProcedureStore.replace(':id', clientId!);

                                        request.open('POST', endpoint);
                                        request.setRequestHeader('Accept', 'application/json');
                                        request.setRequestHeader('X-App-Locale', strings.getLanguage());
                                        request.withCredentials = true;

                                        // let token = getCookie('XSRF-TOKEN');

                                        // if (!token) {
                                        //     await callCsrfToken();
                                        //     token = getCookie('XSRF-TOKEN');
                                        // }

                                        // request.setRequestHeader('X-XSRF-TOKEN', token ?? '');

                                        // Handle upload progress
                                        request.upload.onprogress = (e) => {
                                            const progress = Math.round((e.loaded / e.total) * 100);
                                            setValue('progress', progress);
                                        };

                                        // Handle response
                                        request.onload = () => {
                                            const data = JSON.parse(request.response);
                                            resolve({ status: request.status, data });
                                        };

                                        request.onerror = () => {
                                            reject(new Error('Network error occurred'));
                                        };

                                        request.send(formData);
                                    });
                                };

                                // Submit the procedure
                                const { status, data } = (await submitProcedure()) as any;

                                // console.log('submitProcedure', status, data);

                                // Handle different response statuses
                                if (status === 401) {
                                    setError('server', { message: data.message || 'Authentication error, please log in again' });
                                    return;
                                }

                                if (status === 426) {
                                    setShowUpgrade(true);
                                    return;
                                }

                                if (data.status !== '1') {
                                    setError('server', { message: data.message || 'Server error, please contact admin' });
                                    return;
                                }

                                // Handle file deletions if needed
                                if (clientProcedureId && values?.deletedIds?.length) {
                                    try {
                                        const response = await fetch(api.clientProcedureDeleteMobile.replace(':id', clientProcedureId), {
                                            method: 'POST',
                                            headers: {
                                                Accept: 'application/json',
                                                'Content-Type': 'application/json',
                                                'X-App-Locale': strings.getLanguage(),
                                            },
                                            credentials: 'include',
                                            body: JSON.stringify({ files: values?.deletedIds }),
                                        });

                                        const deleteData = await response.json();
                                        if (deleteData.status !== '1') {
                                            setError('server', { message: deleteData.message || 'Error deleting files' });
                                            return;
                                        }
                                    } catch (error) {
                                        setError('server', { message: 'Error deleting files' });
                                        return;
                                    }
                                }

                                // Success handling
                                toast.success(strings.client_treatment_saved_success);
                                mutateCounts();
                                swrMutate(api.clientTreatments(clientId));
                                swrMutate([api.clientTreatments(clientId), 20]);

                                // Handle sign or redirect
                                if (isSaveSign) {
                                    setSelectedProcedure(data.data);
                                    setOpenModal(true);
                                } else {
                                    setIsFormSuccess(true);
                                }
                            } catch (error) {
                                setError('server', { message: error instanceof Error ? error.message : 'An unexpected error occurred' });
                            }
                        })}
                    >
                        <ModalSuspense>
                            {openModal && (
                                <ClientProcedureSignModal
                                    openModal={openModal}
                                    onClose={async () => {
                                        await setOpenModal(false);
                                        await setSelectedProcedure(undefined);
                                        navigate(-1);
                                    }}
                                    mutate={mutate}
                                    clientMutate={clientMutate}
                                    selectedClientTreatment={selectedProcedure}
                                />
                            )}
                        </ModalSuspense>
                        <RouteLeavingGuard
                            when={isDirty && !showUpgrade && !isFormSuccess}
                            navigate={(location) => navigate(location.pathname)}
                            shouldBlockNavigation={() => isDirty && !showUpgrade && !isFormSuccess}
                            handleSave={() => {
                                formRef.current?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                            }}
                        />
                        <Modal
                            open={showUpgrade}
                            title={`${strings.Oops}!`}
                            handleClose={() => {
                                setShowUpgrade(false);
                            }}
                            submitButton={<Button onClick={() => navigate('/settings/upgrade-plan')}>{strings.UpgradePlan}</Button>}
                        >
                            <div className="p-4">
                                <h4 className="text-xl font-semibold">{strings.it_seems_plans_limit_reached}</h4>
                                <p>{strings.Click_below_button_to_upgrade_your_plan}</p>
                            </div>
                        </Modal>
                        {isSubmitting && (
                            <Modal open={isSubmitting} title={strings.Please_wait_while_we_save_your_record} hideCloseButton>
                                <div className="break-normal p-4">
                                    <LinearProgressWithLabel value={watch('progress') || 0} />
                                </div>
                            </Modal>
                        )}
                        <ClientProcedureCanvasHandler>
                            {({ addPage, changePage, deletePage, clear, undo, canvasRef, onDrawingChange, onChange, onShapeChange }) => (
                                <>
                                    <div className="flex flex-col space-y-4 xl:flex-row xl:space-x-4 xl:space-y-0">
                                        <Resizable
                                            className="relative min-w-full max-w-full xl:pr-3"
                                            defaultSize={{
                                                height: 'auto',
                                                width: width && width > 1280 ? (storedWidth ?? 'auto') : 'auto',
                                            }}
                                            maxWidth={width && width > 1280 ? '65%' : undefined}
                                            minWidth={width && width > 1280 ? '32%' : '100%'}
                                            handleStyles={{ right: { width: '4px' } }}
                                            onResizeStop={(event, direction, elementRef, delta) => {
                                                setStoredWidth(elementRef.style.width);
                                            }}
                                            handleComponent={{
                                                right: (
                                                    <div className="relative h-full w-full bg-transparent hover:bg-primary/80 active:bg-primary/80"></div>
                                                ),
                                            }}
                                            enable={{
                                                top: false,
                                                right: width && width > 1280 ? true : false,
                                                bottom: false,
                                                left: false,
                                                topRight: false,
                                                bottomRight: false,
                                                bottomLeft: false,
                                                topLeft: false,
                                            }}
                                        >
                                            <ClientProcedureCreateForm />
                                        </Resizable>

                                        <div className="grow">
                                            <ClientProcedureDrawingBoard
                                                onAddPage={addPage}
                                                onChangePage={changePage}
                                                onDeletePage={deletePage}
                                                onClear={clear}
                                                onUndo={undo}
                                                onChange={onChange}
                                                ref={canvasRef}
                                                onCircle={() => onShapeChange('circle')}
                                                onPen={() => onShapeChange('pen')}
                                                onPenFat={() => onShapeChange('pen', 4)}
                                                onCross={() => onShapeChange('cross')}
                                                onCrossFat={() => onShapeChange('cross', 4)}
                                                onFilledCircle={() => onShapeChange('filled_circle')}
                                                onGradientCircle={() => onShapeChange('gradient_circle')}
                                                onText={(text) => onShapeChange('text', 2, text ?? '1')}
                                                onCustomText={(val) => onShapeChange('customText', 2, '1', val)}
                                            />
                                        </div>
                                    </div>
                                    <ServerError className="mt-4 xl:col-span-2" error={errors?.server?.message} />
                                    <div className="mt-6 flex justify-end flex-wrap gap-4">
                                        <CancelButton
                                            onClick={async (e) => {
                                                e.preventDefault();
                                                await onDrawingChange();
                                                navigate(-1);
                                            }}
                                            type="button"
                                            className="dark:border-gray-500"
                                            disabled={isSubmitting}
                                            children={strings.Cancel}
                                        />
                                        <Button
                                            type="submit"
                                            disabled={isSubmitting}
                                            loading={!isSaveSign ? isSubmitting : false}
                                            children={strings.save_as_draft}
                                            onClick={async (e) => {
                                                e.preventDefault();
                                                flushSync(async () => {
                                                    await onChange();
                                                    await onDrawingChange();
                                                    formRef.current?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                                                });
                                            }}
                                        />
                                        <Button
                                            type="submit"
                                            disabled={isSubmitting}
                                            loading={isSaveSign ? isSubmitting : false}
                                            onClick={async (e) => {
                                                e.preventDefault();
                                                await flushSync(async () => {
                                                    await setIsSaveSign(true);
                                                    await onChange();
                                                    await onDrawingChange();
                                                    formRef.current?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                                                });
                                            }}
                                        >
                                            {strings.save_and_sign}
                                        </Button>
                                    </div>
                                </>
                            )}
                        </ClientProcedureCanvasHandler>
                    </form>
                </Card>
            </FormProvider>
        </>
    );
};

function validateClientTreatmentStore(values: IClientProcedureCreateValues, dateFormat: DateFormat): ResolverError<IClientProcedureCreateValues> {
    const resolver: ResolverError<IClientProcedureCreateValues> = { values, errors: {} };

    if (values.date && !dayjs(values.date, dateFormat, true).isValid()) {
        resolver.errors.date = {
            type: 'pattern',
            message: strings.formatString(strings.please_provide_valid_date_format_custom, dateFormat).toString(),
        };
    }

    const treatments: { id?: FieldError; actual_cost?: FieldError }[] = [];
    values.treatments.forEach((treatment) => {
        const localErrors: { id?: FieldError; actual_cost?: FieldError } = {};
        if (!treatment?.id) {
            localErrors.id = {
                type: 'required',
                message: strings.treatment_is_required,
            };
        }
        if (treatment?.actual_cost && Number.isNaN(Number(treatment?.actual_cost))) {
            localErrors.actual_cost = {
                type: 'valueAsNumber',
                message: strings.treatment_cost_must_be_number,
            };
        } else if (treatment?.actual_cost && parseFloat(treatment?.actual_cost.toString()) < 0) {
            localErrors.actual_cost = {
                type: 'minLength',
                message: strings.treatment_cost_must_be_greater_than_zero,
            };
        }

        treatments.push(localErrors);
    });

    let isTreatmentsEmpty = true;
    treatments.forEach((treatment) => {
        if (Object.keys(treatment).length !== 0) {
            isTreatmentsEmpty = false;
        }
    });

    if (!isTreatmentsEmpty) {
        resolver.errors.treatments = treatments;
    }

    return resolver;
}

const ClientProcedureCreateHoC: FC = () => {
    const { clientProcedureId }: { clientProcedureId?: string } = useParams();
    const { clientId } = useClient();

    const {
        data: procedureData,
        error: procedureError,
        isLoading,
        isValidating,
    } = useSWRImmutable<ClientTreatmentResponse>(!clientProcedureId ? null : api.clientTreatment({ clientId, procedureId: clientProcedureId }), {
        revalidateOnMount: true,
        revalidateIfStale: true,
        keepPreviousData: false,
        dedupingInterval: 0, // No deduplication, fetch every time
        refreshInterval: 0, // No auto-refresh
        shouldRetryOnError: false, // Prevent auto-retry
        fallbackData: undefined,
    });

    const hasData = !!procedureData && !isLoading && !isValidating;

    if (clientProcedureId && (isLoading || isValidating)) {
        return <SectionLoading />;
    }

    if (clientProcedureId && procedureError) {
        return <FullPageError code={procedureError?.status || 500} message={procedureError?.message || 'server error'} />;
    }

    return <ClientProcedureCreate procedureData={clientProcedureId && hasData ? procedureData : null} />;
};

export default ClientProcedureCreateHoC;
