import Badge from '@components/form/Badge';
import { ChipVarient } from '@components/form/Chip';
import IconButton from '@components/form/IconButton';
import useAuth from '@hooks/useAuth';
import { ReceiptItem } from '@interface/model/receipt_item';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import * as React from 'react';

export interface ClientProductListItemProps {
    product: ReceiptItem;
    onViewClick?: () => void;
}

const ClientProductListItem: React.FC<ClientProductListItemProps> = ({ product, onViewClick = () => {} }) => {
    const { withCurrency } = useAuth();
    const { renderDateTime } = useHours();

    return (
        <>
            <tr className={`alternate-tr-mobile md:hidden`}>{mobileListItem()}</tr>
            <tr className={`alternate-tr-desktop hidden md:table-row`}>{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>{product?.receipt?.viva_receipt_id ? `#${product?.receipt?.viva_receipt_id}` : ''}</Table.Td>
                <Table.Td>{product?.name}</Table.Td>
                <Table.Td>{renderDateTime(product?.created_at)}</Table.Td>
                <Table.Td>{product?.selling_price ? `${withCurrency(product?.selling_price ?? 0)}` : ''}</Table.Td>
                <Table.Td>{product?.quantity}</Table.Td>
                <Table.Td>{product?.total ? `${withCurrency(product?.total ?? 0)}` : ''}</Table.Td>
                <Table.Td>
                    <div className="flex items-center justify-start gap-2">
                        <Badge
                            varient={
                                cx(
                                    (product?.status || product?.receipt?.status) === 'PAID' && 'success',
                                    ((product?.status || product?.receipt?.status) === 'CANCELLED' ||
                                        (product?.status || product?.receipt?.status) === 'ABORTED') &&
                                        'error',
                                    (product?.status || product?.receipt?.status) === 'PROCESSING' && 'info',
                                    (product?.status === 'REFUNDED' || product?.status === 'PARTIALLY_REFUNDED') && 'warning',
                                    !product?.status &&
                                        (product?.receipt?.status === 'REFUNDED' || product?.receipt?.status === 'PARTIALLY_REFUNDED') &&
                                        'success',
                                ) as ChipVarient
                            }
                        >
                            {pos_strings.getString(
                                `receipt.status.${
                                    product?.status
                                        ? product?.status?.toLowerCase()
                                        : product?.receipt?.status === 'REFUNDED' || product?.receipt?.status === 'PARTIALLY_REFUNDED'
                                          ? 'paid'
                                          : product?.receipt?.status?.toLowerCase()
                                }`,
                            )}
                        </Badge>
                    </div>
                </Table.Td>
                <Table.Td className="p-2">
                    <div className="flex items-center justify-end space-x-1.5">
                        <IconButton onClick={onViewClick} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }

    function mobileListItem() {
        return (
            <Table.Td>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.order_id} :`}</span>
                    <span className="ml-1">{product?.receipt?.viva_receipt_id ? `#${product?.receipt?.viva_receipt_id}` : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.product.product_name} :`}</span>
                    <span className="ml-1">{product?.name}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.DateTime} :`}</span>
                    <span className="ml-1">{renderDateTime(product?.created_at)}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.price} :`}</span>
                    <span className="ml-1">{product?.selling_price ? `${withCurrency(product?.selling_price ?? 0)}` : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.qty} :`}</span>
                    <span className="ml-1">{product?.quantity}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.total_price} :`}</span>
                    <span className="ml-1">{product?.total ? `${withCurrency(product?.total ?? 0)}` : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.status} :`}</span>
                    <span className="ml-2 flex items-center justify-start gap-2">
                        <Badge
                            varient={
                                cx(
                                    (product?.status || product?.receipt?.status) === 'PAID' && 'success',
                                    ((product?.status || product?.receipt?.status) === 'CANCELLED' ||
                                        (product?.status || product?.receipt?.status) === 'ABORTED') &&
                                        'error',
                                    (product?.status || product?.receipt?.status) === 'PROCESSING' && 'info',
                                    (product?.status === 'REFUNDED' || product?.status === 'PARTIALLY_REFUNDED') && 'warning',
                                    !product?.status &&
                                        (product?.receipt?.status === 'REFUNDED' || product?.receipt?.status === 'PARTIALLY_REFUNDED') &&
                                        'success',
                                ) as ChipVarient
                            }
                        >
                            {pos_strings.getString(
                                `receipt.status.${
                                    product?.status
                                        ? product?.status?.toLowerCase()
                                        : product?.receipt?.status === 'REFUNDED' || product?.receipt?.status === 'PARTIALLY_REFUNDED'
                                          ? 'paid'
                                          : product?.receipt?.status?.toLowerCase()
                                }`,
                            )}
                        </Badge>
                    </span>
                </p>
                <IconButton className="-ml-2" onClick={onViewClick} name={strings.View}>
                    <EyeIcon />
                </IconButton>
            </Table.Td>
        );
    }
};

export default ClientProductListItem;
