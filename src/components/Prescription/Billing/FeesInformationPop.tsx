import IconButton from '@components/form/IconButton';
import CircleInfoIcon from '@icons/CircleInfo';
import strings from '@lang/Lang';

const FeesInformationPop = () => {
    return (
        <div>
            <div className="text-orange-500">
                {strings.monthly_fees_for_meridiq_are_automatically_deducted}
                <IconButton
                    className="ml-1 inline-block !p-0"
                    name={`${strings.formatString(strings.every_month_auto_invoice_will_be_paid, '40 SEK')}`}
                >
                    <CircleInfoIcon className="text-base" />
                </IconButton>
            </div>
        </div>
    );
};
export default FeesInformationPop;
