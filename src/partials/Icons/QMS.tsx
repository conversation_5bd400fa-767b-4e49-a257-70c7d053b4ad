import * as React from 'react';
import { SVGProps } from 'react';
const QMSIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="1em" height="1em" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_12560_58346)">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M19.6945 13.485C19.7455 13.4493 19.7834 13.398 19.8026 13.3388C19.8218 13.2796 19.8212 13.2157 19.8009 13.1569L19.1291 10.9741C19.0309 10.6652 19.0309 10.3335 19.1291 10.0246L19.8009 7.84176C19.8212 7.78291 19.8217 7.71905 19.8026 7.65983C19.7834 7.60062 19.7454 7.54924 19.6945 7.51346L17.8677 6.14215C17.6065 5.95053 17.4116 5.68216 17.3103 5.37448L16.5715 3.21496C16.5535 3.15521 16.5163 3.10304 16.4657 3.06652C16.4151 3.03 16.3539 3.01116 16.2915 3.01289L14.0079 2.97766C13.6843 2.97477 13.3694 2.87255 13.1058 2.68482L11.2373 1.36904C11.1878 1.33105 11.1272 1.31045 11.0648 1.31045C11.0025 1.31045 10.9418 1.33105 10.8924 1.36904L9.02384 2.68482C8.76015 2.87253 8.44519 2.97475 8.12153 2.97766L5.83788 3.01289C5.77548 3.01108 5.71422 3.02989 5.6636 3.06643C5.61298 3.10296 5.57582 3.15516 5.55788 3.21496L4.8182 5.37448C4.71684 5.68216 4.52196 5.95053 4.26076 6.14215L2.43515 7.51323C2.3842 7.549 2.34625 7.60036 2.32701 7.65957C2.30778 7.71879 2.30831 7.78264 2.32851 7.84153L3.00052 10.0244C3.09876 10.3332 3.09876 10.665 3.00052 10.9738L2.32851 13.1567C2.30833 13.2155 2.30781 13.2793 2.32704 13.3385C2.34627 13.3977 2.38421 13.449 2.43515 13.4847L4.26193 14.8561C4.52309 15.0477 4.71795 15.3161 4.81937 15.6237L5.55904 17.7832C5.57637 17.8434 5.61337 17.8961 5.66413 17.9327C5.71489 17.9694 5.77647 17.988 5.83904 17.9856L8.12293 18.0236C8.44659 18.0241 8.76185 18.1266 9.02384 18.3167L10.8924 19.6292C10.9422 19.6663 11.0027 19.6864 11.0648 19.6864C11.127 19.6864 11.1874 19.6663 11.2373 19.6292L13.1058 18.3167C13.3681 18.1264 13.6838 18.0238 14.0079 18.0236L16.2915 17.9856C16.3541 17.988 16.4157 17.9694 16.4664 17.9327C16.5172 17.8961 16.5542 17.8434 16.5715 17.7832L17.3112 15.6237C17.4126 15.3161 17.6075 15.0477 17.8686 14.8561L19.6945 13.485ZM21.0553 12.77L20.3835 10.5872C20.3632 10.5304 20.3632 10.4683 20.3835 10.4115L21.0553 8.2284C21.1555 7.91137 21.1542 7.57096 21.0515 7.25473C20.9488 6.93849 20.7498 6.66226 20.4825 6.46462L18.6557 5.09331C18.6052 5.05921 18.5685 5.00826 18.5521 4.94957L17.8124 2.79005C17.7067 2.47494 17.5054 2.20062 17.2365 2.00527C16.9676 1.80992 16.6444 1.70326 16.3121 1.70014L14.0282 1.66491C13.9676 1.66606 13.9084 1.64647 13.8604 1.60938L11.9921 0.296633C11.7214 0.103694 11.3972 0 11.0648 0C10.7324 0 10.4083 0.103694 10.1375 0.296633L8.269 1.60984C8.22104 1.64693 8.16185 1.66653 8.10123 1.66538L5.81758 1.70061C5.48521 1.70373 5.16209 1.81039 4.89318 2.00573C4.62427 2.20108 4.42295 2.4754 4.31723 2.79052L3.57756 4.95004C3.56118 5.00873 3.52446 5.05968 3.47396 5.09377L1.64717 6.46462C1.37981 6.66227 1.18086 6.9385 1.07812 7.25472C0.975377 7.57093 0.973973 7.91135 1.0741 8.2284L1.74611 10.4115C1.76618 10.4683 1.76618 10.5303 1.74611 10.5872L1.0741 12.77C0.973955 13.0871 0.975349 13.4276 1.07809 13.7438C1.18083 14.0601 1.37979 14.3364 1.64717 14.5341L3.47396 15.9054C3.52441 15.9394 3.56113 15.9903 3.57756 16.0489L4.31723 18.2084C4.42188 18.5242 4.62293 18.7992 4.89208 18.9947C5.16123 19.1902 5.48491 19.2964 5.81758 19.2983L8.10123 19.3335C8.16162 19.3341 8.22031 19.3536 8.269 19.3893L10.1375 20.7032C10.4082 20.8962 10.7324 21 11.0648 21C11.3973 21 11.7214 20.8962 11.9921 20.7032L13.8604 19.39C13.9092 19.3543 13.968 19.3347 14.0284 19.3342L16.3121 19.299C16.6447 19.2971 16.9684 19.1909 17.2376 18.9954C17.5067 18.7999 17.7078 18.5249 17.8124 18.2091L18.5521 16.0496C18.5685 15.991 18.6052 15.9401 18.6557 15.9061L20.4825 14.5348C20.7499 14.3371 20.9488 14.0608 21.0515 13.7445C21.1542 13.4283 21.1556 13.0871 21.0553 12.77Z"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth={0.1}
            />
            <path d="M7 10.5L9.52632 13L14.5 8.5" stroke="currentColor" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round" />
        </g>
        <defs>
            <clipPath id="clip0_12560_58346">
                <rect width={22} height={22} fill="currentColor" />
            </clipPath>
        </defs>
    </svg>
);
export default QMSIcon;
