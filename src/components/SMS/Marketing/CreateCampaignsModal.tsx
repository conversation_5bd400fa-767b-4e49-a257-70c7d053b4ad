import BookingNoteIcon from '@icons/BookingNote';
import strings from '@lang/Lang';
import BirthdayCakeIcon from '@partials/Icons/BirthdayCake';
import SettingsIcon from '@partials/Icons/Setting';
import UserAddIcon from '@partials/Icons/UserAdd';
import Modal from '@partials/MaterialModal/Modal';
import { useSMSMarketing } from '@provider/SMSMarketingProvider';
import React from 'react';

export interface CreateCampaignsModalProps {
    openModal: boolean;
    handleClose: () => void;
}
const CreateCampaignsModal: React.FC<CreateCampaignsModalProps> = ({ openModal, handleClose }) => {
    const { setFilterCondition, formik, setDetailsModal, setOpenCampaignsModal } = useSMSMarketing();
    const { setFieldValue } = formik;
    const handleSubmit = () => {
        setOpenCampaignsModal(false);
        setDetailsModal(true);
    };
    return (
        <Modal open={openModal} handleClose={handleClose}>
            <div className="space-y-2.5 pt-4 text-center">
                <p className="text-center text-lg font-semibold">{strings.select_template}</p>
                <p>{strings.select_the_type_of_message_you_want_to_create_campaign}</p>
            </div>
            <div className="space-y-5 p-4 pt-6">
                <div className="flex gap-4">
                    <SettingsIcon className="text-2xl text-primary dark:text-primaryLight" />
                    <div
                        className="cursor-pointer"
                        onClick={() => {
                            setFieldValue('client_type', 'custom_segments');
                            setFieldValue('message_name', '');
                            handleSubmit();
                            setFilterCondition([
                                {
                                    type: undefined,
                                    key: '',
                                    value: '',
                                },
                            ]);
                        }}
                    >
                        <p className="font-medium">{strings.custom}</p>
                        <p className="text-sm">{strings.custom_message_that_you_configure_yourself}</p>
                    </div>
                </div>
                <hr />
                <div className="space-y-5">
                    <p className="font-semibold text-gray-500">{strings.select_template}</p>
                    {/* <div className="flex gap-3">
                        <RightFillIcon className="text-3xl text-primary dark:text-primaryLight" />
                        <div className="cursor-pointer" onClick={() => {}}>
                            <p className="font-medium">{strings.follow_up_message}</p>
                            <p className="text-sm">{strings.follow_up_message_sent_some_time_after_the_appointment_is_held}</p>
                        </div>
                    </div> */}
                    <div className="flex gap-3">
                        <UserAddIcon className="text-3xl text-primary dark:text-primaryLight" />
                        <div
                            className="cursor-pointer"
                            onClick={() => {
                                setFieldValue('client_type', 'new_clients');
                                setFieldValue('message_name', 'New client');
                                setFilterCondition([
                                    {
                                        type: 'joined',
                                        key: `${strings.day}s`,
                                        value: '30',
                                    },
                                ]);
                                handleSubmit();
                            }}
                        >
                            <p className="font-medium">{strings.new_clients}</p>
                            <p className="text-sm">{strings.send_message_new_clients_join_in_last_thirty_days}</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <BirthdayCakeIcon className="text-3xl text-primary dark:text-primaryLight" />
                        <div
                            className="cursor-pointer"
                            onClick={() => {
                                setFieldValue('client_type', 'birthday');
                                setFieldValue('message_name', 'Birthday offer');
                                handleSubmit();
                                setFilterCondition([
                                    {
                                        type: 'upcoming_birthday',
                                        key: strings.day,
                                        value: `1`,
                                    },
                                ]);
                            }}
                        >
                            <p className="font-medium">{strings.birthday_offer_message}</p>
                            <p className="text-sm">{strings.birthday_offer_message_sent_before_hours_of_client_birthday}</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <BookingNoteIcon className="text-3xl text-primary dark:text-primaryLight" />
                        <div
                            className="cursor-pointer"
                            onClick={() => {
                                setFieldValue('client_type', 'custom_segments');
                                setFieldValue('message_name', 'Past Booking');
                                handleSubmit();
                                setFilterCondition([
                                    {
                                        type: 'booking',
                                        key: `${strings.month}s`,
                                        value: '3',
                                    },
                                ]);
                            }}
                        >
                            <p className="font-medium">{strings.booking_service}</p>
                            <p className="text-sm">{strings.booking_service_note}</p>
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};
export default CreateCampaignsModal;
