import Button from '@components/form/Button';
import { Formik } from 'formik';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import SignaturePad from 'signature_pad';
import api from '@configs/api';
import strings from '@lang/Lang';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import MaterialSignturePad from '@partials/SignaturePad/MaterialSignaturePad';
import { convertBase64ToFile, downloadFile, regenPDF } from '@/helper';
import { ClientQuestionary } from '@interface/model/clientQuestionary';
import IconButton from '@components/form/IconButton';
import EyeIcon from '@partials/Icons/Eye';
import { lazy, useMemo, useState } from 'react';
import LoadingIcon from '@icons/Loading';
import DownloadIcon from '../BeforeAfterImage/components/icons/Download';
import { File } from '@interface/model/File';
import ModalSuspense from '@partials/Loadings/ModalLoading';
const FileViewModal = lazy(() => import('@components/File/FileViewModal'));

export interface ClientQuestionarySignModalProps {
    questionary: ClientQuestionary;
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const ClientQuestionaryFileListModal: React.FC<ClientQuestionarySignModalProps> = ({ questionary, openModal, setOpenModal }) => {
    const handleModelClose = async () => {
        setOpenModal(false);
    };

    const [downloading, setDownloading] = useState<string | undefined>();

    const [viewFile, setViewFile] = useState<[File, string]>();
    const [viewFileModalOpen, setViewFileModalOpen] = useState<boolean>(false);

    const actualNameMap = useMemo(() => {
        const m = new Map();
        questionary.questions?.map((question, index) => {
            if (question.type === 'file_upload') {
                questionary.formatted_response.at(index)?.files?.map((f) => {
                    m.set(f.file_id, f.file_name);
                });
            }
        });
        return m;
    }, [questionary.id]);

    return (
        <>
            <ModalSuspense>
                {viewFileModalOpen && viewFile?.at(0) && (
                    <FileViewModal
                        openModal={viewFileModalOpen}
                        onClose={() => {
                            setViewFile(undefined);
                            setViewFileModalOpen(false);
                        }}
                        file={viewFile[0]}
                        file_name={viewFile[1]}
                    />
                )}
            </ModalSuspense>
            <Modal open={viewFileModalOpen ? false : openModal} title={strings.files} handleClose={handleModelClose} closeOnBackdropClick={false}>
                <div className="p-4">
                    <div className="divide-y dark:divide-gray-800">
                        {questionary.files?.map((file, i) => {
                            const actual_name = actualNameMap.get(file.id) ?? file.filename;

                            const amIdownloading = downloading === `download_${file.id}`;
                            return (
                                <div key={i} className="flex w-full gap-x-3 py-2">
                                    <p className="flex-grow break-all">{actual_name}</p>
                                    <div className="flex">
                                        <IconButton
                                            className="focus:outline-none"
                                            onClick={() => {
                                                setViewFile([file, actual_name]);
                                                setViewFileModalOpen(true);
                                            }}
                                        >
                                            <EyeIcon />
                                        </IconButton>
                                        <IconButton className="focus:outline-none" onClick={() => onDownloadClick(file, actual_name)}>
                                            {amIdownloading ? <LoadingIcon /> : <DownloadIcon />}
                                        </IconButton>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </Modal>
        </>
    );

    async function onDownloadClick(file: File, file_name: string) {
        setDownloading(`download_${file.id}`);

        const response = await fetch(file.url);

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);

        a.target = '_blank';

        a.setAttribute('download', file_name);
        a.click();

        setDownloading(undefined);
    }
};

export default ClientQuestionaryFileListModal;
