import Button from '@components/form/Button';
import IconButton from '@components/form/IconButton';
import InfoCard from '@components/form/InfoCard';
import Input from '@components/form/Input';
import Age from '@components/form/Marketing/Age';
import Birthday from '@components/form/Marketing/Birthday';
import Joined from '@components/form/Marketing/Joined';
import Services from '@components/form/Marketing/Services';
import Select from '@components/form/Select';
import api from '@configs/api';
import useDebounce from '@hooks/useDebounce';
import { MarketingCreditCountResponse } from '@interface/common';
import strings from '@lang/Lang';
import AddRoundIcon from '@partials/Icons/AddRound';
import DeleteIcon from '@partials/Icons/Delete';
import Modal from '@partials/MaterialModal/Modal';
import { ConditionType, TargetType, useSMSMarketing } from '@provider/SMSMarketingProvider';
import cx from 'classix';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import dayjs, { ManipulateType } from 'dayjs';
import React from 'react';
import useSWR from 'swr';
export interface SMSDetailsModalProps {
    openModal: boolean;
    handleClose: () => void;
}
dayjs.extend(isSameOrBefore);

const SMSDetailsModal: React.FC<SMSDetailsModalProps> = ({ openModal, handleClose }) => {
    const client_filter = [
        {
            id: 1,
            name: strings.AllClients,
            key: 'all_clients',
        },
        {
            id: 2,
            name: strings.new_clients,
            key: 'new_clients',
        },
        {
            id: 3,
            name: strings.upcoming_birthday,
            key: 'birthday',
        },
        {
            id: 4,
            name: strings.custom_segments,
            key: 'custom_segments',
        },
    ];

    const defualtValue = {
        type: undefined,
        value: '',
        key: '',
    };
    const client_condition = [
        {
            name: strings.target_client_who_joined,
            type: 'joined',
        },
        {
            name: strings.age,
            type: 'age',
        },
        {
            name: strings.birthday,
            type: 'upcoming_birthday',
        },
        {
            name: strings.target_clients_who_last_booked + ' ' + strings.service.toLowerCase(),
            type: 'booking',
        },
    ];
    const { formik, setDetailsModal, filterCondition, setFilterCondition, setOpenModal, setOpenClientListModal } = useSMSMarketing();
    const [selectError, setSelectError] = React.useState(false);
    const { values, setFieldValue, handleSubmit, errors, touched } = formik ?? {};
    const debouncedFilterCondition = useDebounce(filterCondition, 1000);
    const debouncedValues = useDebounce(values, 1000);

    const { data: clientCount, isLoading } = useSWR<MarketingCreditCountResponse, Error>(
        () => {
            const hasErrors =
                debouncedFilterCondition.some((v) => v.error) ||
                !values.message_name ||
                (values.client_type === 'custom_segments' && !debouncedFilterCondition.filter((v) => v.type).length);
            if (hasErrors) {
                return null;
            }

            const filteredCondition = debouncedFilterCondition.filter((v) => v.type);

            return api.sms.marketing.clientListCount(true, filteredCondition, debouncedValues.service);
        },
        {
            revalidateOnFocus: false,
            refreshInterval: 0,
            errorRetryCount: 0,
        },
    );
    function handleYearBefore(number: string, value: string) {
        const today = dayjs();
        const futureDate = today.add(parseInt(number), value as ManipulateType);
        const oneYearAgo = today.add(1, 'year');
        const isAfterOneYear = futureDate.isAfter(oneYearAgo);
        return isAfterOneYear;
    }
    return (
        <Modal open={openModal} handleClose={handleClose} size="large">
            <div className="space-y-4 p-4">
                <div className="space-y-3 text-center">
                    <h3 className="text-2xl font-bold">{strings.more_details}</h3>
                    <p className="">{strings.add_message_name_and_choose_clients_to_send_the_message}</p>
                </div>
                <Input
                    label={strings.name_for_this_message}
                    value={values.message_name}
                    placeholder={strings.offer_message}
                    onChange={(e) => {
                        if (e.target.value.length && e.target.value.length > 99) return;
                        setFieldValue('message_name', e.target.value);
                    }}
                    required
                    error={touched?.message_name && errors?.message_name}
                />
                <h3 className="text-xl font-bold">{strings.choose_clients}</h3>
                <div className="grid grid-cols-1 gap-2 md:grid-cols-3 md:gap-4">
                    <div className="w-full">
                        <Select
                            label={strings.target_type}
                            displayValue={(val) => val}
                            value={client_filter.find((item) => item.key === values.client_type)?.name || ''}
                            placeholder={strings.Select}
                            required
                            onChange={(val) => {
                                const value = val as TargetType;
                                if (value === 'all_clients') {
                                    setFilterCondition([]);
                                }
                                setFieldValue('client_type', value);
                                if (value === 'new_clients') {
                                    setFilterCondition([
                                        {
                                            type: 'joined',
                                            key: strings.day,
                                            value: '1',
                                        },
                                    ]);
                                }
                                if (value === 'birthday') {
                                    setFilterCondition([
                                        {
                                            type: 'upcoming_birthday',
                                            key: strings.day,
                                            value: `1`,
                                        },
                                    ]);
                                }
                                if (value === 'custom_segments') {
                                    setFilterCondition([defualtValue]);
                                }
                            }}
                        >
                            {client_filter.map((filter, index) => (
                                <Select.Option key={index} value={filter.key}>
                                    {filter.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </div>
                    <div className={`col-span-2 w-full ${values.client_type === 'all_clients' ? '' : ''}`}>
                        <p className={`text-sm font-normal text-gray-700 dark:text-gray-400`}>{strings.condition}</p>
                        {values.client_type === 'all_clients' ? (
                            <p className="rounded-lg border-grayishBlue bg-grayishBlue px-3 py-[11px] dark:bg-darkGray dark:text-white">
                                {strings.target_every_client_in_the_database}
                            </p>
                        ) : (
                            <div className="space-y-3">
                                {filterCondition?.map((item, index) => {
                                    function onDelete(type: ConditionType) {
                                        if (filterCondition.length === 1) {
                                            setFilterCondition([defualtValue]);
                                            setFieldValue('service', []);
                                            return;
                                        }
                                        setFilterCondition([...filterCondition.filter((v, i) => i !== index)]);
                                        if (type === 'booking') setFieldValue('service', []);
                                    }
                                    function onChangeValue(type: ConditionType, value: string) {
                                        const timeUnits = `(${strings.day}|${strings.day}s|${strings.week.toLowerCase()}|${strings.week.toLowerCase()}s|${strings.month}|${strings.month}s|${strings.year}|${strings.year}s)`;
                                        const pattern = new RegExp(`^(\\d+) ${timeUnits}?$`);
                                        const match = value.split(' ');
                                        const checkYearBefore =
                                            type === 'upcoming_birthday' && handleYearBefore(match?.at(0) || '', match?.at(1) || '');

                                        if (pattern.test(value) && match?.at(0) && match?.at(1)) {
                                            setFilterCondition([
                                                ...filterCondition.map((v, i) =>
                                                    i === index
                                                        ? {
                                                              type: type,
                                                              key: match?.at(1) || '',
                                                              value: match?.at(0) || '',
                                                              error: checkYearBefore
                                                                  ? strings.the_selected_time_frame_must_be_within_the_next_a_year
                                                                  : '',
                                                          }
                                                        : v,
                                                ),
                                            ]);
                                        } else {
                                            setFilterCondition([
                                                ...filterCondition.map((v, i) =>
                                                    i === index
                                                        ? {
                                                              type: type,
                                                              key: match?.at(1) || '',
                                                              value: match?.at(0) || '',
                                                              error:
                                                                  type === 'upcoming_birthday'
                                                                      ? `${strings.formatString(strings.invalid_format_use, `(${strings.day}|${strings.week.toLowerCase()}|${strings.month})`)}`
                                                                      : `${strings.formatString(strings.invalid_format_use, `(${strings.day}|${strings.week.toLowerCase()}|${strings.month}|${strings.year})`)}`,
                                                          }
                                                        : v,
                                                ),
                                            ]);
                                        }
                                    }
                                    return (
                                        <div key={index} className={`flex items-center gap-1.5 md:gap-3`}>
                                            {!item.type && (
                                                <div className="w-full">
                                                    <Select
                                                        value={client_condition.find((val) => val.type === item.type)?.name}
                                                        onChange={(val) => {
                                                            setSelectError(false);
                                                            setFilterCondition([
                                                                ...filterCondition.map((v, i) =>
                                                                    i === index
                                                                        ? {
                                                                              type: val as ConditionType,
                                                                              key: val === 'age' ? strings.above.toLowerCase() : strings.day,
                                                                              value: val === 'age' ? '15' : '1',
                                                                          }
                                                                        : v,
                                                                ),
                                                            ]);
                                                        }}
                                                        disabled={!values.client_type}
                                                        placeholder={strings.select_option}
                                                        displayValue={(val) => val}
                                                        error={selectError && strings.please_select_one_option}
                                                    >
                                                        {client_condition
                                                            .filter((typeItem) => {
                                                                return !filterCondition.some((filterItem) => filterItem.type === typeItem.type);
                                                            })
                                                            .map((item, index) => (
                                                                <Select.Option key={index} value={item.type}>
                                                                    {item.name}
                                                                </Select.Option>
                                                            ))}
                                                    </Select>
                                                </div>
                                            )}
                                            {item.type === 'joined' && (
                                                <Joined
                                                    onChange={(e) => {
                                                        onChangeValue(item.type, e.trim());
                                                    }}
                                                    value={item.value + ' ' + item.key}
                                                    error={item.error}
                                                />
                                            )}
                                            {item.type === 'age' && (
                                                <Age
                                                    onChange={(e) => {
                                                        const list = [
                                                            ...filterCondition.map((v, i) => (i === index ? { ...v, type: item.type, key: e } : v)),
                                                        ];
                                                        setFilterCondition(list);
                                                    }}
                                                    onChangeInput={(e) => {
                                                        if (e && parseInt(e) > 99) return;
                                                        const list = [
                                                            ...filterCondition.map((v, i) =>
                                                                i === index
                                                                    ? {
                                                                          ...v,
                                                                          type: item.type,
                                                                          value: e,
                                                                          error: !e ? strings.number_is_required : '',
                                                                      }
                                                                    : v,
                                                            ),
                                                        ];
                                                        setFilterCondition(list);
                                                    }}
                                                    value={item.key}
                                                    inputValue={item.value}
                                                    error={item.error}
                                                />
                                            )}
                                            {item.type === 'upcoming_birthday' && (
                                                <Birthday
                                                    onChange={(e) => {
                                                        onChangeValue(item.type, e.trim());
                                                    }}
                                                    value={item.value + ' ' + item.key}
                                                    error={item.error}
                                                />
                                            )}
                                            {item.type === 'booking' && (
                                                <Services
                                                    onChange={(e) => {
                                                        onChangeValue(item.type, e.trim());
                                                    }}
                                                    value={item.value + ' ' + item.key}
                                                    onServiceChange={(list) => {
                                                        setFieldValue('service', list);
                                                    }}
                                                    servicesValue={values.service}
                                                    error={item.error}
                                                />
                                            )}
                                            {(((values.client_type !== 'new_clients' || index !== 0) &&
                                                (values.client_type !== 'birthday' || index !== 0) &&
                                                !!filterCondition.filter((v) => v.type).length) ||
                                                (values?.client_type === 'custom_segments' && index !== 0)) && (
                                                <IconButton onClick={() => onDelete(item.type)}>
                                                    <DeleteIcon />
                                                </IconButton>
                                            )}
                                        </div>
                                    );
                                })}
                                {filterCondition.length < 4 && (
                                    <IconButton
                                        className="-ml-2"
                                        disabled={!values.client_type}
                                        onClick={() => {
                                            setFilterCondition((d) => [...d, defualtValue]);
                                        }}
                                    >
                                        <AddRoundIcon />
                                    </IconButton>
                                )}
                            </div>
                        )}
                    </div>
                </div>
                {/* <div className="rounded bg-lightPurple p-4 text-primary dark:bg-primaryLight/20 dark:text-primaryLight">
                    <p>{strings.summary}</p>
                    <p>{strings.the_message_will_be_sent_to_all_client_from_database}</p>
                </div> */}

                <div className="mt-4">
                    <InfoCard message={clientCount?.data.toString() === '0' && strings.no_client_found} className="max-w-max" />
                </div>
                {mobileButton()}
                <div className={cx('hidden justify-between md:flex md:flex-wrap', values.client_type === 'all_clients' ? 'pt-20' : 'pt-8')}>
                    <Button variant="ghost" size="big" onClick={handleClose}>
                        {strings.back}
                    </Button>
                    <div className="grid grid-cols-2 items-center gap-4">
                        <p
                            className="cursor-pointer p-2 text-primary underline dark:text-primaryLight"
                            onClick={() => {
                                if (!clientCount?.data) return;
                                if (clientCount?.data.toString() === '0') return;
                                setDetailsModal(false);
                                setOpenClientListModal(true);
                            }}
                        >
                            {strings.number_of_client}: {clientCount?.data || 0}
                        </p>
                        <Button
                            loading={isLoading}
                            type="submit"
                            onClick={async () => {
                                if (!values.client_type || !values.message_name) {
                                    handleSubmit();
                                } else if (values.client_type !== 'all_clients' && filterCondition.filter((v) => v.type).length === 0) {
                                    setSelectError(true);
                                } else {
                                    await setDetailsModal(false);
                                    await setOpenModal(true);
                                }
                            }}
                            disabled={!!filterCondition.find((v) => v.error) || clientCount?.data.toString() === '0'}
                        >
                            {strings.Next}
                        </Button>
                    </div>
                </div>
            </div>
        </Modal>
    );
    function mobileButton() {
        return (
            <div className="block space-y-2 md:hidden">
                <p
                    className="cursor-pointer p-2 text-primary underline dark:text-primaryLight"
                    onClick={() => {
                        if (!clientCount?.data) return;
                        if (clientCount?.data.toString() === '0') return;
                        setDetailsModal(false);
                        setOpenClientListModal(true);
                    }}
                >
                    {strings.number_of_client}: {clientCount?.data || 0}
                </p>
                <div className="grid grid-cols-2 items-center gap-4">
                    <Button variant="ghost" size="big" onClick={handleClose}>
                        {strings.back}
                    </Button>
                    <Button
                        loading={isLoading}
                        type="submit"
                        onClick={async () => {
                            if (!values.client_type || !values.message_name) {
                                handleSubmit();
                            } else if (values.client_type !== 'all_clients' && filterCondition.filter((v) => v.type).length === 0) {
                                setSelectError(true);
                            } else {
                                await setDetailsModal(false);
                                await setOpenModal(true);
                            }
                        }}
                        disabled={!!filterCondition.find((v) => v.error) || clientCount?.data.toString() === '0'}
                    >
                        {strings.Next}
                    </Button>
                </div>
            </div>
        );
    }
};
export default SMSDetailsModal;
