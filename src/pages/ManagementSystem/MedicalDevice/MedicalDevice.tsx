import MedicalDeviceListItem from '@pages/ManagementSystem/MedicalDevice/MedicalDeviceListItem';
import Skeleton from '@components/Skeleton/Skeleton';
import Pagination from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import { MedicalDeviceManagement } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import Card from '@partials/Paper/PagePaper';
import Table from '@partials/Table/PageTable';
import * as React from 'react';
import api from '../../../configs/api';
import { usePaginationSWR } from '../../../hooks/usePaginationSWR';
import { MedicalDevicePaginatedResponse } from '../../../interfaces/common';
import { useNavigate } from 'react-router';
import Select from '@components/form/Select';
import { FilterType } from '@hooks/useCursorPaginationSWR';
import AddButton from '@components/form/AddButton';
import { type ZipDownloadData } from '../DocumentReports/ZipDownloadPopup';
import ExportIcon from '@icons/Export';
import Button from '@components/form/Button';
import { toast } from 'react-toastify';

export interface MedicalDeviceProps {}

const ZipDownloadPopup = React.lazy(() => import('../DocumentReports/ZipDownloadPopup'));
const MedicalDeviceDeleteModal = React.lazy(() => import('@pages/ManagementSystem/MedicalDevice/MedicalDeviceDeleteModal'));
const MedicalDeviceViewModal = React.lazy(() => import('@pages/ManagementSystem/MedicalDevice/MedicalDeviceViewModal'));
const MedicalDeviceModal = React.lazy(() => import('@pages/ManagementSystem/MedicalDevice/MedicalDeviceModal'));

const statusFilter = () => [
    {
        text: strings.All,
        key: 'all',
        filter: 'withTrashed',
        filterType: '=',
        filterValue: null,
    },
    {
        text: strings.Active,
        key: 'active',
        filter: 'deleted_at',
        filterType: '=',
        filterValue: null,
    },
    {
        text: strings.InActive,
        key: 'inactive',
        filter: 'onlyTrashed',
        filterType: '!=',
        filterValue: null,
    },
];

const MedicalDevice: React.FC<MedicalDeviceProps> = () => {
    const [openModal, setOpenModal] = React.useState(false);
    const [viewOpen, setViewOpen] = React.useState(false);
    const [deleteOpen, setDeleteOpen] = React.useState(false);
    const navigate = useNavigate();

    const { data, mutate, page, orderBy, setPage, loading, orderDirection, handleOrder, filterData, filter, filterType, filterValue } =
        usePaginationSWR<MedicalDevicePaginatedResponse, Error>(api.management_device.list, {
            filter: 'deleted_at',
            filterType: '=',
            filterValue: null,
        });

    const [selectedMedicalDevice, setSelectedMedicalDevice] = React.useState<MedicalDeviceManagement>();
    const [download, setDownload] = React.useState<ZipDownloadData>();
    const [downloadModalOpen, setDownloadModalOpen] = React.useState(false);
    const [downloadLoading, setDownloadLoading] = React.useState(false);

    return (
        <div>
            <ModalSuspense>
                {openModal && (
                    <MedicalDeviceModal
                        openModal={openModal}
                        setOpenModal={setOpenModal}
                        mutate={mutate}
                        selectedMedicalDevice={selectedMedicalDevice}
                    />
                )}
                {deleteOpen && (
                    <MedicalDeviceDeleteModal
                        open={deleteOpen}
                        handleClose={() => {
                            setDeleteOpen(false);
                        }}
                        mutate={mutate}
                        selectedDevice={selectedMedicalDevice}
                    />
                )}
                {viewOpen && selectedMedicalDevice && (
                    <MedicalDeviceViewModal openModal={viewOpen} setOpenModal={setViewOpen} device={selectedMedicalDevice} />
                )}
                {downloadModalOpen && (
                    <ZipDownloadPopup openModal={downloadModalOpen} setOpenModal={setDownloadModalOpen} data={download} refetch={onDownloadClick} />
                )}
            </ModalSuspense>
            <Card>
                <div className="mb-4 flex flex-col items-start justify-between gap-2 lg:flex-row lg:items-center">
                    <Heading text={strings.medical_device} />
                    <div className="flex items-center space-x-4">
                        <Button size="normal" onClick={() => onDownloadClick()} loading={downloadLoading}>
                            <ExportIcon className="mr-2" />
                            <p>{strings.export}</p>
                        </Button>
                        <div className="min-w-24">
                            <Select
                                value={
                                    statusFilter().find(
                                        (oFilter) =>
                                            oFilter.filter === filter && oFilter.filterType === filterType && oFilter.filterValue === filterValue,
                                    )?.key
                                }
                                onChange={(val) => {
                                    const selectedFilter = statusFilter().find((oFilter) => oFilter.key === val);
                                    if (selectedFilter) {
                                        filterData(selectedFilter.filter, selectedFilter.filterType as FilterType, selectedFilter.filterValue);
                                    }
                                }}
                                displayValue={(val) => statusFilter().find((oFilter) => oFilter.key === val)?.text}
                            >
                                {statusFilter().map((filter) => (
                                    <Select.Option value={filter.key} key={`status_filter_${filter.key}`}>
                                        {filter.text}
                                    </Select.Option>
                                ))}
                            </Select>
                        </div>
                        <AddButton
                            size="normal"
                            onClick={() => {
                                setSelectedMedicalDevice(undefined);
                                setOpenModal(true);
                            }}
                        />
                    </div>
                </div>

                <Table>
                    <Table.Head>
                        <Table.ThSort
                            sort={orderBy === 'product_name' && orderDirection}
                            onClick={() => handleOrder('product_name')}
                            children={strings.device_name}
                        />
                        <Table.Th children={strings.added_on} />
                        <Table.Th children={strings.maintenance_doc} />
                        <Table.Th children={strings.Actions} className="text-right" />
                    </Table.Head>
                    <Table.Body>
                        {data?.data?.map((medical) => (
                            <MedicalDeviceListItem
                                key={medical.id}
                                onEditClick={() => {
                                    setSelectedMedicalDevice(medical);
                                    setOpenModal(true);
                                }}
                                onDeleteClick={() => {
                                    setSelectedMedicalDevice(medical);
                                    setDeleteOpen(true);
                                }}
                                onViewClick={() => {
                                    setSelectedMedicalDevice(medical);
                                    setViewOpen(true);
                                }}
                                onMaintenanceClick={() => {
                                    navigate(`/management-system/medical-device/${medical?.id}/maintenance`);
                                }}
                                medicalDevice={medical}
                            />
                        ))}
                        {loading ? <MedicalDeviceSkeleton limit={10} /> : <></>}
                        {!loading && !data?.data.length && <EmptyData cardHeight="h-[65vh]" />}
                    </Table.Body>
                </Table>
                <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />
            </Card>
        </div>
    );

    async function onDownloadClick(force: boolean = false) {
        setDownloadLoading(true);
        const response = await fetch(api.management_device.download(force), {
            method: 'POST',
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                'X-App-Locale': strings.getLanguage(),
            },
            credentials: 'include',
        });
        const json = await response.json();
        setDownloadLoading(false);

        // If ZIP file exists in s3
        if (json?.data) {
            setDownload({ id: -1, message: json.message, file: json.data });
            setDownloadModalOpen(true);
            return;
        }
        // ZIP generating or in queue
        if (response.status === 208) {
            setDownload({ id: -1, message: json.message });
            setDownloadModalOpen(true);
            return;
        }
        // ZIP request added in queue
        if (response.status === 200) {
            setDownload({ id: -1, message: json.message });
            setDownloadModalOpen(true);
            return;
        } else {
            toast.error(json.message || 'server error, please contact admin.');
        }
        setDownload(undefined);
    }
};

function MedicalDeviceSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                return (
                    <tr key={index}>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default MedicalDevice;
