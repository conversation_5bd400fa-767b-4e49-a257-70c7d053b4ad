import { convertBase64ToFile, heic2convert, makeXMLRequest } from '@/helper';
import Button from '@components/form/Button';
import Label from '@components/form/Label';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import strings from '@lang/Lang';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import SelectImageIcon from '@partials/Icons/SelectImage';
import CancelButton from '@partials/MaterialButton/CancelButton';
import File from '@partials/MaterialFile/File';
import Modal from '@partials/MaterialModal/Modal';
import MaterialSignturePad from '@partials/SignaturePad/SignaturePad';
import { Formik, FormikErrors } from 'formik';
import { useRef, useState } from 'react';
import { toast } from 'react-toastify';
import SignaturePad from 'signature_pad';
import { fileTypes } from './ClientMediaModal';
import TextArea from '@components/form/TextArea';
import Error from '@components/form/Error';

export interface ClientProcedureAddPicturesModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    onSuccess?: () => Promise<any>;
    signable_id: number;
    signable_type: 'procedure' | 'note' | 'loc' | 'prescription';
    allTypeFiles?: boolean;
}

export interface IClientMediaValues {
    images: (File | Blob)[];
    sign?: string;
    description?: string;
    server?: string;
}

const fileLimit = 30;

const ClientFileBatchAddPicturesModal: React.FC<ClientProcedureAddPicturesModalProps> = ({
    openModal,
    setOpenModal,
    onSuccess = async () => {},
    signable_id,
    signable_type,
    allTypeFiles,
}) => {
    const { clientId } = useClient();
    const [progress, setProgress] = useState(0);
    const [isSigning, setIsSigning] = useState(false);
    const canvasRef = useRef<SignaturePad>(null);

    return (
        <Formik<IClientMediaValues>
            initialValues={{
                images: [],
                description: '',
                sign: undefined,
            }}
            enableReinitialize
            validate={(values) => validatePictureStore(values, isSigning)}
            onSubmit={async (values, { resetForm, setErrors, setSubmitting, setFieldError }) => {
                setSubmitting(true);

                const formData = new FormData();
                for (let i = 0; i < values.images.length; i++) {
                    formData.set(`files[${i}]`, values.images[i] as Blob);
                }
                if (values.sign) {
                    formData.set(`sign`, convertBase64ToFile(values.sign));
                }
                if (values.description) {
                    formData.set(`description`, values.description);
                }

                formData.set(`signable_id`, `${signable_id}`);
                formData.set(`signable_type`, signable_type);

                const response = await makeXMLRequest('POST', api.clientFileBatch.create(clientId), formData, (e) => {
                    setProgress(Math.round((e.loaded / e.total) * 100));
                });

                const data = JSON.parse(response.response);

                if (data.status === '1') {
                    resetForm();
                    canvasRef.current?.clear();
                    toast.success(data.message);
                    setOpenModal(false);
                    onSuccess();
                } else {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                }

                setSubmitting(false);
            }}
        >
            {({
                errors,
                values,
                touched,
                dirty,
                setFieldValue,
                setFieldTouched,
                handleSubmit,
                isSubmitting,
                isValidating,
                resetForm,
                validateForm,
            }) => {
                const handleModelClose = async () => {
                    if (isSubmitting || isValidating) return;
                    setOpenModal(false);
                    await resetForm();
                };
                return (
                    <Modal
                        open={openModal}
                        title={isSigning ? strings.Sign : strings.add_information}
                        handleClose={handleModelClose}
                        cancelButton={
                            <CancelButton fullWidth disabled={isSubmitting} onClick={handleModelClose}>
                                {strings.Cancel}
                            </CancelButton>
                        }
                        submitButton={
                            <Button
                                type="submit"
                                loading={isSubmitting}
                                onClick={async () => {
                                    const err = await validateForm();

                                    if ((!values.images.length || err.images) && !values.description) {
                                        return;
                                    }

                                    if (!isSigning && ((values.images.length && !err.images) || values.description)) {
                                        setIsSigning(true);
                                        return;
                                    }

                                    return handleSubmit();
                                }}
                            >
                                {isSigning ? strings.Submit : strings.Sign}
                            </Button>
                        }
                    >
                        <FormikErrorFocus />
                        <div className="space-y-4 p-4">
                            {isSubmitting && (
                                <div className="flex w-full items-center justify-center space-x-2 py-8 lg:py-12">
                                    {uploadingImage(values.images.length, progress)}
                                </div>
                            )}
                            {!isSubmitting &&
                                (isSigning ? (
                                    <MaterialSignturePad
                                        ref={canvasRef}
                                        onEnd={async () => {
                                            if (!canvasRef || !canvasRef.current) return;
                                            await setFieldValue('sign', canvasRef.current.toDataURL());
                                            setFieldTouched('sign', true);
                                        }}
                                        error={touched?.sign && Boolean(errors.sign)}
                                        helperText={touched?.sign && errors.sign}
                                        onClear={async () => {
                                            await setFieldValue('sign', '');
                                            await setFieldTouched('sign', false);
                                            canvasRef.current?.clear();
                                        }}
                                    />
                                ) : allTypeFiles ? (
                                    <div className="space-y-3">
                                        <TextArea
                                            value={values.description}
                                            onChange={(e) => {
                                                setFieldTouched('description');
                                                setFieldValue('description', e.target.value);
                                            }}
                                            label={strings.Text}
                                            rows={4}
                                            placeholder={strings.Text}
                                        />
                                        <div>
                                            <Label label={strings.files} />
                                            <File
                                                onChange={async (e) => {
                                                    const eFiles = e.target.files;
                                                    if (!eFiles?.length) return;

                                                    if (eFiles?.length > fileLimit) {
                                                        alert(strings.notesFiles_max_select.replace(':limit', `${fileLimit}`));
                                                        e.target.value = '';
                                                        return;
                                                    }
                                                    setFieldTouched('images');
                                                    setFieldValue('images', eFiles);
                                                }}
                                                multiple
                                                id="file_input"
                                                name="file"
                                                error={touched?.images && Boolean(errors.images)}
                                                helperText={touched?.images && (errors.images as string)}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        <div className="space-y-3">
                                            <TextArea
                                                value={values.description}
                                                onChange={(e) => {
                                                    setFieldTouched('description');
                                                    setFieldValue('description', e.target.value);
                                                }}
                                                rows={4}
                                                label={strings.Text}
                                                placeholder={strings.Text}
                                            />
                                            <label
                                                htmlFor="template_image"
                                                className={`description-center relative grid h-56 grid-cols-1 grid-rows-1 text-center place-items-center rounded border p-1 text-gray-500 ring-primary focus-within:ring-2 dark:text-gray-300 ${
                                                    errors?.images && touched?.images ? 'border-error' : 'border-gray-300 dark:border-gray-600'
                                                }`}
                                            >
                                                {loadImage(values.images)}
                                                <input
                                                    type="file"
                                                    id="template_image"
                                                    name="image"
                                                    className="invisible h-0 w-0 focus:outline-none"
                                                    accept=".png,.jpg,.jpeg,.heic,.webp"
                                                    disabled={isSubmitting}
                                                    multiple
                                                    onChange={async (event) => {
                                                        if (!event?.target?.files?.length) return;
                                                        let eFiles = event.target.files;
                                                        if (eFiles.length > fileLimit) {
                                                            alert(strings.formatString(strings.media_max_select, fileLimit));
                                                            return;
                                                        }
                                                        for (let index = 0; index < eFiles.length; index++) {
                                                            const filename = eFiles[index].name.split('.').pop();
                                                            if (fileTypes.includes(filename?.toLowerCase() ?? '')) continue;
                                                            alert(strings.only_images_allowed);
                                                            return;
                                                        }
                                                        let files = [];
                                                        for (const file of Array.from(eFiles)) {
                                                            let newFile: File | Blob | string = file;
                                                            if (newFile && !newFile.type) {
                                                                newFile = await heic2convert(file);
                                                            }
                                                            files.push(newFile);
                                                        }
                                                        await setFieldValue('images', files);
                                                        await setFieldTouched('images');
                                                    }}
                                                />
                                            </label>
                                            <Label label={strings.formatString(strings.media_max_select, fileLimit)} />
                                        </div>
                                    </>
                                ))}
                            <Error error={errors.images as string} />

                            <ServerError error={errors?.server} />
                        </div>
                    </Modal>
                );
            }}
        </Formik>
    );
};

export function validatePictureStore(values: IClientMediaValues, isSigning: boolean): void | object | Promise<FormikErrors<IClientMediaValues>> {
    const errors: FormikErrors<IClientMediaValues> = {};

    if ((!values.images || !values.images.length) && !values.description) {
        errors.images = strings.at_least_one_field_is_required;
        return errors;
    }
    if (isSigning) {
        if (!values.sign) {
            errors.sign = strings.signature_is_required;
        }
    }

    return errors;
}

function loadImage(images?: (File | Blob)[]) {
    if (images && images?.length) {
        return `${images.length} ${strings.images_has_selected}`;
    } else {
        return (
            <div>
                <SelectImageIcon className="mb-2 inline-block text-6xl text-gray-400" />
                <p className="text-sm font-medium">{strings.upload_media_message}</p>
            </div>
        );
    }
}

function uploadingImage(images_count: number, progress: number) {
    const perImageProgress = 100 / images_count;

    if (progress >= 100) {
        return (
            <div>
                <p>{strings.processing}...</p>
            </div>
        );
    }

    return (
        <div>
            <p>
                {parseInt((progress / perImageProgress).toString(), 10)}/{images_count} ({progress}% done)
            </p>
        </div>
    );
}

export default ClientFileBatchAddPicturesModal;
