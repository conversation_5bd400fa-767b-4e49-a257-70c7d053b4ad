import { customRound, getUnitKeyToValue } from '@/helper';
import Button from '@components/form/Button';
import { CreditCard } from '@components/form/CreditCard';
import InfoCard from '@components/form/InfoCard';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import strings from '@lang/Lang';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { PaymentIntent } from '@stripe/stripe-js';
import cx from 'classix';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

interface PendingInvoiceModalProps {
    open: boolean;
    handleClose: (isSuccess: boolean) => void;
    invoice: PendingInvoice;
}

export interface PendingInvoice {
    id: string;
    amount_due: number;
    intent: string;
    currency: string;
    subtotal: number;
    subtotal_excluding_tax: number;
    tax: number;
    status: PaymentIntent.Status;
    tax_percent?: number;
    total: number;
    discount?: DiscountItem;
    items: DiscountItem[];
}

export interface DiscountItem {
    description: string;
    amount: number;
    currency: string;
}

type TError = {
    [key: string]: string;
};

const PendingInvoiceModal: FC<PendingInvoiceModalProps> = ({ open, handleClose, invoice: fetchedInvoice }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [cancelling, setCancelling] = useState(false);
    const [submitting, setSubmitting] = useState(false);

    const [invoice, setInvoice] = useState(fetchedInvoice);

    const isCardRequired = invoice.status === 'requires_payment_method';
    const [errors, setErrors] = useState<TError>();

    const { mutate: authMutate } = useAuth();

    useEffect(() => {
        setInvoice(invoice);
    }, [invoice]);

    async function handle3dCard() {
        setSubmitting(true);
        let result = undefined;
        if (isCardRequired) {
            const card = elements?.getElement(CardElement);
            // const cardDetail = card && (await stripe?.createToken(card));

            // if (cardDetail?.error) {
            //     setErrors({ card: cardDetail?.error?.message || 'card error, please try again.' });
            //     setSubmitting(false);
            //     return;
            // }

            if (!stripe || !elements || !card) {
                setErrors({ card: 'Card is required' });
                setSubmitting(false);
                return;
            }

            // const cardPaymentMethod = await stripe.createPaymentMethod({
            //     type: 'card',
            //     card: { token: cardDetail?.token?.id ?? '' },
            // });

            // const cardFormData = new FormData();
            // cardFormData.set('payment_method_id', cardPaymentMethod.paymentMethod?.id ?? '');
            // cardFormData.set(`labels[0]`, "RECORD");
            // const response = await fetch(api.creditCard.create, {
            //     method: 'POST',
            //     headers: {
            //         Accept: 'application/json',
            //         'X-App-Locale': strings.getLanguage(),
            //     },
            //     credentials: 'include',
            //     body: cardFormData,
            // });
            // const json = await response.json();
            // if (json.status === '1') {
            //     toast.success(json.message ?? 'Success!');
            //     onClose();
            // } else {
            //     toast.error(json.message ?? 'Error!');
            // }

            result = await stripe?.confirmCardPayment(invoice.intent, {
                payment_method: {
                    card: card,
                },
                setup_future_usage: 'off_session',
            });
        } else {
            result = await stripe?.confirmPayment({
                clientSecret: invoice.intent,
                redirect: 'if_required',
            });
        }
        if (result?.error) {
            console.log(result.error.message);
            toast.error(result?.error?.message || strings.something_went_wrong_please_contact_support);
            setSubmitting(false);

            if (result.error.payment_intent) {
                setInvoice({
                    ...invoice,
                    status: result.error.payment_intent.status,
                    intent: result.error.payment_intent.client_secret ?? '',
                });
            }

            return;
        } else {
            // The payment has been processed!
            if (result?.paymentIntent?.status === 'succeeded') {
                toast.success('Payment succeeded!');
                authMutate();
                setSubmitting(false);
                handleClose(true);
            }
        }
    }

    async function voidInvoice() {
        // setCancelling(true);

        // const res = await voidApiCall();
        // const data = await res.json();
        // if (data.status === '1') {
        handleClose(false);
        //     toast.success(data.message);
        // } else {
        //     toast.error(strings.something_went_wrong_please_contact_support);
        // }
        // authMutate();

        // setCancelling(false);
    }

    return (
        <Modal
            open={open}
            title={strings.payment_failed}
            handleClose={() => handleClose(false)}
            size="medium"
            cancelButton={<CancelButton onClick={voidInvoice} loading={cancelling} disabled={cancelling || submitting} />}
            hideCloseButton
            submitButton={
                <Button onClick={handle3dCard} color="primary" loading={submitting} disabled={cancelling || submitting}>
                    {strings.Confirm} Payment
                </Button>
            }
        >
            <div className="flex flex-col gap-3 p-4 pt-2 md:p-6">
                <p className="whitespace-pre-wrap rounded-lg bg-error/10 px-3 py-1 text-error dark:text-red-400">
                    {cx(
                        invoice.status === 'requires_action' && strings.card_3d_failed_payment,
                        invoice.status === 'requires_confirmation' && strings.card_3d_failed_payment,
                        invoice.status === 'requires_payment_method' && strings.update_card_info_error,
                    )}
                </p>
                <div className="space-y-2 border-b border-t py-4 dark:border-gray-600">
                    {invoice.items?.map((sub, index) => <TextValueComp key={index} text={sub.description} amount={sub.amount} />)}
                </div>

                <TextValueComp text={`${strings.subtotal}:`} amount={invoice.subtotal_excluding_tax} />

                {!!invoice.discount && (
                    <TextValueComp text={`${strings.discount}: ${invoice.discount.description}`} amount={-invoice.discount.amount} />
                )}
                <TextValueComp
                    text={`${strings.total_excl_tax}:`}
                    amount={customRound(invoice.subtotal_excluding_tax - (!!invoice.discount ? invoice.discount.amount : 0))}
                />
                <TextValueComp text={`${strings.tax}: ${invoice.tax_percent ? `(${invoice.tax_percent}%)` : ''}`} amount={invoice.tax} />
                <TextValueComp text={`${strings.total_amount_due}:`} amount={invoice.amount_due} />
                {invoice.status === 'requires_payment_method' && (
                    <CreditCard
                        error={errors?.card}
                        onChange={(event) => {
                            if (event.empty) {
                                setErrors({ card: 'Card number is required' });
                            }
                        }}
                    />
                )}

                <InfoCard message={strings.any_issues_contact_support} variant='warning' />
            </div>
        </Modal>
    );

    function TextValueComp({ text, amount }: { text: string; amount: number }) {
        return (
            <div className="flex">
                <p className="flex-1">{text}</p>
                <p className="font-medium">{currencyPlusAmount(invoice.currency, amount)}</p>
            </div>
        );
    }

    function currencyPlusAmount(currency: string, amount: number) {
        if (amount < 0) {
            return `- ${getUnitKeyToValue(currency)}${Math.abs(amount)}`;
        }
        return `${getUnitKeyToValue(currency)}${amount}`;
    }

    async function voidApiCall() {
        return await fetch(api.invoice.void(invoice.id), {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
        });
    }
};

export default PendingInvoiceModal;
