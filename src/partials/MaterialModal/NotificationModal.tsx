import { useEffect, useState } from 'react';
import strings from '../../lang/Lang';
import Button from '@components/form/Button';
import Modal from '@partials/MaterialModal/Modal';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { NotificationResponse } from '@interface/common';
import api from '@configs/api';
import RadioButton from '@partials/MaterialRadioButton/MaterialRadioButton';
import { timeZone } from '@/helper';
import { toast } from 'react-toastify';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import useLocalStorage from '@hooks/useLocalStorage';

type NotificationValue = {
    ids: number[];
    date: string;
};
function NotificationModal() {
    const [open, setOpen] = useState(false);
    const [popupShowAgin, setPopupShowAgin] = useState<string>('2');
    const [isLoading, setIsLoading] = useState(false);
    const { storedValue: remindNotificationPopup, setStorageValue: setRemindNotificationPopup } = useLocalStorage<NotificationValue | undefined>(
        'remind_notification_popup',
        {
            ids: [],
            date: new Date().toISOString(),
        },
    );
    const { data: notificationData, mutate, loading } = usePaginationSWR<NotificationResponse, Error>(api.notificationList({ is_popup: 1 }));
    const { mutate: countMutate } = usePaginationSWR<NotificationResponse, Error>(api.notificationList({ count: 1 }));
    const notificationValue = notificationData?.data?.filter((item) => !remindNotificationPopup?.ids?.includes(item.id)).at(0);

    const isDataExpired = () => {
        if (!remindNotificationPopup?.date) return true;

        const storedTime = new Date(remindNotificationPopup.date).getTime();
        const now = new Date().getTime();
        const diffInHours = (now - storedTime) / (1000 * 60 * 60);
        if (diffInHours > 24) {
            setRemindNotificationPopup(undefined);
        }
        return;
    };
    useEffect(() => {
        isDataExpired();
    }, []);
    useEffect(() => {
        if (notificationData?.data?.length && notificationValue?.id) {
            setOpen(true);
        }
    }, [notificationData?.data?.length, open]);

    const handleSunbmit = async (id: number) => {
        if (popupShowAgin === '1') {
            setIsLoading(true);
            const response = await fetch(api.notificationRead(id), {
                method: 'GET',
                headers: {
                    Accept: 'application/json',
                    'X-App-Locale': strings.getLanguage(),
                    'X-Time-Zone': timeZone(),
                },
                credentials: 'include',
            });
            const data = await response.json();
            if (data.status === '1') {
                await mutate();
                await countMutate();
                await setOpen(false);
                await setPopupShowAgin('2');
            } else {
                toast.error(data.message || 'server error, please contact admin.');
            }
            setIsLoading(false);
        }
        if (popupShowAgin === '2') {
            setRemindNotificationPopup({ date: new Date().toDateString(), ids: [...(remindNotificationPopup?.ids ?? []), id] });
            setOpen(false);
        }
    };
    return (
        <Modal
            open={!!open}
            title={`${notificationValue?.title || ''}`}
            handleClose={() => setOpen(false)}
            hideCloseButton
            buttonChildren={
                <div className="flex flex-col items-center space-y-4 border-t p-4 dark:border-dimGray">
                    <div className="flex gap-4 px-4">
                        <RadioButton label={strings.do_not_show_again} checked={popupShowAgin === '1'} onChange={() => setPopupShowAgin('1')} />
                        <RadioButton label={strings.remind_me_later} checked={popupShowAgin === '2'} onChange={() => setPopupShowAgin('2')} />
                    </div>
                    <Button
                        onClick={() => {
                            handleSunbmit(notificationValue?.id ?? 0);
                        }}
                        loading={isLoading}
                    >
                        {strings.Submit}
                    </Button>
                </div>
            }
        >
            <div className="space-y-4 p-4">
                {loading || !notificationValue ? (
                    <SectionLoading />
                ) : (
                    <div className="prose dark:prose-invert" dangerouslySetInnerHTML={{ __html: notificationValue?.description ?? '' }}></div>
                )}
            </div>
        </Modal>
    );
}

export default NotificationModal;
