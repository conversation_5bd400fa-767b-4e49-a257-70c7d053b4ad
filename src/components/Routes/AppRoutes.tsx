import routes, { BreadCrumb } from '@configs/routes';
import useAuth from '@hooks/useAuth';
import FullPageError from '@partials/Error/FullPageError';
import FullPageLoading from '@partials/Loadings/FullPageLoading';
import { lazy, Suspense, useMemo } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';
import prescriptionsRoutes from '@configs/prescriptionsRoutes';
import BookingPortalSuccess from '@components/BookingPortal/pages/BookingPortalSuccess';

const LazyLogin = lazy(() => import('@pages/auth/Login'));
const LazyMaster = lazy(() => import('@components/Master'));
const LazySignup = lazy(() => import('@pages/auth/signup'));
const LazySignupSetup = lazy(() => import('@pages/auth/signup/set-up'));
const LazySignupSubscription = lazy(() => import('@pages/auth/signup/Subscription2'));
const LazyForgotPassword = lazy(() => import('@pages/auth/forgot'));
const LazyRegPortal = lazy(() => import('@components/RegistrationPortal/RegistrationPortal'));
const SubscriptionPublic = lazy(() => import('@components/Subscription/Public/SubscriptionPublic'));
const LazyBookPortal = lazy(() => import('@components/BookingPortal/BookingPortal'));
const LazyBookingEmail = lazy(() => import('@components/BookingPortal/emailTemplate/BookingEmail'));
const LazyQuestionaryLoc = lazy(() => import('@components/QuestionnairesAndLocPortal/QuestionaryAndLocPortal'));
const LazyCancelSubscription = lazy(() => import('@components/BookingPortal/emailTemplate/CancelSubscrition'));

const LazyPrescriptionLogin = lazy(() => import('@pages/auth/Prescription/PrescriptionLogin'));
const LazyPrescriptionSignup = lazy(() => import('@pages/auth/Prescription/PrescriptionSignup'));
const LazyPrescriptionSignupSetup = lazy(() => import('@pages/auth/Prescription/PrescriptionRegister'));
const LazyPrescriptionDoctorPortal = lazy(() => import('@components/Prescription/PrescriptionDoctorPortal'));
const EmailVerifyPage = lazy(() => import('@components/verify/EmailVerifyPage'));
const LazyFortnoxCallback = lazy(() => import('@pages/fortnox/FortnoxCallback'));

export interface AppRoutesProps {}

const AppRoutes: React.FC<AppRoutesProps> = () => {
    const { user, userType } = useAuth();

    const appRoutes = useMemo(() => {
        if (userType.isMasterAdmin) return [];
        if (userType.isPrescriptionUser) return prescriptionsRoutes();
        if (userType.isSuperAdmin) return routes();
        if (userType.isAdmin) return routes().filter((v) => !v.not_allowed_to_role?.includes('admin'));
        if (userType.isUser) return routes().filter((v) => !v.not_allowed_to_role?.includes('admin') || !v.not_allowed_to_role?.includes('user'));
        return [];
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user?.user_role]);

    return (
        <Suspense fallback={<FullPageLoading />}>
            <Routes>
                {!user && <Route path="/" element={<LazyLogin />} />}
                <Route path="/login" element={<LazyLogin />} />
                <Route path="/register" element={<Navigate replace to="/sign-up" />} />
                <Route path="/sign-up" element={<LazySignup />} />
                <Route path="/email/verify/:id/:hash" element={<EmailVerifyPage />} />
                {/* <Route path="/sign-up/set-up" element={<LazySignupSetup />} /> */}
                <Route path="/subscription" element={<LazySignupSubscription />} />
                <Route path="/checkout" element={<LazySignupSubscription />} />
                <Route path="/checkout/finalize" element={<LazySignupSubscription />} />
                <Route path="/forgot-password" element={<LazyForgotPassword />} />
                <Route path="/prescription/forgot-password" element={<LazyForgotPassword />} />
                <Route path="/registration/:companyId" element={<LazyRegPortal />} />
                <Route path="/booking/:companyId" element={<LazyBookPortal />} />
                <Route path="/booking/reschedule/:companyId" element={<LazyBookPortal />} />
                <Route path="/payment" element={<BookingPortalSuccess />} />
                <Route path="/booking/info/:companyId" element={<LazyBookingEmail />} />
                <Route path="/cancel-subscription" element={<LazyCancelSubscription />} />
                <Route path="/questionary-loc/:companyId" element={<LazyQuestionaryLoc />} />
                <Route path="/fortnox/callback" element={<LazyFortnoxCallback />} />
                {user?.user_role !== 'master_admin' && <Route path="/subscription" element={<LazySignupSubscription />} />}
                <Route path="/public/subscription" element={<SubscriptionPublic />} />
                {
                    <Route path="/" element={<LazyMaster />}>
                        {appRoutes.map(renderRoute)}
                    </Route>
                }
                {/* Prescription */}
                <Route path="/prescription/login" element={<LazyPrescriptionLogin />} />
                <Route path="/prescription/sign-up" element={<LazyPrescriptionSignup />} />
                <Route path="/prescription/sign-up/set-up" element={<LazyPrescriptionSignupSetup />} />

                <Route path="/doctor-invitation/:companyId" element={<LazyPrescriptionDoctorPortal />} />

                <Route path="*" element={<FullPageError code={404} message="Page Not Found." />} />
            </Routes>
        </Suspense>
    );
};

function renderRoute(route: BreadCrumb, key: number) {
    if (!route.routes?.length) {
        return <Route key={key} path={route.path} index={route.index} element={route.Component} />;
    }
    return (
        <Route path={route.path} element={route.Component} key={key}>
            {route.routes.map((r, i) => renderRoute(r, i))}
        </Route>
    );
}

export default AppRoutes;
