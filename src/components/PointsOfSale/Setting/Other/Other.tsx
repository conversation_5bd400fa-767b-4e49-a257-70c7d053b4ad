import config from '@/config';
import { commonFetch, getUrlExtension } from '@/helper';
import LightBox from '@components/LightBox/LightBox';
import Skeleton from '@components/Skeleton/Skeleton';
import IconButton from '@components/form/IconButton';
import Input from '@components/form/Input';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import useDebounce from '@hooks/useDebounce';
import UploadIcon from '@icons/Upload';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import CheckIcon from '@partials/Icons/Check';
import EyeIcon from '@partials/Icons/Eye';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { useHours } from '@provider/TimeProvider';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import * as React from 'react';
import { toast } from 'react-toastify';
import LicenseAgreementViewModal from '../LicenseAgreement/LicenseAgreementViewModal';
import { POSSettingContext } from '../POSSetting';
import DeleteIcon from '@partials/Icons/Delete';
import LoadingIcon from '@icons/Loading';
import MaterialCheckbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import CheckoutCompanyPhotoUploadModal from './CheckoutCompanyPhotoUploadModal';
import Settings from '@configs/settings';

export interface OtherProps {}

async function download(fileName: string, url: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
        });

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);
        const ext = getUrlExtension(fileName) ? '' : getUrlExtension(url);
        a.setAttribute('download', `${fileName}${ext ? '.' + ext : ''}`);
        a.click();
    } catch (error) {
        console.error(error);
    }
}

const Other: React.FC<OtherProps> = () => {
    // return <OtherSkeleton />;
    const { user } = useAuth();

    const { renderHours } = useHours();

    const [emailError, setEmailError] = React.useState<string>();
    const [showCheckoutCompanyLogo, setShowCheckoutCompanyLogo] = React.useState(false);
    const [viewLicenseAgreement, setViewLicenseAgreement] = React.useState(false);
    const [swishQR, setSwishQR] = React.useState(false);

    const { findSetting, loading, updateSetting, reload } = React.useContext(POSSettingContext);

    const Z_REPORT_EMAIL = findSetting('Z_REPORT_EMAIL');
    const license_agreement = findSetting('POS_LICENSE');
    const swish_qr = findSetting('POS_SWISH_QR');
    const online_payment = findSetting('ONLINE_PAYMENT');
    const vivaPaymentSource = findSetting('VIVA_WALLET_SOURCE_CODE');

    const hasMerchantId = !!user?.company?.viva_merchant_id;

    const [onlinePayment, setOnlinePayment] = React.useState(online_payment?.value === '1' && hasMerchantId ? true : false);
    const [email, setEmail] = React.useState(Z_REPORT_EMAIL?.value ?? '');

    const swishQRfileRef = React.useRef<HTMLInputElement>(null);

    const debouncedEmail = useDebounce(email, 600);

    React.useEffect(() => {
        setEmail(Z_REPORT_EMAIL?.value ?? '');
    }, [Z_REPORT_EMAIL]);

    React.useEffect(() => {
        if (!email || email === '') return;
        if (!config.emailValidationRegex.test(email)) {
            setEmailError(strings.please_provide_valid_email);
            return;
        }
    }, [email]);

    React.useEffect(() => {
        if (!debouncedEmail || debouncedEmail === '') {
            updateSetting('Z_REPORT_EMAIL', '');
            return;
        }

        if (emailError) return;

        updateSetting('Z_REPORT_EMAIL', debouncedEmail);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [debouncedEmail]);

    if (loading) {
        return <ItemSkeletonComponent />;
    }

    return (
        <>
            <ModalSuspense>
                <LicenseAgreementViewModal
                    openModal={viewLicenseAgreement}
                    onClose={() => setViewLicenseAgreement(false)}
                    fileName={license_agreement?.value ?? ''}
                />
                <CheckoutCompanyPhotoUploadModal
                    openModal={showCheckoutCompanyLogo}
                    onClose={() => setShowCheckoutCompanyLogo(false)}
                    paymentSourceId={vivaPaymentSource?.value ?? ''}
                />
                <LightBox
                    selectedIndex={0}
                    open={swishQR}
                    imageURLs={[api.storageUrl(swish_qr?.value ?? '')]}
                    handleClose={() => setSwishQR(false)}
                    handleDownload={async (index) => {
                        await download(pos_strings.swish.swish_qr, api.storageUrl(swish_qr?.value ?? ''));
                    }}
                />
            </ModalSuspense>
            <div className="my-4 space-y-4">
                <p className="text-base uppercase text-primary dark:text-primaryLight">{pos_strings.report.zreport}</p>
                <div className="grid grid-flow-row grid-cols-1 gap-6 rounded-md bg-purpleGray p-4 dark:bg-black/40 md:grid-cols-2">
                    <div>
                        <h3 className="text-lg font-medium">{pos_strings.config.z_report_email_to}</h3>
                        <h3 className="text-sm text-mediumGray">{pos_strings.config.auto_z_report_to}</h3>
                    </div>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : (
                        <div className="h-10 w-full md:w-3/4">
                            <Input
                                value={email}
                                onChange={(e) => {
                                    setEmail(e.target.value);
                                    setEmailError(undefined);
                                }}
                                color="white"
                                suffixButton={!emailError && email ? <CheckIcon className="text-success dark:bg-dimGray" /> : <></>}
                                error={emailError}
                                placeholder={strings.Email}
                            />
                        </div>
                    )}

                    <div>
                        <h3 className="text-lg font-medium">{pos_strings.report.close_batch}</h3>
                        <h3 className="text-sm text-mediumGray">{pos_strings.config.auto_z_report_time}</h3>
                    </div>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : (
                        <div className="h-10 w-full md:w-3/4">
                            <Input color="white" disabled placeholder={strings.DateTime} value={renderHours(dayjs().startOf('day'))} />
                        </div>
                    )}
                </div>
                <p className="text-base uppercase text-primary dark:text-primaryLight">{pos_strings.viva.viva_wallet}</p>

                <div className="grid grid-flow-row grid-cols-1 gap-6 rounded-md bg-purpleGray p-4 dark:bg-black/40 md:grid-cols-2">
                    <div>
                        <h3 className="text-lg font-medium">{pos_strings.viva.merchant_id}</h3>
                        <h3 className="text-sm text-mediumGray">{pos_strings.viva.unique_assigned_to_you}</h3>
                    </div>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : (
                        <Input className="h-10 w-full md:w-3/4" value={user?.company?.viva_merchant_id ?? ''} disabled color="white" />
                    )}
                    <div className="flex">
                        <MaterialCheckbox
                            checked={onlinePayment}
                            label=""
                            disabled={!hasMerchantId}
                            onChange={(e) => {
                                setOnlinePayment(e.target.checked);
                                updateSetting('ONLINE_PAYMENT', e.target.checked ? '1' : '0');
                            }}
                        />
                        <h3 className="text-lg font-medium">{strings.online_payment}</h3>
                    </div>
                </div>

                <div className="grid grid-flow-row grid-cols-1 gap-6 rounded-md bg-purpleGray p-4 dark:bg-black/40 md:grid-cols-2">
                    <div>
                        <h3 className="text-lg font-medium">{pos_strings.viva.checkout_company_logo}</h3>
                        <h3 className="text-sm text-mediumGray">{pos_strings.viva.checkout_company_logo_desc}</h3>
                    </div>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : (
                        <IconButton className="focus:outline-none" onClick={() => setShowCheckoutCompanyLogo(true)} name={strings.View}>
                            {<EyeIcon />}
                        </IconButton>
                    )}
                </div>

                <p className="text-base uppercase text-primary dark:text-primaryLight">{pos_strings.ecr.ecr}</p>

                <div className="grid grid-flow-row grid-cols-1 gap-6 rounded-md bg-purpleGray p-4 dark:bg-black/40 md:grid-cols-2">
                    <div>
                        <h3 className="text-lg font-medium">{pos_strings.ecr.ecr_id}</h3>
                        <h3 className="text-sm text-mediumGray">{pos_strings.ecr.ecr_message}</h3>
                    </div>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : (
                        <Input className="h-10 w-full md:w-3/4" value={user?.company?.ccu_register_id ?? ''} disabled color="white" />
                    )}
                </div>

                <p className="text-base uppercase text-primary dark:text-primaryLight">{pos_strings.swish.swish}</p>

                <div className="grid grid-flow-row grid-cols-1 gap-6 rounded-md bg-purpleGray p-4 dark:bg-black/40 md:grid-cols-2">
                    <div>
                        <h3 className="text-lg font-medium">{pos_strings.swish.swish_qr}</h3>
                        <h3 className="text-sm text-mediumGray">{pos_strings.swish.info_upload_swish_qr}</h3>
                    </div>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : swish_qr?.value ? (
                        <div className="flex h-10 w-full items-center justify-start space-x-4 md:w-3/4">
                            <IconButton disabled={loading} className="focus:outline-none" onClick={() => setSwishQR(true)} name={strings.View}>
                                <EyeIcon />
                            </IconButton>
                            <IconButton
                                className="text-red-500 focus:outline-none"
                                disabled={loading}
                                onClick={() => {
                                    updateSetting('POS_SWISH_QR', '');
                                }}
                                name={strings.Delete}
                            >
                                {loading ? <LoadingIcon /> : <DeleteIcon />}
                            </IconButton>
                        </div>
                    ) : (
                        <Formik<{ image?: File }>
                            initialValues={{
                                image: undefined,
                            }}
                            onSubmit={async (values, { resetForm }) => {
                                if (!values.image) return;

                                try {
                                    const file = values.image;

                                    const form = new FormData();
                                    form.set('image', file);

                                    const data = await commonFetch(api.pos.setting.uploadSwishQR, {
                                        method: 'POST',
                                        headers: {
                                            Accept: 'application/json',
                                            'X-App-Locale': strings.getLanguage(),
                                        },
                                        credentials: 'include',
                                        body: form,
                                    });

                                    if (data.status === '1') {
                                        resetForm();
                                        if (swishQRfileRef.current) swishQRfileRef.current.value = '';
                                        await reload();
                                        toast.success(data.message);
                                    } else {
                                        toast.error(data.message || 'server error, contact admin.');
                                    }
                                } catch (error: any) {
                                    toast.error(error?.message || 'server error, contact admin.');
                                }
                            }}
                        >
                            {({ submitForm, setFieldValue, isSubmitting }) => (
                                <>
                                    <IconButton
                                        className="focus:outline-none"
                                        disabled={loading || isSubmitting}
                                        onClick={() => {
                                            swishQRfileRef.current?.click();
                                        }}
                                        name={strings.choose_file_here}
                                    >
                                        {loading || isSubmitting ? <LoadingIcon /> : <UploadIcon />}
                                    </IconButton>
                                    <input
                                        type="file"
                                        ref={swishQRfileRef}
                                        onChange={async (e) => {
                                            if (e.target.files) {
                                                const file = e.target.files[0];
                                                await setFieldValue('image', file);
                                                await submitForm();
                                            }
                                        }}
                                        className="hidden"
                                    />
                                </>
                            )}
                        </Formik>
                    )}
                </div>

                <div className="grid grid-flow-row grid-cols-1 items-center gap-6 pt-4 md:grid-cols-2">
                    <h3 className="text-base font-medium uppercase text-primary dark:text-primaryLight">{strings.license_agreement}</h3>

                    {loading ? (
                        <Skeleton className="h-10 w-full md:w-3/4" />
                    ) : (
                        <div className="h-10 w-full md:w-3/4">
                            <IconButton className="focus:outline-none" onClick={() => setViewLicenseAgreement(true)} name={strings.View}>
                                <EyeIcon />
                            </IconButton>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

function ItemSkeletonComponent() {
    return (
        <>
            <div className="my-4 flex flex-col gap-2">
                <Skeleton className="h-10 w-44 cursor-wait" />
                <div className="flex flex-wrap items-center justify-between gap-2">
                    <Skeleton className="h-10 w-72 cursor-wait" />
                    <Skeleton className="h-10 w-72 cursor-wait" />
                </div>
            </div>
        </>
    );
}
export default Other;
