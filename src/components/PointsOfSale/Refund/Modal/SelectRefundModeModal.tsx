import { firstCapital, isDecimal, limitDecimalPlaces } from '@/helper';
import CalendarSelect from '@components/Calendar/Custom/CalendarSelect';
import Button from '@components/form/Button';
import Input from '@components/form/Input';
import Label from '@components/form/Label';
import useAuth from '@hooks/useAuth';
import useRefund from '@hooks/useRefund';
import pos_strings from '@lang/pos/Lang';
import CancelButton from '@partials/MaterialButton/CancelButton';
import Modal from '@partials/MaterialModal/Modal';
import Switch from '@partials/MaterialSwitch';
import { Formik, isInteger, useFormikContext } from 'formik';
import * as React from 'react';
import { IRefundValues } from '../Refund';
import { validationRefundPaymentMode } from '../validation';
import strings from '@lang/Lang';
import { POSPaymentMethod } from '@interface/model/receipt';

export interface SelectRefundModeModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface IRefundPaymentModeValues {
    openBank: boolean;
    openGiftCard: boolean;

    bankValue: string;

    giftCardValue: string;
    giftCardExpire: string;
}

const SelectRefundModeModal: React.FC<SelectRefundModeModalProps> = ({ openModal, setOpenModal }) => {
    const { values, isSubmitting, setFieldValue } = useFormikContext<IRefundValues>();
    const { withCurrency } = useAuth();
    const data = useRefund();

    return (
        <Formik<IRefundPaymentModeValues>
            initialValues={{
                openGiftCard: values.gift_card,
                giftCardValue: values.gift_card_value.toString() ?? '0',
                openBank: values.viva,
                bankValue: values.viva_value.toString() ?? '0',
                giftCardExpire: values.gift_card_expire_at,
            }}
            enableReinitialize
            validate={(v) => validationRefundPaymentMode(v, data)}
            onSubmit={async (values, { resetForm, setFieldError }) => {
                setFieldValue('gift_card_value', values.giftCardValue);
                setFieldValue('viva_value', values.bankValue);
                setFieldValue('gift_card_expire_at', values.giftCardExpire);
                setFieldValue('gift_card', values.openGiftCard);
                setFieldValue('viva', values.openBank);

                setOpenModal(false);
            }}
        >
            {({ values, errors, setFieldValue, touched, submitForm, setFieldTouched }) => {
                return (
                    <Modal
                        open={openModal}
                        closeOnBackdropClick={false}
                        hideCloseButton={isSubmitting}
                        title={pos_strings.refund.select_payment_mode}
                        handleClose={() => (!isSubmitting ? setOpenModal(false) : {})}
                        cancelButton={
                            <CancelButton disabled={isSubmitting} onClick={() => (!isSubmitting ? setOpenModal(false) : {})}>
                                {strings.Cancel}
                            </CancelButton>
                        }
                        submitButton={
                            <Button
                                type="submit"
                                disabled={!(values.openBank || values.openGiftCard)}
                                onClick={() => {
                                    if (!(values.openBank || values.openGiftCard)) return;
                                    submitForm();
                                }}
                            >
                                {firstCapital(pos_strings.SAVE)}
                            </Button>
                        }
                    >
                        <div className="min-h-[6rem] px-6 py-6">
                            <div className="space-y-2">
                                <div className="soft-searchbar grid gap-10 md:overflow-auto">
                                    <div className="grid gap-5">
                                        <div className="flex items-center justify-between">
                                            <p>{pos_strings.gift_card.gift_card}</p>
                                            <Switch
                                                onChange={(check) => {
                                                    setFieldTouched('openGiftCard');
                                                    setFieldValue('openGiftCard', !values.openGiftCard);
                                                }}
                                                disabled={data.gift_card.disabled}
                                                checked={values.openGiftCard}
                                            />
                                        </div>
                                        {values.openGiftCard ? (
                                            <>
                                                <Input
                                                    name="refund_value"
                                                    value={values.giftCardValue}
                                                    required
                                                    onChange={(e) => {
                                                        if (e.target.value && !isInteger(e.target.value)) return;
                                                        setFieldTouched('giftCardValue');
                                                        setFieldValue('giftCardValue', e.target.value);
                                                    }}
                                                    error={touched?.giftCardValue && errors?.giftCardValue}
                                                    label={`${pos_strings.refund.enter_refund_amount}`}
                                                    placeholder={withCurrency('100')}
                                                />
                                                <Label>{`${strings.New} ${pos_strings.gift_card.gift_card}`}</Label>
                                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                    <Input
                                                        name="gift_code"
                                                        label={pos_strings.gift_card.gift_card_number}
                                                        placeholder={'XXXXXX'}
                                                        onText={pos_strings.gift_card.auto_generated}
                                                        disabled
                                                    />
                                                    <CalendarSelect
                                                        selectedDate={values.giftCardExpire}
                                                        placeholder={strings.select_date}
                                                        inputProps={{
                                                            label: pos_strings.gift_card.expiry_date,
                                                            placeholder: strings.select_date,
                                                            required: true,
                                                        }}
                                                        error={touched?.giftCardExpire && errors.giftCardExpire}
                                                        minToday
                                                        onChange={(date) => {
                                                            setFieldTouched('giftCardExpire');
                                                            setFieldValue('giftCardExpire', date);
                                                        }}
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            <></>
                                        )}
                                    </div>
                                    <div className="grid gap-5">
                                        <div className="flex items-center justify-between">
                                            <p>
                                                {data.receipt?.payment_method === 'swish'
                                                    ? pos_strings.refund.refund_in_swish
                                                    : pos_strings.refund.refund_in_bank}
                                            </p>
                                            <div>
                                                <Switch
                                                    onChange={(check) => {
                                                        setFieldTouched('openBank');
                                                        setFieldValue('openBank', !values.openBank);
                                                    }}
                                                    disabled={data.bank.disabled}
                                                    checked={values.openBank}
                                                />
                                            </div>
                                        </div>
                                        {values.openBank ? (
                                            <Input
                                                name="refund_value"
                                                value={values.bankValue}
                                                required
                                                onChange={(e) => {
                                                    if (e.target.value && !isDecimal(e.target.value, 2)) return;

                                                    setFieldTouched('bankValue');
                                                    setFieldValue('bankValue', limitDecimalPlaces(e.target.value, 2));
                                                }}
                                                error={touched?.bankValue && errors?.bankValue}
                                                label={`${pos_strings.refund.enter_refund_amount}`}
                                                placeholder={withCurrency('100')}
                                            />
                                        ) : (
                                            <></>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Modal>
                );
            }}
        </Formik>
    );
};

export default SelectRefundModeModal;
