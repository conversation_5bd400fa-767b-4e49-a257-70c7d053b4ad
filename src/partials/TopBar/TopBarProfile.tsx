import Avatar from '@components/avatar/Avatar';
import { Menu, MenuButton, MenuItems, MenuItem, Transition } from '@headlessui/react';
import * as React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import api from '../../configs/api';
import { clearSWRCache, generateUserFullName } from '../../helper';
import useAuth from '../../hooks/useAuth';
import strings from '../../lang/Lang';
import DownIcon from '../Icons/Down';
import cx from 'classix';

export interface TopBarProfileProps {}

const TopBarProfile: React.FC<TopBarProfileProps> = () => {
    const [loading, setLoading] = React.useState(false);
    const navigate = useNavigate();

    const {
        user,
        mutate,
        userType: { isMasterAdmin, isPrescriptionUser },
    } = useAuth();
    function onMyProfileClick() {
        navigate(user?.user_role === 'master_admin' ? '/admin/profile' : '/profile');
    }

    async function onLogoutClick() {
        if (loading) return;
        setLoading(true);
        try {
            const response = await fetch(api.logout, {
                method: 'GET',
                headers: {
                    Accept: 'application/json',
                    'X-App-Locale': strings.getLanguage(),
                },
                credentials: 'include',
            });

            const data = await response.json();

            if (response.status === 401) {
                toast.error(data.message || 'Unauthorized', {});
            }

            await mutate();
            if (data.status === '1') {
                await toast.success(data.message);
                await clearSWRCache();
                (await isPrescriptionUser) ? navigate('prescription/login') : navigate('/login');
            } else {
                toast.error(data.message);
            }
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    }

    return (
        <div className="flex items-center">
            <Menu as="div" className="relative inline-block text-left">
                <div>
                    <MenuButton className="flex items-center md:mr-1">
                        <Avatar
                            className="h-8 w-8 md:h-10 md:w-10"
                            src={user?.profile_photo ? `${import.meta.env.REACT_APP_STORAGE_PATH}/${user?.profile_photo}` : undefined}
                        />
                        <div className="ml-3 mr-2 hidden text-left md:block">
                            <span className="font-semibold capitalize">{generateUserFullName(user || undefined)}</span>
                            {!isMasterAdmin && !isPrescriptionUser && (
                                <span className="hidden text-xs font-medium text-primary dark:text-gray-500 md:block">
                                    {user?.company?.storage_usage} {strings?.used}
                                </span>
                            )}
                        </div>
                        <DownIcon className="ml-1 hidden md:ml-0 md:block" />
                    </MenuButton>
                </div>
                <MenuItems
                    modal={false}
                    className={cx(
                        'mt-2 w-48 divide-y divide-gray-100 rounded-lg bg-white py-1.5 shadow-card outline-none dark:divide-gray-700 dark:bg-dimGray dark:shadow-none dark:ring-1 dark:ring-gray-800',
                        'transition duration-200 data-[closed]:opacity-0',
                        'data-[closed]:-translate-y-1',
                    )}
                    transition
                    anchor="bottom end"
                >
                    <MenuItem>
                        {({ focus }) => (
                            <button
                                className={`${focus ? 'bg-primary/5 dark:bg-gray-800' : ''} block w-full px-4 py-2 text-left text-sm text-gray-900 active:bg-primary/10 dark:text-white`}
                                onClick={onMyProfileClick}
                            >
                                {strings.my_profile}
                            </button>
                        )}
                    </MenuItem>
                    <MenuItem>
                        {({ focus }) => (
                            <button
                                className={`${focus ? 'bg-primary/5 dark:bg-gray-800' : ''} block w-full px-4 py-2 text-left text-sm text-gray-900 active:bg-primary/10 dark:text-white`}
                                onClick={onLogoutClick}
                            >
                                {strings.Logout}
                            </button>
                        )}
                    </MenuItem>
                </MenuItems>
            </Menu>
        </div>
    );
};

export default TopBarProfile;
