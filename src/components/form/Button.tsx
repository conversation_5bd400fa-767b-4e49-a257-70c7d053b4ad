import LoadingIcon from '@icons/Loading';
import { cx } from 'classix';
import { ButtonHTMLAttributes, DetailedHTMLProps, FC } from 'react';

export interface ButtonProps extends Omit<DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, 'className'> {
    fullWidth?: boolean;
    variant?: 'filled' | 'outlined' | 'ghost';
    color?: 'primary' | 'secondary' | 'success' | 'ghost' | 'white' | 'black' | 'red';
    loading?: boolean;
    rounded?: boolean;
    className?: string;
    size?: 'normal' | 'small' | 'big';
    show?: boolean;
    loadingClassName?: string;
}

const Button: FC<ButtonProps> = ({
    children,
    variant = 'filled',
    color = 'primary',
    size = 'normal',
    fullWidth = false,
    loading = false,
    rounded = false,
    show = true,
    className: propClassName,
    loadingClassName,
    ...props
}) => {
    if (!show) {
        return <></>;
    }
    // prettier-ignore
    const className = cx(
        'relative block outline-none select-none transition-all border duration-75 font-medium ring-offset-2 whitespace-nowrap rounded-lg focus-visible:ring-2 focus-visible:ring-black disabled:text-gray-600 disabled:bg-gray-200 dark:disabled:bg-gray-700 disabled:border-gray-300 dark:disabled:border-dimGray disabled:cursor-not-allowed ',
        (variant !== 'outlined') && 'border-transparent dark:border-transparent',
        (size === 'big') && 'px-5 py-2 lg:px-6 lg:py-3',
        (size === 'normal') && 'px-3 md:px-5 py-2 md:py-2.5',
        (size === 'small') && 'px-3 py-1.5 text-sm',
        (variant === 'filled') && 'font-medium border-primary disabled:bg-gray-300 disabled:text-gray-500 active:shadow-inner',
        (variant === 'filled' && color === 'primary') && 'bg-primary hover:bg-primary/90 active:bg-primary/80 text-white',
        (variant === 'filled' && color === 'secondary') && 'bg-white hover:bg-white/90 active:bg-white text-black',
        (variant === 'outlined' && color === 'primary') && 'border-primary/50 dark:border-primaryLight/20',
        (variant === 'outlined' && color !== 'primary') && 'border-primary/20 dark:border-primaryLight/20',
        (variant === 'outlined') && 'dark:border-gray-800 text-black dark:text-white active:bg-gray-300 hover:bg-gray-200 dark:active:bg-primaryLight/20 dark:hover:bg-primaryLight/10 ',
        (variant === 'ghost' && color === 'primary') && 'bg-primary/[12%] text-primary dark:text-white dark:bg-darkGray hover:bg-primary/20 dark:hover:bg-darkGray/70 active:bg-primary/30 dark:active:bg-primaryLight/30 ',
        (variant === 'ghost' && color === 'success') && 'bg-green-500/[18%] text-green-600 hover:bg-green-500/20 active:bg-green-500/30 dark:bg-green-400/[12%] dark:text-green-500 dark:hover:bg-green-500/20 dark:active:bg-green-500/30',
        (variant === 'filled' && color === 'success') && 'bg-cyangreen text-white hover:bg-cyangreen/90 active:bg-cyangreen/80 ',
        (variant === 'ghost' && color === 'secondary') && 'bg-gray-500/[12%] text-gray-600 hover:bg-gray-500/20 active:bg-gray-500/30 ',
        (variant === 'ghost' && color === 'white') && 'bg-white text-gray-600 hover:bg-gray-500/20 active:bg-gray-500/30 ',
        (variant === 'ghost' && color === 'ghost') && 'bg-transparent text-gray-500 dark:text-gray-400 hover:bg-primary/10 dark:hover:text-primaryLight hover:text-primary active:text-primary active:bg-primary/20 ',
        (variant === 'ghost' && color === 'black') && 'bg-transparent text-black dark:text-white hover:bg-primary/10 dark:hover:text-primaryLight hover:text-primary active:text-primary active:bg-primary/20 ',
        (variant === 'ghost' && color === 'red') && 'bg-warning/15 text-mediumRed  hover:bg-warning/20 hover:text-mediumRed  active:text-warning active:bg-warning/20 dark:text-red-400',
        (fullWidth) && 'w-full',
        (loading) && '!text-transparent',
        (rounded) && '!rounded-full',
        propClassName,
    );

    const loaderClassName = cx(
        'absolute left-0 top-0 flex h-full w-full select-none items-center justify-center text-lg',
        variant === 'filled' && 'text-white',
        variant === 'outlined' && 'rounded-full text-black',
        variant === 'ghost' && 'rounded-full text-black',
        loadingClassName,
    );

    return (
        <button className={className} {...props} disabled={props.disabled || loading}>
            {loading && (
                <div className={loaderClassName}>
                    <LoadingIcon className="h-5 w-5" />
                </div>
            )}
            <div className="flex items-center justify-center">{children}</div>
        </button>
    );
};

export default Button;
