import { generateUserFullName } from '@/helper';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import useMatchMultipleRoutes from '@hooks/useMatchMultipleRoutes';
import { usePaginationSWR, usePaginationSWRResponse } from '@hooks/usePaginationSWR';
import { ClientGeneralNotesPaginatedResponse } from '@interface/common';
import { GeneralNote } from '@interface/model/generalNote';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { FC, PropsWithChildren, createContext, lazy, useContext, useState } from 'react';

const ClientGeneralNoteModal = lazy(() => import('@components/Clients/Client/GeneralNotes/ClientGeneralNoteModal'));
const ClientGeneralNoteSignModal = lazy(() => import('@components/Clients/Client/GeneralNotes/ClientGeneralNoteSignModal'));
const ClientGeneralNoteViewModal = lazy(() => import('@components/Clients/Client/GeneralNotes/ClientGeneralNoteViewModal'));
const ClientGeneralNoteDeleteModal = lazy(() => import('@components/Clients/Client/GeneralNotes/ClientGeneralNoteDeleteModal'));
const ClientRecordsCancelModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelModal'));
const ClientRecordsCancelNoteViewModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelNoteViewModal'));

interface IContext extends usePaginationSWRResponse<ClientGeneralNotesPaginatedResponse, Error> {
    selectedGeneralNote: GeneralNote | undefined;
    setSelectedGeneralNote: React.Dispatch<React.SetStateAction<GeneralNote | undefined>>;
    openGeneralNote: boolean;
    setopenGeneralNote: React.Dispatch<React.SetStateAction<boolean>>;
    openGeneralNoteSignModal: boolean;
    setOpenGeneralNoteSignModal: React.Dispatch<React.SetStateAction<boolean>>;
    openGeneralNoteViewModal: boolean;
    setOpenGeneralNoteViewModal: React.Dispatch<React.SetStateAction<boolean>>;
    openGeneralNoteDeleteModal: boolean;
    setOpenGeneralNoteDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
    openGeneralNoteCancelModal: boolean;
    setOpenGeneralNoteCancelModal: React.Dispatch<React.SetStateAction<boolean>>;
    openGeneralNoteCancelNoteViewModal: boolean;
    setOpenGeneralNoteCancelNoteViewModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const MainCardGeneralNoteContext = createContext<IContext>({} as IContext);

const MainCardGeneralNoteProvider: FC<PropsWithChildren> = ({ children }) => {
    const { clientId, mutateCounts } = useClient();

    const isClientPage = useMatchMultipleRoutes([
        'clients/:clientId',
        'settings/team/:userId/booking/:bookingId/client/:clientId',
        'calendar/booking/:bookingId/client/:clientId',
    ]);

    const [openGeneralNote, setopenGeneralNote] = useState(false);
    const [selectedGeneralNote, setSelectedGeneralNote] = useState<GeneralNote>();
    const [openGeneralNoteSignModal, setOpenGeneralNoteSignModal] = useState(false);
    const [openGeneralNoteViewModal, setOpenGeneralNoteViewModal] = useState(false);
    const [openGeneralNoteDeleteModal, setOpenGeneralNoteDeleteModal] = useState(false);
    const [openGeneralNoteCancelModal, setOpenGeneralNoteCancelModal] = useState(false);
    const [openGeneralNoteCancelNoteViewModal, setOpenGeneralNoteCancelNoteViewModal] = useState(false);

    const swr = usePaginationSWR<ClientGeneralNotesPaginatedResponse, Error>(api.clientGeneralNotes(clientId), {
        filter: 'important',
        filterType: '!=',
        filterValue: null,
        limit: isClientPage ? 3 : 20,
    });

    const generalNotesMutate = async () => {
        mutateCounts();
        swr.mutate();
    };

    return (
        <MainCardGeneralNoteContext.Provider
            value={{
                openGeneralNote,
                setopenGeneralNote,
                selectedGeneralNote,
                setSelectedGeneralNote,
                openGeneralNoteSignModal,
                setOpenGeneralNoteSignModal,
                openGeneralNoteViewModal,
                setOpenGeneralNoteViewModal,
                openGeneralNoteDeleteModal,
                setOpenGeneralNoteDeleteModal,
                openGeneralNoteCancelModal,
                setOpenGeneralNoteCancelModal,
                openGeneralNoteCancelNoteViewModal,
                setOpenGeneralNoteCancelNoteViewModal,
                ...swr,
            }}
        >
            <ModalSuspense>
                {openGeneralNote && (
                    <ClientGeneralNoteModal
                        openModal={openGeneralNote}
                        setOpenSignModal={setOpenGeneralNoteSignModal}
                        setOpenModal={setopenGeneralNote}
                        mutate={generalNotesMutate}
                        selectedGeneralNote={selectedGeneralNote}
                        setSelectedGeneralNotes={setSelectedGeneralNote}
                    />
                )}
                {openGeneralNoteViewModal && (
                    <ClientGeneralNoteViewModal
                        openModal={openGeneralNoteViewModal}
                        handleClose={() => {
                            setOpenGeneralNoteViewModal(false);
                            setSelectedGeneralNote(undefined);
                        }}
                        selectedGeneralNote={selectedGeneralNote}
                    />
                )}
                {openGeneralNoteSignModal && (
                    <ClientGeneralNoteSignModal
                        openModal={openGeneralNoteSignModal}
                        setOpenModal={setOpenGeneralNoteSignModal}
                        clientMutate={mutateCounts}
                        mutate={generalNotesMutate}
                        selectedGeneralNote={selectedGeneralNote}
                    />
                )}
                {openGeneralNoteDeleteModal && (
                    <ClientGeneralNoteDeleteModal
                        openModal={openGeneralNoteDeleteModal}
                        mutate={generalNotesMutate}
                        handleCloseModal={() => setOpenGeneralNoteDeleteModal(false)}
                        selectClientNote={selectedGeneralNote}
                    />
                )}
                {openGeneralNoteCancelModal && (
                    <ClientRecordsCancelModal
                        openModal={openGeneralNoteCancelModal}
                        setOpenModal={setOpenGeneralNoteCancelModal}
                        mutate={generalNotesMutate}
                        url={api.generalNoteUpdate.replace(':generalNoteId', selectedGeneralNote?.id.toString() || '')}
                    />
                )}
                {openGeneralNoteCancelNoteViewModal && (
                    <ClientRecordsCancelNoteViewModal
                        openModal={openGeneralNoteCancelNoteViewModal}
                        handleModelClose={() => setOpenGeneralNoteCancelNoteViewModal(false)}
                        cancelledNoteDetails={selectedGeneralNote?.cancel_note}
                        cancelNoteDate={selectedGeneralNote?.cancelled_at}
                        cancelByuUserName={generateUserFullName(selectedGeneralNote?.cancelled_by)}
                    />
                )}
            </ModalSuspense>
            {children}
        </MainCardGeneralNoteContext.Provider>
    );
};

export const useMainCardGeneralNote = () => {
    const context = useContext(MainCardGeneralNoteContext);
    if (!context) {
        throw new Error('useMainCardGeneralNote must be used within a MainCardGeneralNoteProvider');
    }
    return context;
};

export default MainCardGeneralNoteProvider;
