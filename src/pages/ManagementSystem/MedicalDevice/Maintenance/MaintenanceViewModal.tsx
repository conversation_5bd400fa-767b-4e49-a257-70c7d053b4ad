import { getUrlExtension } from '@/helper';
import IconButton from '@components/form/IconButton';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import { MaintenanceDocument } from '@interface/model/medicalDevice';
import strings from '@lang/Lang';
import DownloadIcon from '@partials/Icons/Download';
import EyeIcon from '@partials/Icons/Eye';
import Modal from '@partials/MaterialModal/Modal';
import { useState } from 'react';
import { ViewFileModel } from '../MedicalDeviceViewModal';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import MedicalDeviceFileViewModal from '../FileViewModal';

export interface MaintenanceViewModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    maintenance: MaintenanceDocument;
}

const MaintenanceViewModal: React.FC<MaintenanceViewModalProps> = ({ openModal, setOpenModal, maintenance }) => {
    const handleModelClose = async () => {
        setOpenModal(false);
    };
    const [downloading, setDownloading] = useState<string | undefined>();
    const [fileViewOpen, setFileViewOpen] = useState<boolean>(false);
    const [viewFile, setViewFile] = useState<ViewFileModel>();


    return (
        <>
            <ModalSuspense>
                {fileViewOpen && !!viewFile && (
                    <MedicalDeviceFileViewModal
                        openModal={fileViewOpen}
                        onClose={() => {
                            setViewFile(undefined);
                            setFileViewOpen(false);
                        }}
                        file_name={viewFile.name}
                        file={viewFile.url}
                    />
                )}
            </ModalSuspense>
            <Modal open={fileViewOpen ? false : openModal} title={strings.files} handleClose={handleModelClose} closeOnBackdropClick={false}>
                <div className="p-4">
                    {!!maintenance.protocol_path?.length ? (
                        <div className="flex w-full gap-x-3 py-2">
                            <p className="flex-grow break-all text-sm uppercase text-primary dark:text-primaryLight">{strings.upload_protocal}</p>
                            <div className="flex">
                                <IconButton
                                    className="focus:outline-none"
                                    onClick={() => {
                                        setFileViewOpen(true);
                                        setViewFile({
                                            name: maintenance.title,
                                            url: api.storageUrl(maintenance.protocol_path ?? ''),
                                        });
                                    }}
                                    children={<EyeIcon />}
                                />
                                <IconButton
                                    className="focus:outline-none"
                                    onClick={() => onDownloadClick(maintenance.protocol_path ?? '', 'protocol_paths')}
                                >
                                    {downloading === 'protocol_paths' ? <LoadingIcon /> : <DownloadIcon />}
                                </IconButton>
                            </div>
                        </div>
                    ) : (
                        strings.no_data
                    )}
                </div>
            </Modal>
        </>
    );

    async function onDownloadClick(file: string, name: string) {
        setDownloading(name);

        const response = await fetch(api.storageUrl(file));

        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);

        a.target = '_blank';

        const ext = getUrlExtension(file) ?? '';

        a.setAttribute('download', `${name}.${ext}`);
        a.click();

        setDownloading(undefined);
    }
};

export default MaintenanceViewModal;
