import { generateUserFullName } from '@/helper';
import useAuth from '@hooks/useAuth';
import useTheme from '@hooks/useTheme';
import useTranslation from '@hooks/useTranslation';
import RoboChatIcon from '@icons/RoboChat';
import { Helmet } from 'react-helmet-async';
export interface ChatbotProviderProps {}

const ChatbotProvider: React.FC<ChatbotProviderProps> = () => {
    const [lang] = useTranslation();
    const { user } = useAuth();
    const { isDark } = useTheme();
    return (
        <>
            <Helmet>
                <script
                    id="mdq-chat-widget"
                    data-project-id="meridiq"
                    type="text/javascript"
                    src={import.meta.env.REACT_APP_CHATBOT_SCRIPT_URL}
                    async
                    defer
                ></script>
            </Helmet>
            <button
                className="fixed bottom-4 right-4 z-50 flex size-9 cursor-pointer items-center justify-center rounded-full bg-primary text-white shadow-md md:size-10 lg:bottom-6 lg:right-6"
                onClick={async () => {
                    const id = user?.id ? await sha256(user?.id) : undefined;
                    window.Chatbot.openChat({ id: id, lang: lang, name: generateUserFullName(user), theme: isDark ? 'dark' : 'light' });
                }}
            >
                <RoboChatIcon className="text-3xl text-white" />
            </button>
        </>
    );
};

async function sha256(input: string | number): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(input.toString());
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
}

interface Chatbot {
    is_open: boolean;
    info?: Info;
    openChat: (info: Info) => void;
    closeChat: () => void;
}

type Info = {
    id?: string | number;
    name?: string;
    lang?: 'en' | 'sv';
    theme?: 'light' | 'dark';
};

declare global {
    interface Window {
        Chatbot: Chatbot;
    }
}

export default ChatbotProvider;
