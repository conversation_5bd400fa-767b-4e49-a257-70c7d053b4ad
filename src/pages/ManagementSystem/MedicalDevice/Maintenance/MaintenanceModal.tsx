import Button from '@components/form/Button';
import Input from '@components/form/Input';
import Label from '@components/form/Label';
import api from '@configs/api';
import strings from '@lang/Lang';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import CancelButton from '@partials/MaterialButton/CancelButton';
import File from '@partials/MaterialFile/File';
import Modal from '@partials/MaterialModal/Modal';
import { Formik, FormikErrors } from 'formik';
import * as React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import { heic2convert } from '../../../../helper';

export interface IMaintenanceValues {
    title: string;
    upload_protocal?: string;
    server?: string;
}
export interface MaintenanceModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
    mutate: () => Promise<any>;
    medicalId?:string;
}

const MaintenanceModal: React.FC<MaintenanceModalProps> = ({ openModal, setOpenModal, mutate,medicalId }) => {
    const navigate = useNavigate();

    return (
        <Formik<IMaintenanceValues>
            initialValues={{
                title: '',
                upload_protocal: '',
            }}
            enableReinitialize
            validate={validateMedicalDevice}
            onSubmit={async (values, { resetForm, setSubmitting, setFieldError }) => {
                const formData = new FormData();
                formData.set('title', values.title);
                if (values?.upload_protocal) {
                    formData.append('protocol_path', values?.upload_protocal);
                }
                const response = await fetch(api.management_device.maintenance.store(medicalId), {
                    method: 'POST',
                    headers: {
                        Accept: 'application/json',
                        
                        
                        'X-App-Locale': strings.getLanguage(),
                    },
                    credentials: 'include',
                    body: formData,
                });

                const data = await response.json();

                if (response.status === 401) {
                    navigate('/');
                }

                if (data.status === '1') {
                    await mutate();
                    await resetForm();
                    toast.success(data.message);
                    setOpenModal(false);
                } else {
                    setFieldError('server', data.message || 'server error, please contact admin.');
                }
                setSubmitting(false);
            }}
        >
            {({
                errors,
                resetForm,
                values,
                touched,
                dirty,
                handleSubmit,
                setFieldTouched,
                setFieldValue,
                isSubmitting,
                isValidating,
                handleBlur,
                handleChange,
            }) => {
                const handleClose = async () => {
                    if (isSubmitting || isValidating) return;
                    setOpenModal(false);
                    await resetForm();
                };
                return (
                    <Modal
                        open={openModal}
                        title={`${strings.add + ' ' + strings.maintenance_doc}`}
                        handleClose={handleClose}
                        cancelButton={<CancelButton disabled={isSubmitting} onClick={handleClose} />}
                        submitButton={
                            <Button
                                loading={isSubmitting}
                                onClick={() => {
                                    if (!dirty) {
                                        toast.success(strings.no_data_changed);
                                        handleClose();
                                        return;
                                    }
                                    if (handleSubmit) return handleSubmit();
                                }}
                            >
                                {strings.Submit}
                            </Button>
                        }
                    >
                        <FormikErrorFocus />
                        <div className="space-y-4 p-4">
                            <Input
                                name="title"
                                label={strings.TITLE}
                                placeholder={strings.TITLE}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.title}
                                required
                                error={touched?.title && errors.title}
                            />
                            <div>
                                <Label label={strings.upload_protocal} />
                                <File
                                    onChange={async (e) => {
                                        let file: File | Blob | null = e?.target?.files ? e.target.files[0] : null;
                                        if (file && !file.type) {
                                            file = await heic2convert(file);
                                        }

                                        await setFieldTouched('upload_protocal');
                                        setFieldValue('upload_protocal', e?.target?.files ? e.target.files[0] : null);
                                    }}
                                    multiple
                                    id="upload_protocal"
                                    name="upload_protocal"
                                />
                            </div>
                            <ServerError className="mt-4" error={errors?.server} />
                        </div>
                    </Modal>
                );
            }}
        </Formik>
    );
};
function validateMedicalDevice(values: IMaintenanceValues): void | object | Promise<FormikErrors<IMaintenanceValues>> {
    let errors: FormikErrors<IMaintenanceValues> = {};
    if (!values.title) {
        errors.title = strings.title_is_required;
    }
    return errors;
}
export default MaintenanceModal;
