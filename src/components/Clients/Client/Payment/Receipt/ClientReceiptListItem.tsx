import Badge from '@components/form/Badge';
import { ChipVarient } from '@components/form/Chip';
import IconButton from '@components/form/IconButton';
import useAuth from '@hooks/useAuth';
import { Receipt } from '@interface/model/receipt';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import * as React from 'react';

export interface ClientReceiptListItemProps {
    receipt: Receipt;
    onViewClick?: () => void;
}

const ClientReceiptListItem: React.FC<ClientReceiptListItemProps> = ({ receipt, onViewClick = () => {} }) => {
    const { withCurrency } = useAuth();
    const { renderDate } = useHours();

    return (
        <>
            <tr className={`alternate-tr-mobile md:hidden`}>{mobileListItem()}</tr>
            <tr className={`alternate-tr-desktop hidden md:table-row`}>{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <p>{receipt.viva_receipt_id ? `#${receipt.viva_receipt_id}` : ''}</p>
                </Table.Td>
                <Table.Td>
                    <p>{renderDate(receipt.created_at)}</p>
                </Table.Td>
                <Table.Td>
                    <p>{withCurrency(receipt.total_formatted)}</p>
                </Table.Td>
                <Table.Td>
                    <p>{receipt.payment_mode}</p>
                </Table.Td>
                <Table.Td>
                    <div className="flex items-center justify-start gap-2">
                        <Badge
                            varient={
                                cx(
                                    receipt.status === 'PAID' && 'success',
                                    (receipt.status === 'CANCELLED' || receipt.status === 'ABORTED') && 'error',
                                    receipt.status === 'PROCESSING' && 'info',
                                    (receipt.status === 'REFUNDED' || receipt.status === 'PARTIALLY_REFUNDED') && 'warning',
                                ) as ChipVarient
                            }
                        >
                            {pos_strings.getString(`receipt.status.${receipt?.status?.toLowerCase()}`)}
                        </Badge>
                    </div>
                </Table.Td>
                <Table.Td className="p-2">
                    <div className="flex items-center justify-end space-x-1.5">
                        <IconButton onClick={onViewClick} name={strings.View}>
                            <EyeIcon />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }

    function mobileListItem() {
        return (
            <Table.Td>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.id} :`}</span>
                    <span className="ml-1">#{receipt.viva_receipt_id}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.time} :`}</span>
                    <span className="ml-1">{renderDate(receipt.created_at)}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.value} :`}</span>
                    <span className="ml-1">{receipt.paid_amount_formatted ? withCurrency(receipt.paid_amount_formatted) : ''}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.receipt.payment_by} :`}</span>
                    <span className="ml-1">{receipt?.payment_mode}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${strings.status} :`}</span>
                    <span className="ml-1 flex items-center justify-start gap-2">
                        <Badge
                            varient={
                                cx(
                                    receipt.status === 'PAID' && 'success',
                                    (receipt.status === 'CANCELLED' || receipt.status === 'ABORTED') && 'error',
                                    receipt.status === 'PROCESSING' && 'info',
                                    (receipt.status === 'REFUNDED' || receipt.status === 'PARTIALLY_REFUNDED') && 'warning',
                                ) as ChipVarient
                            }
                        >
                            {pos_strings.getString(`receipt.status.${receipt?.status?.toLowerCase()}`)}
                        </Badge>
                    </span>
                </p>
                <IconButton className="-ml-2" onClick={onViewClick} name={strings.View}>
                    <EyeIcon />
                </IconButton>
            </Table.Td>
        );
    }
};

export default ClientReceiptListItem;
