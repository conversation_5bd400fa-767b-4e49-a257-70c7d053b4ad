import ContactUs from "@components/BookingPortal/ContactUs";
import Button from "@components/form/Button";
import AnchorLink from "@components/link/AnchorLink";
import api from "@configs/api";
import useAuth from "@hooks/useAuth";
import strings from "@lang/Lang";
import Modal from "@partials/MaterialModal/Modal"
import { useNavigate } from "react-router";
import { toast } from "react-toastify";

export interface EmailVerificationModalProps {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>
}
const EmailVerificationModal: React.FC<EmailVerificationModalProps> = ({ open, setOpen }) => {
    const navigate = useNavigate();
    const {mutate}=useAuth();
    async function logout() {
        try {
            const response = await fetch(api.logout, {
                method: "GET",
                headers: {
                    "Accept": 'application/json',
                    
                    
                    'X-App-Locale': strings.getLanguage(),
                },
                credentials: "include",
            });

            const data = await response.json();

            if (response.status === 401) {
                toast.error(data.message || "Unauthorized", {});
            }
            await mutate();
            await navigate('/login')
        } catch (ex) {}
    }

    return (
        <Modal open={open} title={strings.email_verification} handleClose={async()=>{await logout(); await setOpen(false)}}
            submitButton={
                <Button>
                    <a
                        href={`mailto:<EMAIL>`}
                        rel="noopener noreferrer"
                        target="_blank"
                        onClick={async()=>{await logout(); await setOpen(false)}}

                    >
                        {strings.contact_us}
                    </a>
                </Button>
            }
        >
            <p className="p-4">{strings.meridiq_email_verification}</p>
        </Modal>
    )
}

export default EmailVerificationModal