import Button from '@components/form/Button';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import OpenWindowIcon from '@icons/OpenWindow';
import { CommonModelResponse } from '@interface/common';
import strings from '@lang/Lang';
import { BankIdResponse, BankIdSuccessResponse } from '@partials/MaterialModal/BankIdModal';
import qrcodegen from 'nayuki-qr-code-generator';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import useSWRImmutable from 'swr/immutable';

export interface BankIdProps {
    onSuccess: (res: BankIdSuccessResponse) => void;
    onClose: () => void;
}
const rand = () => Math.random().toString(36).substring(2);

const BankIdComponent: React.FC<BankIdProps> = ({ onSuccess, onClose }) => {
    const ref = useRef(rand());
    const { data, error, isLoading } = useSWRImmutable<CommonModelResponse<BankIdResponse>>(api.userBankId.start + `?uuid=${ref.current}`);

    const isDataLoading = !data && !error;
    const [qr, setQR] = useState<ReactNode>();
    const [stopped, setStopped] = useState<ReactNode>();

    const generateQR = useCallback(async () => {
        if (isDataLoading || error || !data || stopped) return;
        checkScanedApiCall();

        const res = await qrGenerateApiCall();
        setQR(string2QR_SVG(res.data));

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data, error, isDataLoading, stopped]);

    useEffect(() => {
        const interval = setInterval(generateQR, 1000);

        return () => {
            clearInterval(interval);
        };
    }, [generateQR]);

    const stoppedComponent = (
        <div className="flex flex-col items-center gap-4">
            <p>{stopped}</p>
            <Button
                size="small"
                variant="ghost"
                onClick={() => {
                    ref.current = rand();
                    setStopped(undefined);
                    setQR(undefined);
                }}
            >
                <p>{strings.restart}</p>
            </Button>
        </div>
    );
    const completedComponent = (
        <div className="flex flex-col items-center gap-4">
            <LoadingIcon />
            <p>{stopped}</p>
        </div>
    );

    return (
        <>
            <p>{strings.bank_id_modal_title}</p>
            <p>{strings.bank_id_modal_title_2}</p>

            <div className="my-8">
                {/* This will render component on stopped (stopped is component) */}
                {!!stopped && stopped}
                {!stopped &&
                    (isLoading || !qr ? (
                        <div className="flex items-center space-x-3">
                            <LoadingIcon className="mx-auto" />
                            <p>{strings.preparing}...</p>
                        </div>
                    ) : (
                        <>
                            <div className="max-h-xs mx-auto mb-6 h-full w-full max-w-xs rounded border p-3 text-black dark:text-white">{qr}</div>
                            <a href={api.bankIDVerificationURL(data?.data.auto_start_token)}>
                                <Button rounded color="primary" variant="ghost">
                                    <div className="flex items-center space-x-4">
                                        <p>{strings.bank_id_open_msg}</p>
                                        <OpenWindowIcon className="text-sm" />
                                    </div>
                                </Button>
                            </a>
                        </>
                    ))}
            </div>
        </>
    );

    async function qrGenerateApiCall(): Promise<CommonModelResponse<string>> {
        return await (
            await fetch(api.userBankId.qr, {
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    start_secret: data!.data.start_secret,
                    start_time: data!.data.start_time,
                    start_token: data!.data.start_token,
                }),
            })
        ).json();
    }

    async function checkScanedApiCall() {
        const res = await (
            await fetch(api.userBankId.check(data!.data.order_ref), {
                method: 'GET',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
            })
        ).json();

        if (res.status === '0') {
            setStopped(stoppedComponent);
        }
        if (res.status === '1') {
            onSuccess(res.data);
            setStopped(completedComponent);
        }
        if (res.status === '5') {
            setStopped(completedComponent);
            toast.error(res.message || strings.please_contact_support_for_assistance);
            onClose();
        }
    }
};

export function string2QR_SVG(data: string): ReactNode {
    const QRC = qrcodegen.QrCode;
    const qr0 = QRC.encodeText(data, QRC.Ecc.LOW);
    return toSvgString(qr0);
}
function toSvgString(qr: qrcodegen.QrCode): ReactNode {
    const parts: Array<string> = [];
    for (let y = 0; y < qr.size; y++) {
        for (let x = 0; x < qr.size; x++) {
            if (qr.getModule(x, y)) parts.push(`M${x},${y}h1v1h-1z`);
        }
    }
    return (
        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox={`0 0 ${qr.size} ${qr.size}`} stroke="none">
            <path d={`${parts.join(' ')}`} fill="currentColor" />
        </svg>
    );
}

export default BankIdComponent;
