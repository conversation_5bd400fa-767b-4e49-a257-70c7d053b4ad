import { generateClientFullName } from '@/helper';
import Badge from '@components/form/Badge';
import Chip, { ChipVarient } from '@components/form/Chip';
import IconButton from '@components/form/IconButton';
import useAuth from '@hooks/useAuth';
import { GiftCard } from '@interface/model/giftCard';
import { Receipt } from '@interface/model/receipt';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';
import cx from 'classix';
import * as React from 'react';

export interface ClientGiftCardListItemProps {
    giftItem: GiftCard;
    onViewClick?: () => void;
}

const ClientGiftCardListItem: React.FC<ClientGiftCardListItemProps> = ({ giftItem, onViewClick = () => {} }) => {
    const { renderDate } = useHours();
    const { withCurrency } = useAuth();

    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className={`alternate-tr-desktop hidden rounded-none md:table-row`}>{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>
                    <p>{giftItem.gift_code?.padStart(5, '0')}</p>
                </Table.Td>
                <Table.Td>
                    <Chip
                        varient={
                            cx(
                                giftItem.status === 'active' && 'success',
                                giftItem.status === 'in_active' && 'info',
                                giftItem.status === 'expired' && 'error',
                            ) as ChipVarient
                        }
                    >
                        {cx(
                            giftItem.status === 'active' && strings.Active,
                            giftItem.status === 'in_active' && strings.InActive,
                            giftItem.status === 'expired' && strings.expired,
                        )}
                    </Chip>
                </Table.Td>
                <Table.Td>
                    <p>
                        {`${renderDate(giftItem.created_at)} ${strings.to.toLocaleLowerCase()}`} {`${renderDate(giftItem.expired_at)}`}
                    </p>
                </Table.Td>
                <Table.Td>
                    <p>{`${withCurrency(giftItem.current_value ?? '0')} / ${withCurrency(giftItem.initial_value ?? '')}`}</p>
                </Table.Td>
                <Table.Td>
                    <div className="flex justify-end">
                        {onViewClick ? (
                            <IconButton onClick={onViewClick} name={strings.View}>
                                <EyeIcon />
                            </IconButton>
                        ) : (
                            <></>
                        )}
                    </div>
                </Table.Td>
            </>
        );
    }

    function mobileListItem() {
        return (
            <Table.Td className="space-y-0.5 p-4">
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.gift_card.card_number} :`}</span>
                    <span className="ml-1">{giftItem.gift_code?.padStart(5, '0')}</span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.gift_card.status} :`}</span>
                    <span className="ml-1">
                        <Chip
                            varient={
                                cx(
                                    giftItem.status === 'active' && 'success',
                                    giftItem.status === 'in_active' && 'info',
                                    giftItem.status === 'expired' && 'error',
                                ) as ChipVarient
                            }
                        >
                            {cx(
                                giftItem.status === 'active' && strings.Active,
                                giftItem.status === 'in_active' && strings.InActive,
                                giftItem.status === 'expired' && strings.expired,
                            )}
                        </Chip>
                    </span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.gift_card.validity} :`}</span>
                    <span className="ml-1">
                        {`${renderDate(giftItem.created_at)} ${strings.to.toLocaleLowerCase()}`} {`${renderDate(giftItem.expired_at)}`}
                    </span>
                </p>
                <p className={`flex items-center`}>
                    <span className="font-semibold">{`${pos_strings.gift_card.remaining} :`}</span>
                    <span className="ml-1">{`${withCurrency(giftItem.current_value ?? '0')} / ${withCurrency(giftItem.initial_value ?? '')}`}</span>
                </p>
                <IconButton className="-ml-2" onClick={onViewClick} name={strings.View}>
                    <EyeIcon />
                </IconButton>
            </Table.Td>
        );
    }
};

export default ClientGiftCardListItem;
