import Avatar from '@components/avatar/Avatar';
import IconButton from '@components/form/IconButton';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import LoadingIcon from '@icons/Loading';
import Button from '@components/form/Button';
import React, { lazy, useState } from 'react';
import { generateUserFullName, userRole } from '../../helper';
import useAuth from '../../hooks/useAuth';
import strings from '../../lang/Lang';
import EditIcon from '../../partials/Icons/Edit';
import Card from '../../partials/Paper/PagePaper';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import MailIcon from '@partials/Icons/Mail';

const MyProfileChangePassword = lazy(() => import('./MyProfileChangePassword'));
const MyProfileEditModal = lazy(() => import('./MyProfileEditModal'));

export interface MyProfileInfoProps {}

const MyProfileInfo: React.FC<MyProfileInfoProps> = () => {
    const { user, mutate } = useAuth();
    const [openEditModal, setOpenEditModal] = useState(false);
    const [openChangePassword, setOpenChangePassword] = useState(false);

    if (!user) {
        return <MyProfileInfoSkeleton />;
    }

    return (
        <Card>
            <ModalSuspense>
                {openEditModal && (
                    <MyProfileEditModal mutate={mutate} selectedUser={user} openModal={openEditModal} handleClose={() => setOpenEditModal(false)} />
                )}
                {openChangePassword && <MyProfileChangePassword isOpen={openChangePassword} onClose={() => setOpenChangePassword(false)} />}
            </ModalSuspense>
            <div className="flex space-x-3">
                <Avatar src={user?.profile_photo ? api.storageUrl(user.profile_photo) : undefined} className="h-16 lg:h-20 xl:h-24" />
                <div className="flex-grow">
                    <div className="flex items-start justify-between">
                        <div className="">
                            <Heading text={generateUserFullName(user)} variant="bigTitle" />
                            <div className="flex items-center gap-2 text-center">
                                <MailIcon className="text-base text-primary dark:text-primaryLight" />
                                <p className="text-sm text-primary dark:text-primaryLight">{user?.email}</p>
                            </div>
                            <p className="text-sm capitalize text-primary dark:text-primaryLight">{userRole(user)}</p>
                        </div>
                        <IconButton onClick={() => setOpenEditModal(true)} children={<EditIcon />} name={strings.edit} />
                    </div>
                    <div className="mt-3 flex flex-wrap gap-3">
                        <Button size="small" variant={'ghost'} onClick={() => setOpenChangePassword(true)}>
                            {strings.ChangePassword}
                        </Button>
                    </div>
                </div>
            </div>
        </Card>
    );
};

function MyProfileInfoSkeleton() {
    return (
        <div className="flex w-full items-center justify-center space-x-2 py-8 lg:py-12">
            <LoadingIcon className="text-xl text-primary" />
            <p className="text-mediumGray">{strings.Loading}...</p>
        </div>
    );
}

export default MyProfileInfo;
