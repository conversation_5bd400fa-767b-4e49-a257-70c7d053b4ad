import * as React from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';
import api from '@configs/api';
import { generateClientFullName } from '../../../helper';
import { Client } from '@interface/model/client';
import strings from '@lang/Lang';
import Button from '@components/form/Button';
import DeleteModal from '@partials/MaterialModal/DeleteModal';
import PowerIcon from '@partials/Icons/Power';
import DeleteIcon from '@partials/Icons/Delete';
import GoogleRatingsDetails from '@interface/model/googleRating';

export interface GoogleRatingRemoveModalProps {
    openModal?: boolean;
    handleClose?: () => void;
    mutate: () => Promise<any>;
    googleRatingsDetails?: GoogleRatingsDetails;
}

const GoogleRatingRemoveModal: React.FC<GoogleRatingRemoveModalProps> = ({
    openModal = false,
    handleClose = () => {},
    mutate,
    googleRatingsDetails,
}) => {
    const [isSubmitting, setIsSubmitting] = React.useState(false);
    const navigate = useNavigate();

    return (
        <DeleteModal
            open={openModal}
            handleClose={() => {
                if (isSubmitting) return;
                handleClose();
            }}
            icon={<DeleteIcon />}
            text={strings.Are_you_sure_you_want_to + ' ' + strings.remove.toLowerCase() + ' ' + strings.google_ratings.toLowerCase() + '?'}
            submitButton={
                <Button
                    fullWidth
                    loading={!!isSubmitting}
                    disabled={!!isSubmitting}
                    onClick={async () => {
                        setIsSubmitting(true);
                        const response = await fetch(api.googlePlaces.storeExtra, {
                            method: 'POST',
                            headers: {
                                Accept: 'application/json',
                                
                                'X-App-Locale': strings.getLanguage(),
                                'Content-Type': 'application/json',
                            },
                            credentials: 'include',
                            body: JSON.stringify({ clear_google_place: 1}),
                        });

                        const data = await response.json();

                        if (response.status === 401) {
                            navigate('/');
                        }

                        if (data.status === '1') {
                            await mutate();
                            toast.success(data.message);
                            handleClose();
                        } else {
                            toast.error(data.message || 'server error, please contact admin.');
                        }
                        setIsSubmitting(false);

                        handleClose();
                        // if (handleSubmit) return handleSubmit();
                    }}
                >
                    {strings.Submit}
                </Button>
            }
        />
    );
};

export default GoogleRatingRemoveModal;
