import Heading from '@components/heading/Heading';
import strings from '@lang/Lang';
import { ReactNode } from 'react';

export interface ReportTitleValueCompProps {
    title: string;
    value?: ReactNode;
}

const ReportTitleValueComponent: React.FC<React.PropsWithChildren<ReportTitleValueCompProps>> = ({ title, value, children }) => {
    return (
        <div className="mb-4 flex flex-wrap items-center justify-between">
            <Heading text={title} className="" />
            {children}
            {!!value && (
                <div className="rounded-xl bg-mediumGray/10 px-4 py-2">
                    {strings.total}: <span className="font-semibold">{value}</span>
                </div>
            )}
        </div>
    );
};

export default ReportTitleValueComponent;
