import cx from 'classix';
import { useState, useEffect } from 'react';

type TimeColumn = {
    start: number;
    end: number;
    setValue: (value: string) => void;
    value: string;
    exclude?: Array<number>;
    notShowExclude?: boolean;
};

const TimeColumn = ({ start, end, setValue, value, exclude, notShowExclude }: TimeColumn) => {
    const [slecetorMove, setSlecetorMove] = useState<number>(+value ? +value : 0);

    const timeArray: (string | number)[] = [];
    for (let time = start; time <= end; time++) {
        if (notShowExclude) !exclude?.includes(time) && timeArray.push(time);
        else timeArray.push(time);
    }

    useEffect(() => {
        let prev = slecetorMove;
        if (exclude?.includes(prev)) {
            while (exclude?.includes(prev)) {
                prev = prev + 1;
                setSlecetorMove(prev);
            }
        }
    }, []);

    useEffect(() => {
        setValue(slecetorMove.toString().length === 1 ? `0${slecetorMove}` : slecetorMove.toString());
    }, [slecetorMove]);

    const controlBottom = () => {
        let prev = slecetorMove;
        if (prev !== end) {
            if (exclude?.includes(prev + 1)) {
                while (exclude?.includes(prev + 1)) {
                    if (prev + 2 > end) {
                        return setSlecetorMove(start);
                    }
                    prev = prev + 1;
                    setSlecetorMove(prev + 1);
                }
            } else {
                return setSlecetorMove(prev + 1);
            }
        } else {
            return setSlecetorMove(start);
        }
    };

    const controlTop = () => {
        let prev = slecetorMove;
        if (prev !== start) {
            if (exclude?.includes(prev - 1)) {
                while (exclude?.includes(prev - 1)) {
                    if (prev - 2 < start) {
                        return setSlecetorMove(end);
                    }
                    prev = prev - 1;
                    setSlecetorMove(prev - 1);
                }
            } else {
                return setSlecetorMove(prev - 1);
            }
        } else {
            let endnumber = end;
            if (exclude?.includes(end)) {
                while (exclude?.includes(endnumber - 1)) {
                    endnumber = endnumber - 1;
                    setSlecetorMove(endnumber - 1);
                }
            } else {
                return setSlecetorMove(end);
            }
        }
    };

    return (
        <div className={'flex flex-col items-center'}>
            <button className={'cursor-pointer transition-opacity duration-300 hover:opacity-50'} onClick={controlTop}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M19.9201 15.0499L13.4001 8.52989C12.6301 7.75989 11.3701 7.75989 10.6001 8.52989L4.08008 15.0499"
                        strokeWidth="2"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={'stroke-primary'}
                    />
                </svg>
            </button>
            <div className={'relative flex h-32 w-16 select-none flex-col items-center overflow-hidden'}>
                <div className={'absolute top-[39px] h-10 w-full rounded-lg border-8 border-primary bg-primary'} />
                <div
                    className={'flex w-full flex-col items-center pt-10 text-xl leading-10 transition-transform duration-300 ease-in-out'}
                    style={{
                        transform: `translateY(-${slecetorMove && timeArray.indexOf(slecetorMove) * 40}px)`,
                    }}
                >
                    {timeArray.map((time) => (
                        <div
                            key={time}
                            className={cx(
                                'z-[1] transition-colors duration-300',
                                +time === slecetorMove ? 'font-medium text-white' : 'text-primary opacity-50 dark:text-primaryLight',
                                exclude && exclude.includes(+time) ? 'opacity-20' : '',
                            )}
                        >
                            {time.toString().length === 1 ? `0${time}` : time}
                        </div>
                    ))}
                </div>
            </div>
            <button className={'cursor-pointer transition-opacity duration-300 hover:opacity-50'} onClick={controlBottom}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M19.9201 8.94995L13.4001 15.47C12.6301 16.24 11.3701 16.24 10.6001 15.47L4.08008 8.94995"
                        strokeWidth="2"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={'stroke-primary'}
                    />
                </svg>
            </button>
        </div>
    );
};

type TimeitProps = {
    onChange?: (value: string) => any;
    defaultValue?: string;
    minuteExclude?: Array<number>;
    hourExclude?: Array<number>;
    notShowExclude?: boolean;
};

const Timeit = ({ onChange, defaultValue, minuteExclude, hourExclude, notShowExclude }: TimeitProps) => {
    const [hour, setHour] = useState(defaultValue ? defaultValue.split(':')[0] : '00');
    const [minute, setMinute] = useState(defaultValue ? defaultValue.split(':')[1] : '00');

    useEffect(() => {
        onChange && onChange(`${hour}:${minute}`);
    }, [hour, minute]);

    return (
        <div className={'flex space-x-3'}>
            <TimeColumn notShowExclude={notShowExclude} start={0} end={23} value={hour} setValue={setHour} exclude={hourExclude} />
            <TimeColumn notShowExclude={notShowExclude} start={0} end={59} value={minute} setValue={setMinute} exclude={minuteExclude} />
        </div>
    );
};

export default Timeit;
