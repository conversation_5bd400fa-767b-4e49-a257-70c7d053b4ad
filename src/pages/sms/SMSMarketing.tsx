import SMSMarketingClientListModal from '@components/SMS/Marketing/SMSMarketingClientListModal';
import SMSMarketingFeature from '@components/SMS/Marketing/SMSMarketingFeature';
import SMSMarketingListItem from '@components/SMS/Marketing/SMSMarketingListItem';
import Skeleton from '@components/Skeleton/Skeleton';
import Card from '@components/card';
import Button from '@components/form/Button';
import Pagination from '@components/form/Pagination';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { MarketingPaginatedResponse } from '@interface/common';
import { Marketing } from '@interface/model/marketing';
import strings from '@lang/Lang';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import Table from '@partials/Table/PageTable';
import SMSMarketingProvider, { useSMSMarketing } from '@provider/SMSMarketingProvider';
import React, { useEffect } from 'react';

export interface SMSMarketingProps {}

const SMSMarketingView = React.lazy(() => import('@components/SMS/Marketing/SMSMarketingView'));
const SMSMarketing: React.FC<SMSMarketingProps> = () => {
    const { setOpenCampaignsModal, openCampaignsModal, openModal, detailsModal } = useSMSMarketing();
    const { data, mutate, page, orderBy, setPage, loading, orderDirection, handleOrder } = usePaginationSWR<MarketingPaginatedResponse, Error>(
        api.sms.marketing.list,
    );
    const [selectedMarketing, setSelectedMarketing] = React.useState<Marketing>();
    const [viewModal, setViewModal] = React.useState(false);
    const [openClientListModal, setOpenClientListModal] = React.useState(false);
    useEffect(() => {
        if (!openCampaignsModal && !openModal && !detailsModal) {
            mutate();
        }
    }, [openCampaignsModal, openModal, detailsModal]);
    return (
        <>
            <ModalSuspense>
                {viewModal && (
                    <SMSMarketingView
                        openModal={viewModal}
                        handleClose={() => setViewModal(false)}
                        selectedMarketing={selectedMarketing}
                        setClientListModal={setOpenClientListModal}
                    />
                )}
                {openClientListModal && (
                    <SMSMarketingClientListModal
                        openModal={openClientListModal}
                        handleClose={() => {
                            setViewModal(true);
                            setOpenClientListModal(false);
                        }}
                        marketingId={selectedMarketing?.id || 0}
                    />
                )}
            </ModalSuspense>
            <Card className="space-y-4">
                <div className="flex items-center justify-between">
                    <Heading text={strings.sms} />
                    {!!data?.data.length && (
                        <div className="flex gap-4">
                            <Button onClick={() => setOpenCampaignsModal(true)}>{strings.send_sms}</Button>
                        </div>
                    )}
                </div>
                <div>
                    <Table>
                        <Table.Head>
                            <Table.ThSort
                                sort={orderBy === 'created_at' && orderDirection}
                                onClick={() => handleOrder('created_at')}
                                children={strings.date_and_time}
                            />
                            <Table.ThSort
                                children={strings.name_of_message}
                                sort={orderBy === 'name' && orderDirection}
                                onClick={() => handleOrder('name')}
                            />
                            <Table.Th children={strings.send_to} />
                            <Table.Th children={strings.credit_used} />
                            <Table.Th children={strings.failed} />
                            <Table.Th children={strings.Actions} className="text-right" />
                        </Table.Head>
                        <Table.Body>
                            {loading ? <Skeletons limit={10} /> : <></>}
                            {data?.data.map((marketing, index) => (
                                <SMSMarketingListItem
                                    key={index}
                                    marketing={marketing}
                                    onViewClick={(marketing) => {
                                        setSelectedMarketing(marketing);
                                        setViewModal(true);
                                    }}
                                />
                            ))}
                            {!loading && !data?.data.length && <SMSMarketingFeature onClick={() => setOpenCampaignsModal(true)} />}
                        </Table.Body>
                    </Table>
                    <Pagination pageSize={data?.per_page} totalCount={data?.total} currentPage={page} onPageChange={(page) => setPage(page)} />{' '}
                </div>
            </Card>
        </>
    );
};

function Skeletons({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((_, index) => {
                const delay = index * 150;

                return (
                    <tr key={`loading_${index}`}>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" delay={delay} />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" delay={delay} />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" delay={delay} />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-8 w-full" delay={delay} />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}

const SMSMarketingHoC: React.FC<SMSMarketingProps> = ({ ...props }) => {
    return (
        <SMSMarketingProvider {...props}>
            <SMSMarketing {...props} />
        </SMSMarketingProvider>
    );
};

export default SMSMarketingHoC;
