import Button from '@components/form/Button';
import InfoCard from '@components/form/InfoCard';
import api from '@configs/api';
import { CommonResponse, LetterOfConsentResponse, SettingTypes } from '@interface/common';
import strings from '@lang/Lang';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import CancelButton from '@partials/MaterialButton/CancelButton';
import MaterialCheckbox from '@partials/MaterialCheckbox/MaterialCheckbox';
import Modal from '@partials/MaterialModal/Modal';
import useSettings from '@provider/SettingsProvider';
import { useMemo } from 'react';
import { ResolverError, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import useSWR from 'swr';

export interface LetterOfConsentModalProps {
    openModal: boolean;
    setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
}

interface LoCFormData {
    loc_ids: string[];
}

const RegPortalLoCSeletionModal: React.FC<LetterOfConsentModalProps> = ({ openModal, setOpenModal }) => {
    const { data, isLoading } = useSWR<LetterOfConsentResponse>(api.letterOfConsent);
    const { getSettingValue, mutate: settingsMutate } = useSettings();

    const preSelectedLoCs = getSettingValue('SHOW_LETTER_OF_CONSENT_IDS');

    const memoPreSelectedLoCs = useMemo(() => {
        try {
            if (!preSelectedLoCs) return [];
            return JSON.parse(preSelectedLoCs);
        } catch (err) {
            return [];
        }
    }, [preSelectedLoCs]);

    const {
        formState: { isSubmitting, isDirty, errors },
        handleSubmit,
        register,
        getValues,
    } = useForm<LoCFormData>({
        values: {
            loc_ids: memoPreSelectedLoCs,
        },
        resolver: validation,
    });

    async function handleClose() {
        setOpenModal(false);
    }

    return (
        <Modal
            open={openModal}
            title={strings.LettersofConsents}
            handleClose={handleClose}
            size="small"
            cancelButton={<CancelButton disabled={isSubmitting} onClick={handleClose} />}
            submitButton={
                <Button
                    loading={isSubmitting}
                    onClick={handleSubmit(async (values) => {
                        await saveSettingsV2([
                            { key: 'SHOW_LETTER_OF_CONSENT_IDS', value: JSON.stringify(values.loc_ids) },
                            { key: 'SHOW_LETTER_OF_CONSENT', value: '1' },
                        ]);

                        await settingsMutate();
                        handleClose();
                    })}
                >
                    {strings.Submit}
                </Button>
            }
            buttonChildren={<InfoCard variant="error" className="rounded-none" message={errors.loc_ids?.message} />}
        >
            <div className="flex flex-col items-start gap-2 p-4">
                {isLoading && <SectionLoading />}
                {data?.data.map((loc, index) => <MaterialCheckbox key={index} label={loc.consent_title} value={loc.id} {...register('loc_ids')} />)}
            </div>
        </Modal>
    );
};

export const validation = (values: LoCFormData): ResolverError<LoCFormData> => {
    const resolver: ResolverError<LoCFormData> = { values, errors: {} };

    if (!values.loc_ids.length) {
        resolver.errors.loc_ids = { type: 'required', message: strings.please_select_one_option };
    }

    return resolver;
};

interface SaveSettingProps {
    key: SettingTypes;
    value: '1' | '0' | string;
}

const saveSettingsV2 = async (settings: SaveSettingProps[]) => {
    const response = await fetch(api.settingStoreV2, {
        method: 'POST',
        headers: {
            Accept: 'application/json',
            'X-App-Locale': strings.getLanguage(),
            'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ settings }),
    });

    return (await response.json()) as CommonResponse;
};

export default RegPortalLoCSeletionModal;
