import Card from '@components/card';
import PaginationCompo from '@components/form/Pagination';
import Skeleton from '@components/Skeleton/Skeleton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { ClientServiceProductPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import EmptyData from '@partials/Error/EmptyData';
import Table from '@partials/Table/PageTable';
import BackToClientDashboard from '../../sections/BackToClientDashboard';
import ClientServicesListItem from './ClientServicesListItem';
import { useNavigate } from 'react-router';
import Heading from '@components/heading/Heading';
import Input from '@components/form/Input';
import SearchIcon from '@icons/Search';

const ClientServices = () => {
    const { clientId } = useClient();
    const { data, loading, page,search, setSearch, setPage, orderBy, orderDirection, handleOrder } = usePaginationSWR<ClientServiceProductPaginatedResponse, Error>(
        api.clientServiceProductReceipt({ client_id: clientId, type: 'service' }),
        {
            limit: 20,
        },
    );
    const navigate = useNavigate();

    return (
        <>
            <BackToClientDashboard />
            <Card>
            <div className="flex items-center justify-between pb-4 space-x-3">
                    <Heading text={strings.services} />
                    <Input
                        placeholder={strings.Search}
                        value={search || ''}
                        icon={<SearchIcon className="text-mediumGray" />}
                        onChange={(event) => setSearch(event.target.value)}
                    />
                </div>
                <Table>
                    <Table.Head>
                        <Table.ThSort
                            sort={orderBy === 'receipt.viva_receipt_id' && orderDirection}
                            onClick={() => {
                                handleOrder('receipt.viva_receipt_id');
                            }}
                            children={strings.order_id}
                        />
                        <Table.ThSort
                            sort={orderBy === 'name' && orderDirection}
                            onClick={() => {
                                handleOrder('name');
                            }}
                            children={strings.service_name}
                        />
                        <Table.ThSort
                            sort={orderBy === 'created_at' && orderDirection}
                            onClick={() => {
                                handleOrder('created_at');
                            }}
                            children={strings.DateTime}
                        />
                        <Table.ThSort
                            sort={orderBy === 'selling_price' && orderDirection}
                            onClick={() => {
                                handleOrder('selling_price');
                            }}
                            children={strings.price}
                        />
                        <Table.ThSort
                            sort={orderBy === 'quantity' && orderDirection}
                            onClick={() => {
                                handleOrder('quantity');
                            }}
                            children={`${strings.qty}`}
                        />
                        <Table.ThSort
                            sort={orderBy === 'total' && orderDirection}
                            onClick={() => {
                                handleOrder('total');
                            }}
                            children={`${strings.total_price}`}
                        />
                        <Table.Th
                            children={strings.status}
                        />
                        <Table.Th children={strings.Actions} className="text-right" />
                    </Table.Head>
                    <Table.Body>
                        {data?.data.map((service) => (
                            <ClientServicesListItem
                                key={service.id}
                                service={service}
                                onViewClick={() => {
                                    navigate(`/clients/${clientId}/receipt/${service.receipt_id}/detail`);
                                }}
                            />
                        ))}
                        {loading && <ClientServicetSkeleton limit={10} />}
                        {!loading && !data?.data.length && <EmptyData cardHeight="h-[60vh]" />}
                    </Table.Body>
                </Table>
                <PaginationCompo
                    pageSize={data?.per_page ?? 0}
                    totalCount={data?.total ?? 0}
                    currentPage={page}
                    onPageChange={(page) => setPage(page)}
                />
            </Card>
        </>
    );
};

function ClientServicetSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="mx-1 h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default ClientServices;
