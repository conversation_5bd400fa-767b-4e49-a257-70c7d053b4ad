import api from '@configs/api';
import { CommonModelResponse } from '@interface/common';
import { FC, lazy, useEffect, useState } from 'react';
import useSWRImmutable from 'swr/immutable';
import { PendingInvoice } from './PendingInvoiceModal';

const PendingInvoiceModal = lazy(() => import('@components/Subscription/components/PendingInvoiceModal'));

const PendingInvoiceCheck: FC = () => {
    const [pendingInvoiceModalOpen, setPendingInvoiceModalOpen] = useState(false);
    const [pendingInvoice, setPendingInvoice] = useState<PendingInvoice>();
    const { isLoading, data, mutate } = useSWRImmutable<CommonModelResponse<PendingInvoice | undefined>>(api.invoice.pending, {
        errorRetryCount: 0,
        shouldRetryOnError: false,
        revalidateOnMount: true,
        revalidateOnFocus: false,
        refreshInterval: 0,
        revalidateIfStale: false,
        refreshWhenHidden: false,
        revalidateOnReconnect: false,
        refreshWhenOffline: false,
    });

    useEffect(() => {
        if(data?.data) {
            setPendingInvoice(data.data);
            setPendingInvoiceModalOpen(true);
        } else {
            setPendingInvoice(undefined);
            setPendingInvoiceModalOpen(false);
        }
    }, [data]);

    if (pendingInvoiceModalOpen && pendingInvoice) {
        return (
            <PendingInvoiceModal
                open={pendingInvoiceModalOpen}
                invoice={pendingInvoice}
                handleClose={(isSuccess) => {
                    setPendingInvoiceModalOpen(false);
                    setPendingInvoice(undefined);
                    if(isSuccess) {
                        mutate();
                    }
                }}
            />
        );
    }

    return <></>;
};

export default PendingInvoiceCheck;
