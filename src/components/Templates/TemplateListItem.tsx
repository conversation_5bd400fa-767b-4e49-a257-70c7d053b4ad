import IconButton from '@components/form/IconButton';
import * as React from 'react';
import { Template } from '../../interfaces/model/template';
import DeleteIcon from '../../partials/Icons/Delete';
import EditIcon from '../../partials/Icons/Edit';
import PowerIcon from '../../partials/Icons/Power';
import api from '@configs/api';
import strings from '@lang/Lang';
import Table from '@partials/Table/PageTable';

export interface TemplateListItemProps {
    template: Template;
    onEditClick?: (data: Template) => void;
    onDeleteClick?: (data: Template) => void;
}

const TemplateListItem: React.FC<TemplateListItemProps> = ({ template, onEditClick = () => {}, onDeleteClick = () => {} }) => {
    return (
        <tr className="alternate-tr">
            <Table.Td>
                <div className="h-16 w-16">
                    <img
                        src={api.storageUrl(template.image)}
                        className="h-full w-full rounded-lg border bg-white object-contain dark:border-gray-700 dark:bg-dimGray"
                        alt={`client media ${template.id}`}
                    />
                </div>
            </Table.Td>
            <Table.Td>
                <p className={`break-words ${template.deleted_at ? 'text-mediumGray' : ''} `}>{template.name}</p>
            </Table.Td>
            <Table.Td className="p-2">
                <div className="flex justify-end space-x-1.5">
                    {template.is_editable ? (
                        <>
                            <IconButton onClick={() => onEditClick(template)} name={strings.edit}>
                                <EditIcon />
                            </IconButton>
                            <IconButton onClick={() => onDeleteClick(template)} name={strings.Delete}>
                                <DeleteIcon />
                            </IconButton>
                        </>
                    ) : (
                        <IconButton onClick={() => onDeleteClick(template)} name={!template.deleted_at ? strings.inactivate : strings.activate}>
                            <PowerIcon className="text-xl" slash={!template.deleted_at} />
                        </IconButton>
                    )}
                </div>
            </Table.Td>
        </tr>
    );
};

export default TemplateListItem;
