import * as React from 'react';
import { SVGProps } from 'react';
const PosAvgTransactionIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg width="1em" height="1em" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.25678 25.3698H20.7432C20.99 25.3698 21.2001 25.1651 21.2001 24.9129C21.2001 24.6607 20.99 24.456 20.7432 24.456H9.25678C9.79591 23.9205 10.6092 23.1063 10.6092 23.1063C10.7919 22.9281 10.7919 22.6385 10.6092 22.4603C10.4355 22.2821 10.1434 22.2821 9.96978 22.4603L7.83123 24.5903C7.65762 24.7685 7.65762 25.0573 7.83123 25.2364L9.96978 27.3664C10.1434 27.5445 10.4355 27.5445 10.6092 27.3664C10.7919 27.1873 10.7919 26.8985 10.6092 26.7194L9.25678 25.3698ZM14.4472 6.79906C10.3352 6.79906 7 10.1371 7 14.25C7 18.3629 10.3352 21.7009 14.4472 21.7009C18.5592 21.7009 21.9038 18.3629 21.9038 14.25C21.9038 10.1371 18.5592 6.79906 14.4472 6.79906ZM14.4472 7.71284C18.0566 7.71284 20.99 10.6415 20.99 14.25C20.99 17.8585 18.0566 20.7872 14.4472 20.7872C10.8378 20.7872 7.91378 17.8585 7.91378 14.25C7.91378 10.6415 10.8378 7.71284 14.4472 7.71284ZM14.0269 10.2678C12.7202 10.3537 11.7059 11.3533 11.7059 12.5422C11.7059 13.7748 12.8021 14.8038 14.1911 14.8038H14.8309C15.2421 14.8038 15.6352 14.9454 15.9276 15.2031C16.1926 15.4434 16.3479 15.7669 16.3479 16.1068V16.1086C16.3479 16.7958 15.6989 17.3249 14.9313 17.3249H14.2005C13.771 17.3249 13.3687 17.1768 13.0672 16.9091C12.9484 16.8077 12.8569 16.6925 12.7838 16.5673C12.6559 16.3489 12.3821 16.274 12.1628 16.3992C11.9434 16.5253 11.8704 16.8049 11.9892 17.0233C12.108 17.2317 12.2631 17.4236 12.455 17.5917C12.8753 17.9682 13.4421 18.1957 14.0269 18.2332V18.7842C14.0269 19.0364 14.2371 19.2411 14.4838 19.2411C14.7396 19.2411 14.9407 19.0364 14.9407 18.7842V18.2386C16.2382 18.2313 17.2617 17.2655 17.2617 16.1086C17.2617 16.1077 17.2617 16.1077 17.2617 16.1068C17.2617 15.5128 17.0056 14.9408 16.5304 14.5205C16.0827 14.1184 15.4706 13.89 14.8309 13.89H14.1911C13.3321 13.8891 12.6196 13.3024 12.6196 12.5431C12.6196 11.7728 13.3504 11.1751 14.2094 11.1751H14.648C15.3699 11.1751 15.9823 11.5534 16.2382 12.1081C16.3387 12.3384 16.613 12.4398 16.8414 12.3356C17.0698 12.2315 17.1704 11.961 17.0698 11.7307C16.7135 10.954 15.9093 10.3738 14.9407 10.276V9.71584C14.9407 9.46363 14.7396 9.25895 14.4838 9.25895C14.2371 9.25895 14.0269 9.46363 14.0269 9.71584V10.2678ZM19.6372 3.13024H8.16052C7.90466 3.13024 7.70363 3.33493 7.70363 3.58713C7.70363 3.83934 7.90466 4.04402 8.16052 4.04402H19.6372C19.1072 4.5795 18.2848 5.39367 18.2848 5.39367C18.1112 5.57186 18.1112 5.86152 18.2848 6.03971C18.4676 6.2179 18.7604 6.2179 18.934 6.03971L21.0627 3.90969C21.2455 3.73151 21.2455 3.44276 21.0627 3.26366L18.934 1.13364C18.7604 0.955453 18.4676 0.955453 18.2848 1.13364C18.1112 1.31274 18.1112 1.60149 18.2848 1.78059L19.6372 3.13024Z"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth={0.5}
        />
    </svg>
);
export default PosAvgTransactionIcon;
