import ViewAllButton from '@components/form/ViewAllButton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { GiftPaginatedResponse } from '@interface/common';
import pos_strings from '@lang/pos/Lang';
import EmptyData from '@partials/Error/EmptyData';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Table from '@partials/Table/PageTable';
import ClientGiftCardListItem from './ClientGiftCardListItem';
import BackToClientDashboard from '../../sections/BackToClientDashboard';
import Card from '@components/card';
import PaginationCompo from '@components/form/Pagination';
import Skeleton from '@components/Skeleton/Skeleton';
import { useNavigate } from 'react-router';
import { Refund } from '@interface/model/refund';
import Heading from '@components/heading/Heading';
import Input from '@components/form/Input';
import strings from '@lang/Lang';
import SearchIcon from '@icons/Search';

const ClientGiftCards = () => {
    const { clientId } = useClient();
    const { data, page, search, setSearch, setPage, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<GiftPaginatedResponse, Error>(
        api.pos.giftCard.list({ client_id: clientId }),
        {
            limit: 20,
        },
    );
    const navigate = useNavigate();
    return (
        <>
            <BackToClientDashboard />
            <Card>
                <div className="flex items-center justify-between pb-4 space-x-3">
                    <Heading text={pos_strings.gift_card.gift_card} />
                    <Input
                        placeholder={strings.Search}
                        value={search || ''}
                        icon={<SearchIcon className="text-mediumGray" />}
                        onChange={(event) => setSearch(event.target.value)}
                    />
                </div>
                <Table>
                    <Table.Head>
                        <Table.ThSort
                            sort={orderBy === 'gift_code' && orderDirection}
                            onClick={() => {
                                handleOrder('gift_code');
                            }}
                            children={pos_strings.gift_card.card_number}
                        />
                        <Table.ThSort
                            sort={orderBy === 'status' && orderDirection}
                            onClick={() => {
                                handleOrder('status');
                            }}
                            children={pos_strings.gift_card.status}
                        />
                        <Table.ThSort
                            sort={orderBy === 'expired_at' && orderDirection}
                            onClick={() => {
                                handleOrder('expired_at');
                            }}
                            children={pos_strings.gift_card.validity}
                        />
                        <Table.ThSort
                            sort={orderBy === 'current_value' && orderDirection}
                            onClick={() => {
                                handleOrder('current_value');
                            }}
                            children={`${pos_strings.gift_card.remaining}`}
                        />
                        <Table.Th children={pos_strings.action} className="text-right" />
                    </Table.Head>
                    <Table.Body>
                        {data?.data.map((gift) => (
                            <ClientGiftCardListItem
                                key={gift.id}
                                giftItem={gift}
                                onViewClick={() => {
                                    if (!gift.payable) return;

                                    if (gift.payable_type === 'App\\CompanyReceipt') {
                                        navigate(`/clients/${clientId}/receipt/${gift.payable.id}/detail`);
                                    }
                                    if (gift.payable_type === 'App\\CompanyReceiptRefund') {
                                        const refund = gift.payable as Refund;
                                        navigate(`/clients/${clientId}/receipt/${refund.receipt_id}/detail?refund_id=${refund.id}`);
                                    }
                                }}
                            />
                        ))}

                        {loading && <ClientGiftCardSkeleton limit={10} />}
                        {!loading && !data?.data.length && <EmptyData cardHeight="h-[60vh]" />}
                    </Table.Body>
                </Table>
                <PaginationCompo
                    pageSize={data?.per_page ?? 0}
                    totalCount={data?.total ?? 0}
                    currentPage={page}
                    onPageChange={(page) => setPage(page)}
                />
            </Card>
        </>
    );
};
function ClientGiftCardSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="mx-1 h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default ClientGiftCards;
