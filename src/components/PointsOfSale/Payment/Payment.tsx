import Card from '@components/card';
import Button from '@components/form/Button';
import ClientLazySearch from '@components/form/ClientLazySearch';
import TextArea from '@components/form/TextArea';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import { useNetworkState } from '@hooks/useNetworkState';
import { CompanyUserResponse, ReceiptResponse } from '@interface/common';
import { Client } from '@interface/model/client';
import { GiftCard } from '@interface/model/giftCard';
import { Product } from '@interface/model/product';
import { DiscountType, POSPaymentMethod, ReceiptExportType } from '@interface/model/receipt';
import { Service } from '@interface/model/service';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import ServerError from '@partials/Error/ServerError';
import FormikErrorFocus from '@partials/FormikErrorFocus/FormikErrorFocus';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { PaymentContext, PaymentProvider } from '@provider/PaymentProvider';
import { Formik } from 'formik';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { commonFetch, customRound, handleExportReceiptData, isDecimal, limitDecimalPlaces } from '../../../../src/helper';
import ItemViewComponent from '../Component/ItemViewComponent';
import SelectedTerminalViewComponent from '../Component/SelectedTerminalViewComponent';
import AddServiceOrProductModal from '../Modal/AddServiceOrProductModal';
import SelectSwishModal from '../Modal/SelectSwishModal';
import SelectTerminalModal from '../Modal/SelectTerminalModal';
import PaymentButtonComponent from './Component/PaymentButtonComponent';
import PaymentSummaryEditComponent from './Component/PaymentSummaryEditComponent';
import DiscountComponent from './DiscountComponent';
import GiftCardComponent from './GiftCardComponent';
import PaymentSuccessModal from './Modal/PaymentSuccessModal';
import { validationPayment } from './validation';
import AddIcon from '@partials/Icons/Add';
import useSettings from '@provider/SettingsProvider';
import FortnoxIcon from '@partials/Icons/Fortnox';
import VivaWalletIcon from '@partials/Icons/VivaWallet';

export interface IPaymentValues {
    gift_card: boolean;
    gift_card_id: string;
    gift_card_value: number;
    gift_card_data?: GiftCard;
    gift_card_error?: Error;

    discount_value: string;
    discount_type: DiscountType;
    discount: boolean;

    terminal_id: string;

    products: ProductItem[];
    services: ServiceItem[];

    client_id?: number;
    client?: Client;

    subTotal?: number;
    totalTax?: number;
    total?: number;
    payable?: number;

    payment_method?: POSPaymentMethod;

    related_id?: number;
    related_type?: 'booking' | 'booking_client';

    server?: string;

    note: string;
}

export interface ProductItem extends Product {
    selected_quantity?: number;
    user_id?: number;
}

export interface ServiceItem extends Service {
    selected_quantity?: number;
    user_id?: number;
}

export interface ProductList {
    label: string;
    price: number;
    quantity: number;
    is_service?: number;
    is_product?: number;
    id: number;
}

const receiptFetch = async ([url, id]: [string, string]) => {
    if (!id) return () => {};
    return commonFetch(url.replace(':id', id));
};

const receiptAbortFetch = async (url: string, { arg }: { arg: string }) => {
    return commonFetch(url.replace(':id', arg), {
        method: 'DELETE',
    });
};

const Payment = () => {
    const { withCurrency } = useAuth();
    const [addModal, setAddModal] = React.useState<boolean>(false);
    const [openTerminal, setOpenTerminal] = React.useState<boolean>(false);
    const [openSwish, setOpenSwish] = React.useState<boolean>(false);
    const navigate = useNavigate();

    const [query, setSearchParams] = useSearchParams();

    const [receiptId, setReceiptId] = React.useState<string>();
    const [dirtyReceipt, setDirtyReceipt] = React.useState(false);
    const isOnline = useNetworkState();

    const { getSettingValue } = useSettings();
    const swishQr = getSettingValue('POS_SWISH_QR');

    const [loadingExport, setLoadingExport] = React.useState<ReceiptExportType | undefined>(undefined);

    const {
        data: receiptData,
        mutate,
        isLoading: isReceiptLoading,
    } = useSWR<ReceiptResponse, Error, any>([api.pos.receipt.show(receiptId ?? ':id', { polling: true }), receiptId], receiptFetch, {
        refreshInterval: 4000,
        refreshWhenHidden: true,
        refreshWhenOffline: false,
    });

    const { data: usersData } = useSWR<CompanyUserResponse>(api.bookingPractitionersAll({ serviceIds: [] }));

    const trigger = useCallback(
        async (id: string | number) => {
            if (id !== receiptId) {
                setReceiptId(id.toString());
                return;
            }
            await mutate();
        },
        [mutate, receiptId],
    );

    const resetReceipt = useCallback(() => {
        setReceiptId(undefined);
    }, []);

    const receipt = useMemo(() => (!!receiptId && receiptData?.data ? receiptData?.data : undefined), [receiptData?.data, receiptId]);

    const {
        // data: receiptAbortData,
        trigger: abortTrigger,
        error: abortError,
        reset: resetAbort,
    } = useSWRMutation<ReceiptResponse, Error, any, string>(api.pos.payment.abort(receiptId ?? ':id'), receiptAbortFetch);
    const [loadingAbort, setLoadingAbort] = React.useState(false);

    const isProcessing = useMemo(() => receipt?.status === 'PROCESSING' || receipt?.status === 'PENDING', [receipt?.status]);

    const [openPaymentSuccess, setOpenPaymentSuccess] = React.useState(false);

    useEffect(() => {
        if (receipt?.status === 'PAID' && receiptId) {
            setOpenPaymentSuccess(true);
        }
    }, [receipt?.status, receiptId]);

    return (
        <Formik<IPaymentValues>
            initialValues={{
                client_id: 0,
                discount: false,
                discount_type: 'value',
                discount_value: '0',
                gift_card: false,
                gift_card_value: 0,
                gift_card_id: '',
                products: [],
                services: [],
                terminal_id: '',
                note: '',
            }}
            enableReinitialize
            validate={validationPayment}
            onSubmit={async (values, { setFieldError, resetForm }) => {
                resetAbort();

                const formData = new FormData();

                if (receiptId) {
                    formData.set('receipt_id', receiptId.toString());
                }

                if (values.payment_method) {
                    formData.set('payment_method', values.payment_method);
                }

                if (values.related_id && values.related_type) {
                    formData.set('related_id', values.related_id?.toString());
                    formData.set('related_type', values.related_type?.toString());
                }

                formData.set('client_id', values.client_id!.toString());

                for (const [index, product] of values.products.entries()) {
                    formData.set(`products[${index}][id]`, product.id.toString());
                    formData.set(`products[${index}][quantity]`, product.selected_quantity!.toString());
                    formData.set(`products[${index}][amount]`, product.selling_price!.toString());
                    product.user_id && formData.set(`products[${index}][user_id]`, product.user_id!.toString());
                }

                for (const [index, service] of values.services.entries()) {
                    formData.set(`services[${index}][id]`, service.id.toString());
                    formData.set(`services[${index}][quantity]`, service.selected_quantity!.toString());
                    formData.set(`services[${index}][amount]`, service.price!.toString());
                    service.user_id && formData.set(`services[${index}][user_id]`, service.user_id!.toString());
                }

                if (values.gift_card) {
                    formData.set('gift_card_id', values.gift_card_data!.id.toString());
                    formData.set('gift_card_amount', values.gift_card_value.toString());
                }

                if (values.discount) {
                    formData.set('discount_type', values.discount_type.toString());
                    formData.set('discount_value', `${values.discount_value.toString()}`);
                }

                if (!!values.terminal_id) {
                    formData.set('terminal_id', values.terminal_id);
                }

                if (values.note) {
                    formData.set('note', values.note);
                }

                const response = await fetch(receiptId && !dirtyReceipt ? api.pos.payment.retry(receiptId) : api.pos.payment.store, {
                    method: 'POST',
                    headers: {
                        Accept: 'application/json',

                        'X-App-Locale': pos_strings.getLanguage(),
                    },
                    credentials: 'include',
                    body: formData,
                });

                const data = (await response.json()) as ReceiptResponse;

                if (response.status === 401) {
                    navigate('/');
                    return;
                }

                if (data.status === '1' || data.status === '2') {
                    setDirtyReceipt(false);

                    await trigger(data.data.id.toString());

                    toast.success(data.message);

                    return;
                }

                toast.error(data.message);
                setFieldError('server', data.message || 'server error, please contact admin.');
            }}
        >
            {({ setFieldValue, setFieldTouched, handleChange, handleBlur, touched, submitForm, errors, values, isSubmitting, resetForm }) => {
                const { terminal_id, server, ...errorsWithoutTerminal } = errors ?? {};

                return (
                    <PaymentProvider
                        updatedValues={() => {
                            if (receiptId) {
                                setDirtyReceipt(true);
                            }
                        }}
                    >
                        <PaymentContext.Consumer>
                            {({ summary, product, service, terminal, discount, gift_card, viva, fortnox }) => (
                                <>
                                    <ModalSuspense>
                                        {addModal && (
                                            <AddServiceOrProductModal
                                                open={addModal}
                                                product={{
                                                    data: product.products,
                                                    selected: values.products,
                                                    loading: product.loading,
                                                    search: product.search,
                                                    setSearch: product.setSearch,
                                                }}
                                                service={{
                                                    data: service.services,
                                                    selected: values.services,
                                                    loading: service.loading,
                                                    search: service.search,
                                                    setSearch: service.setSearch,
                                                }}
                                                handleClose={() => {
                                                    if (isSubmitting) return;
                                                    setAddModal(false);
                                                }}
                                                handleSubmit={(pItems, sItems) => {
                                                    // const pIds = pItems.map((p) => p.id);
                                                    // const sIds = sItems.map((s) => s.id);
                                                    // product.set(product.products.filter((p) => pIds.includes(p.id)));
                                                    // service.set(service.services.filter((s) => sIds.includes(s.id)));
                                                    product.set(pItems);
                                                    service.set(sItems);

                                                    setAddModal(false);
                                                }}
                                            />
                                        )}
                                        {openTerminal && (
                                            <SelectTerminalModal
                                                open={openTerminal}
                                                disabled={isSubmitting}
                                                loading={isSubmitting}
                                                handleClose={() => {
                                                    if (isSubmitting) return;

                                                    setOpenTerminal(false);
                                                }}
                                                handleSubmit={async (terminal_id) => {
                                                    await setFieldValue('terminal_id', terminal_id);

                                                    setOpenTerminal(false);
                                                }}
                                                terminals={terminal.data}
                                                selected_terminal={terminal.selected}
                                                error={errors.server}
                                            />
                                        )}
                                        {openSwish && (
                                            <SelectSwishModal
                                                open={openSwish}
                                                image={swishQr ? api.storageUrl(swishQr) : undefined}
                                                loading={isSubmitting}
                                                handleClose={() => {
                                                    if (isSubmitting) return;

                                                    setOpenSwish(false);
                                                }}
                                                handleSubmit={async () => {
                                                    await setFieldValue('payment_method', 'swish');
                                                    submitForm();

                                                    setOpenSwish(false);
                                                }}
                                                amount={withCurrency(summary?.vivaPayable ?? '0')}
                                            />
                                        )}
                                        {openPaymentSuccess && (
                                            <PaymentSuccessModal
                                                open={openPaymentSuccess}
                                                amount={withCurrency(receipt?.total_formatted ?? '0')}
                                                loading={loadingExport}
                                                handleClose={async (type) => {
                                                    try {
                                                        if (type && receipt) {
                                                            setLoadingExport(type);
                                                            const data = await handleExportReceiptData({
                                                                lang: pos_strings.getLanguage(),
                                                                receipt: receipt,
                                                                title: `${pos_strings.receipt.receipt} - ${receipt.viva_receipt_id}.pdf`,
                                                                type: type,
                                                            });

                                                            if (data && data?.status === '1') {
                                                                toast.success(data?.message);
                                                            }
                                                            if (data && data?.status !== '1') {
                                                                toast.error(data?.message);
                                                            }
                                                            setLoadingExport(undefined);
                                                        }

                                                        setSearchParams(new URLSearchParams());
                                                        await resetForm();
                                                        await setDirtyReceipt(false);
                                                        await setOpenPaymentSuccess(false);
                                                        await resetReceipt();
                                                        await product.fetch();
                                                        await service.fetch();
                                                    } catch (error) {
                                                        setLoadingExport(undefined);
                                                    }
                                                }}
                                            />
                                        )}
                                    </ModalSuspense>
                                    <div>
                                        <FormikErrorFocus />
                                        <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
                                            <Card className="md:col-span-5 md:p-5 xl:col-span-2 xl:p-7">
                                                <Heading text={pos_strings.payment.payment} className="pb-5" />
                                                <div className="">
                                                    <div className="grid grid-cols-1 gap-4 xl:grid-cols-2">
                                                        {/* <Label
                                                                className="font-semibold text-primary"
                                                                label={pos_strings.add_service_product}
                                                                required={true}
                                                            /> */}
                                                        <div className="flex items-center justify-end rounded-lg border-grayishBlue bg-grayishBlue pl-3.5 text-start text-mediumGray dark:border-gray-700 dark:bg-darkGray dark:text-gray-600">
                                                            <span className={'w-full'}>{pos_strings.select_service_product}</span>
                                                            <Button
                                                                onClick={() => setAddModal(true)}
                                                                variant="ghost"
                                                                className="h-full !rounded-l-none dark:bg-primary/20 dark:hover:bg-primary/30"
                                                            >
                                                                <AddIcon />
                                                            </Button>
                                                        </div>
                                                        <div className="flex-grow">
                                                            {/* <Label label={strings.client} required /> */}
                                                            <ClientLazySearch
                                                                value={values?.client_id}
                                                                disabled={!!values.related_id}
                                                                priority_ids={values?.client_id && values.client ? [values?.client_id] : undefined}
                                                                onChange={async (client) => {
                                                                    if (!client) return;
                                                                    await setFieldTouched('client_id');
                                                                    await setFieldValue('client_id', client.id);
                                                                    await setFieldValue('client', client);
                                                                }}
                                                                priorityClient={values.client ? [values.client] : []}
                                                                error={touched?.client_id && errors.client_id}
                                                                inputProps={{ label: strings.client, required: true, placeholder: strings.client }}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h3 className="py-4 text-lg font-semibold">{pos_strings.items_added}</h3>
                                                        {values.services.length || values.products.length ? (
                                                            <table className="w-full">
                                                                <thead>
                                                                    <tr>
                                                                        <th className="hidden text-left text-sm font-normal text-lightPurpleGray lg:table-cell">
                                                                            {pos_strings.payment.items}
                                                                        </th>
                                                                        <th className="hidden text-left text-sm font-normal text-lightPurpleGray lg:table-cell">
                                                                            {strings.User}
                                                                        </th>
                                                                        <th className="hidden text-left text-sm font-normal text-lightPurpleGray lg:table-cell">
                                                                            {strings.price}
                                                                        </th>
                                                                        <th className="hidden text-left text-sm font-normal text-lightPurpleGray lg:table-cell">
                                                                            {pos_strings.payment.qty}
                                                                        </th>
                                                                        <th className="hidden text-left text-sm font-normal text-lightPurpleGray lg:table-cell">
                                                                            {pos_strings.payment.subtotal}
                                                                        </th>
                                                                    </tr>
                                                                </thead>

                                                                {values.services?.map((item, index) => {
                                                                    return (
                                                                        <tbody key={`service-${index}`}>
                                                                            <ItemViewComponent
                                                                                name={item.name}
                                                                                price={item.price}
                                                                                quantity={item.selected_quantity ?? 0}
                                                                                tax={`${item.tax_information ?? '0'}%`}
                                                                                disabled={!!(isProcessing || isSubmitting)}
                                                                                decrease={() => service.decreaseQuantity(item.id)}
                                                                                increase={() => service.increaseQuantity(item.id)}
                                                                                subtotal={withCurrency(
                                                                                    parseFloat(item.price || '0') * (item.selected_quantity ?? 1),
                                                                                )}
                                                                                onPriceEdit={(e) => {
                                                                                    if (e.target.value && !isDecimal(e.target.value)) return;
                                                                                    const value = parseFloat(e.target.value || '0');
                                                                                    if (e.target.value && value > 9999999) return;
                                                                                    const val =
                                                                                        limitDecimalPlaces(
                                                                                            e?.target?.value?.replace(/^0+/, '') || '0',
                                                                                            2,
                                                                                        ) || '0';
                                                                                    const newServicePrice = values?.services.map(
                                                                                        (item: ServiceItem, idx) =>
                                                                                            idx === index ? { ...item, price: val || '0' } : item,
                                                                                    );
                                                                                    setFieldValue('services', newServicePrice);
                                                                                }}
                                                                                userId={item.user_id}
                                                                                onUserChange={async (userId) => {
                                                                                    const newServiceUser = values?.services.map(
                                                                                        (item: ServiceItem, idx) =>
                                                                                            idx === index ? { ...item, user_id: userId } : item,
                                                                                    );
                                                                                    setFieldValue('services', newServiceUser);
                                                                                }}
                                                                                userList={usersData?.data}
                                                                            />
                                                                        </tbody>
                                                                    );
                                                                })}

                                                                {values.products?.map((item, index) => {
                                                                    return (
                                                                        <tbody key={`product-${index}`}>
                                                                            <ItemViewComponent
                                                                                name={item.name}
                                                                                price={item.selling_price}
                                                                                quantity={item.selected_quantity ?? 0}
                                                                                tax={`${item.tax_information ?? '0'}%`}
                                                                                disabled={!!(isProcessing || isSubmitting)}
                                                                                decrease={() => product.decreaseQuantity(item.id)}
                                                                                increase={() => product.increaseQuantity(item.id)}
                                                                                stock={`${pos_strings.in_stock(item.stock)}`}
                                                                                subtotal={withCurrency(
                                                                                    parseFloat(item.selling_price || '0') *
                                                                                        (item.selected_quantity ?? 1),
                                                                                )}
                                                                                onPriceEdit={(e) => {
                                                                                    if (e.target.value && !isDecimal(e.target.value)) return;
                                                                                    const value = parseFloat(e.target.value || '0');
                                                                                    if (e.target.value && value > 9999999) return;
                                                                                    const val =
                                                                                        limitDecimalPlaces(
                                                                                            e?.target?.value?.replace(/^0+/, '') || '0',
                                                                                            2,
                                                                                        ) || '0';
                                                                                    const newProducPrice = values?.products.map(
                                                                                        (item: ProductItem, idx) =>
                                                                                            idx === index
                                                                                                ? { ...item, selling_price: val || '0' }
                                                                                                : item,
                                                                                    );
                                                                                    setFieldValue('products', newProducPrice);
                                                                                }}
                                                                                userId={item.user_id}
                                                                                userList={usersData?.data}
                                                                                onUserChange={async (userId) => {
                                                                                    const newProductUser = values?.products.map(
                                                                                        (item: ProductItem, idx) =>
                                                                                            idx === index ? { ...item, user_id: userId } : item,
                                                                                    );
                                                                                    setFieldValue('products', newProductUser);
                                                                                }}
                                                                            />
                                                                        </tbody>
                                                                    );
                                                                })}
                                                            </table>
                                                        ) : (
                                                            ''
                                                        )}

                                                        {values.products.length || values.services.length ? (
                                                            <div className="mt-3 space-y-2">
                                                                <span>{pos_strings.reference_note}</span>
                                                                <TextArea
                                                                    disabled={loadingAbort || isSubmitting || isProcessing}
                                                                    name="note"
                                                                    value={values.note || ''}
                                                                    onChange={handleChange}
                                                                    onBlur={handleBlur}
                                                                    error={touched?.note && errors.note}
                                                                    placeholder={pos_strings.reference_placeholder}
                                                                />
                                                            </div>
                                                        ) : (
                                                            <></>
                                                        )}
                                                    </div>
                                                </div>
                                            </Card>
                                            <Card className="space-y-4 p-7 md:col-span-4 xl:col-span-1">
                                                <GiftCardComponent disabled={!!(isProcessing || isSubmitting)} />
                                                <hr />
                                                <DiscountComponent disabled={!!(isProcessing || isSubmitting)} />
                                                <hr />
                                                <PaymentSummaryEditComponent
                                                    subtotal={withCurrency(summary.subTotal)}
                                                    taxPercentageLevel={summary.taxPercentageLevel}
                                                    total={summary.payable}
                                                    limit={discount.limitValue}
                                                    handleDiscount={async (total) => {
                                                        const discountAmount = discount.limitValue - parseFloat(total?.toString() ?? '0');

                                                        if (
                                                            discountAmount < 0 ||
                                                            !discount.canAdd ||
                                                            discountAmount > discount.limitValue ||
                                                            isNaN(discountAmount)
                                                        )
                                                            return;

                                                        if (discountAmount === 0) {
                                                            if (discount.on) discount.switch();
                                                            if (values.discount_type !== 'value') await setFieldValue('discount_type', 'value');
                                                            await setFieldValue('discount_value', customRound(discountAmount).toString());
                                                            // await setFieldValue('percentage_discount_value', '');

                                                            return;
                                                        }

                                                        if (!discount.on) discount.switch();
                                                        if (values.discount_type !== 'value') await setFieldValue('discount_type', 'value');
                                                        await setFieldValue('discount_value', customRound(discountAmount).toString());
                                                        // await setFieldValue('percentage_discount_value', '');
                                                    }}
                                                    discount={
                                                        values.discount && values.discount_value && parseFloat(discount.total)
                                                            ? withCurrency(discount.total ?? '0')
                                                            : undefined
                                                    }
                                                    gift_card={summary.giftCard ? withCurrency(summary.giftCard) : undefined}
                                                />

                                                <SelectedTerminalViewComponent
                                                    disabled={loadingAbort || isSubmitting || isProcessing}
                                                    loading={terminal.loading}
                                                    nickname={terminal.selected?.nickname}
                                                    terminal_id={terminal.selected?.virtual_terminal_id}
                                                    show={!!terminal.selected}
                                                    onEditClick={() => {
                                                        terminal.reload();
                                                        setOpenTerminal(true);
                                                    }}
                                                />
                                                <PaymentButtonComponent
                                                    payDirect={parseFloat(summary.vivaPayable) <= 0 && !!values.gift_card_value}
                                                    showViva={viva.status === 'connected'}
                                                    showSwish={true}
                                                    showFortnox={fortnox.status === 'connected'}
                                                    disabled={
                                                        (!!errorsWithoutTerminal && !!Object.keys(errorsWithoutTerminal).length) ||
                                                        isProcessing ||
                                                        loadingAbort ||
                                                        isSubmitting ||
                                                        isReceiptLoading
                                                    }
                                                    onClickSwish={async () => {
                                                        setOpenSwish(true);
                                                    }}
                                                    onClickViva={async () => {
                                                        await setFieldValue('payment_method', 'viva');
                                                        submitForm();
                                                    }}
                                                    onClickFortnox={async () => {
                                                        await setFieldValue('payment_method', 'fortnox_invoice');
                                                        submitForm();
                                                    }}
                                                    onClickTerminal={async () => {
                                                        terminal.reload();

                                                        setOpenTerminal(true);
                                                    }}
                                                    processing={isProcessing && values.payment_method ? values.payment_method : false}
                                                    terminalLoading={isSubmitting || isProcessing || terminal.loading}
                                                    swishLoading={(isSubmitting || isReceiptLoading) && values.payment_method === 'swish'}
                                                    vivaLoading={(isSubmitting || isReceiptLoading) && values.payment_method === 'viva'}
                                                    fortnoxLoading={(isSubmitting || isReceiptLoading) && values.payment_method === 'fortnox_invoice'}
                                                    showTerminal={!values.terminal_id && terminal.required}
                                                    total={withCurrency(summary.vivaPayable ?? '0')}
                                                />
                                                <Button
                                                    variant="ghost"
                                                    fullWidth
                                                    show={!!(isProcessing && receiptId)}
                                                    loading={loadingAbort || isSubmitting}
                                                    onClick={async () => {
                                                        if (!receiptId) return;
                                                        setLoadingAbort(true);
                                                        try {
                                                            await abortTrigger(receiptId);
                                                        } catch (error) {}

                                                        try {
                                                            await trigger(receiptId);
                                                        } catch (error) {}
                                                        setLoadingAbort(false);
                                                    }}
                                                    disabled={loadingAbort || isSubmitting || !isOnline}
                                                >
                                                    {pos_strings.abort}
                                                </Button>
                                                <Button
                                                    fullWidth
                                                    size="normal"
                                                    className="!p-2"
                                                    show={viva.status !== 'connected'}
                                                    disabled={loadingAbort || isSubmitting || isProcessing || isReceiptLoading}
                                                    variant={viva.status === 'in-process' ? 'ghost' : 'filled'}
                                                    onClick={async () => {
                                                        viva.apply();
                                                    }}
                                                >
                                                    <div className="flex items-center space-x-4">
                                                        <VivaWalletIcon className="h-7 w-7" />
                                                        <div className="flex flex-col items-start">
                                                            <p>
                                                                {viva.status == 'in-process'
                                                                    ? pos_strings.viva.verification_is_currently_in_progress
                                                                    : pos_strings.viva.connect_to_vivawallet}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </Button>
                                                <Button
                                                    fullWidth
                                                    size="normal"
                                                    className="!p-2"
                                                    show={fortnox.status !== 'connected'}
                                                    disabled={loadingAbort || isSubmitting || isProcessing || isReceiptLoading}
                                                    variant="filled"
                                                    onClick={async () => {
                                                        fortnox.apply();
                                                    }}
                                                >
                                                    <div className="flex items-center space-x-4">
                                                        <FortnoxIcon className="h-7 w-7" />
                                                        <div className="flex flex-col items-start">
                                                            <p>
                                                                {pos_strings.fortnox.connect_to_fortnox}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </Button>
                                            </Card>
                                        </div>
                                        <ServerError error={errors?.server} className="mt-4" />
                                        <ServerError error={abortError?.message} className="mt-4" />
                                    </div>
                                </>
                            )}
                        </PaymentContext.Consumer>
                    </PaymentProvider>
                );
            }}
        </Formik>
    );
};
export default Payment;
