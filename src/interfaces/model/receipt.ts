import { Terminal } from './Terminal';
import { Booking } from './booking';
import { BookingClientPivot, Client } from './client';
import { Company } from './company';
import { GiftCard } from './giftCard';
import { ReceiptItem } from './receipt_item';
import { Refund } from './refund';

export interface Receipt {
    id: number;
    client_id: number;
    company_id: number;

    gift_card_id: number;
    gift_card_amount: string;

    discount_type: DiscountType;
    discount_value: string;
    discount_amount_formatted: string;

    sub_total: string;
    paid_amount: string;
    paid_amount_formatted: string;
    total_formatted: string;

    total_tax_information: string;
    total_tax_information_formatted: string;

    refund_amount: string;
    refund_amount_formatted: number;

    remaining_amount: number;
    gift_card_remaining_amount: number;
    gift_card_refund_amount?: string;

    paid_for_gift_card_exists?: boolean;

    status: ReceiptStatus;

    transaction_id: string;
    session_id: string;
    viva_receipt_id: string;

    payment_method: POSPaymentMethod;

    payment_mode: string;

    created_at: string;
    paid_at?: string;

    note?: string;

    client?: Client;
    company?: Company;
    gift_card?: GiftCard;
    terminal?: Terminal;
    items?: ReceiptItem[];
    refunds?: Refund[];

    relatable_id?: number;
    relatable_type?: 'App\\CompanyBooking' | 'App\\CompanyBookingClient';

    relatable?: Booking | BookingClientPivot;

    downloaded?: string;
    transaction_type?: string;
    transaction_type_id?: number;
}

export type POSPaymentMethod = 'swish' | 'viva' | 'viva_online' | 'fortnox_invoice';

export type ReceiptExportType = 'mail' | 'download';

export type DiscountType = 'percentage' | 'value';

export type ReceiptStatus = 'PENDING' | 'PROCESSING' | 'PAID' | 'CANCELLED' | 'REFUNDED' | 'ABORTED' | 'PARTIALLY_REFUNDED';
