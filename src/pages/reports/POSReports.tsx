import { customRound, generateClientFullName, generateUserFullName, getUnitKeyToValue } from '@/helper';
import Card from '@components/card';
import ReportIconItem from '@components/card/ReportMetricCard';
import BarMultiChartComponent from '@components/Charts/BarMultiChart';
import PieChartSingleColorComponent, { chartColors, PieChartData } from '@components/Charts/PieChartSingleColor';
import Button from '@components/form/Button';
import Heading from '@components/heading/Heading';
import ReportTableComponent from '@components/Reports/ReportTableComponent';
import ReportTitleValueComponent from '@components/Reports/ReportTitleValueComp';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import DollarIcon from '@icons/Dollar';
import PosAvgTransactionIcon from '@icons/PosAvgTransaction';
import PosRefundIcon from '@icons/PosRefund';
import PosTransactionIcon from '@icons/PosTransaction';
import { CommonModelResponse } from '@interface/common';
import { Client } from '@interface/model/client';
import { Product } from '@interface/model/product';
import { Service } from '@interface/model/service';
import { User } from '@interface/model/user';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import { EmptyDataComponent } from '@partials/Error/EmptyData';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import ReportsProvider, { useReports } from '@provider/ReportsProvider';
import cx from 'classix';
import { FC, lazy, useMemo, useState } from 'react';
import { useSWRConfig } from 'swr';
import useSWRImmutable from 'swr/immutable';
const ProductLibraryModal = lazy(() => import('@components/PointsOfSale/Product/ProductLibraryModal'));

export interface RecordReportsProps {}

export type POSReportAPITypes =
    | 'pos_revenue'
    | 'pos_statistics'
    | 'payment_gateway_preference'
    | 'client_life_time'
    | 'practitioner_performance'
    | 'top_selling_product'
    | 'top_booked_services'
    | 'low_stock_products'
    | 'total_discount'
    | 'refunds';

type POSStatsResponse = {
    total_revenue: number;
    no_of_transactions: number;
    avg_transaction: number;
    total_refunds: number;
};

type PaymentGatewayData = {
    swish_payment: number;
    viva_payment: number;
};

interface ClientLifeTimeValue extends Client {
    receipts_count: number;
    receipts_sum_paid_amount: number;
}
interface UserPerformanceModel extends User {
    receipts_sum_paid_amount: number;
}

interface TopSellingProductModel extends Product {
    sold_quantity: number;
    revenue: number;
}
interface TopBookedServiceModel extends Service {
    sold_quantity: number;
    revenue: number;
}

interface IRevenueModel {
    name: string;
    products: number;
    services: number;
}

interface ITotalDiscount {
    direct_discount: number;
    total_revenue: number;
}

const POSReports: FC<RecordReportsProps> = ({}) => {
    const { user } = useAuth();
    const { endDate, period, startDate, selectedPractitioners } = useReports();
    const { mutate: globalMutate } = useSWRConfig();

    const { data: statsResponse, isLoading: isStatsLoading } = useSWRImmutable<CommonModelResponse<POSStatsResponse>>(
        api.reports.pos('pos_statistics', startDate, endDate, period, selectedPractitioners),
    );

    const { data: PaymentGatewayResponse, isLoading: isPaymentGatewayLoading } = useSWRImmutable<CommonModelResponse<PaymentGatewayData>>(
        api.reports.pos('payment_gateway_preference', startDate, endDate, period, selectedPractitioners),
    );

    const { data: RevenueResponse, isLoading: isRevenueLoading } = useSWRImmutable<CommonModelResponse<IRevenueModel[]>>(
        api.reports.pos('pos_revenue', startDate, endDate, period, selectedPractitioners),
    );

    const { data: TotalDiscountResponse, isLoading: isTotalDiscountLoading } = useSWRImmutable<CommonModelResponse<ITotalDiscount>>(
        api.reports.pos('total_discount', startDate, endDate, period, selectedPractitioners),
    );

    const RevenueDataStats = useMemo(() => {
        return {
            products: RevenueResponse?.data.reduce((acc, curr) => acc + curr.products, 0) ?? 0,
            services: RevenueResponse?.data.reduce((acc, curr) => acc + curr.services, 0) ?? 0,
        };
    }, [RevenueResponse?.data]);

    const PaymentGatewayData = useMemo(() => {
        if (PaymentGatewayResponse?.data) {
            return [
                { name: pos_strings.swish.swish, value: PaymentGatewayResponse?.data.swish_payment } as PieChartData,
                { name: pos_strings.viva.viva, value: PaymentGatewayResponse?.data.viva_payment } as PieChartData,
            ];
        }
        return [];
    }, [PaymentGatewayResponse]);

    const TotalDiscountData = useMemo(() => {
        if (TotalDiscountResponse?.data) {
            return [
                { name: pos_strings.total + ' ' + strings.revenue, value: TotalDiscountResponse?.data.total_revenue } as PieChartData,
                {
                    name: strings.reports_strings.direct_discount,
                    value: TotalDiscountResponse?.data.direct_discount,
                } as PieChartData,
            ];
        }
        return [];
    }, [TotalDiscountResponse]);

    const [selectedProduct, setSelectedProduct] = useState<Product>();
    const [openProductModal, setOpenProductModal] = useState<boolean>(false);
    const [mutateUrl, setMutateUrl] = useState<string | null>();
    const companyUnit = getUnitKeyToValue(user?.company?.unit ?? '');

    return (
        <div className="grid grid-cols-2 gap-4 lg:grid-cols-4">
            <ModalSuspense>
                {openProductModal && (
                    <ProductLibraryModal
                        selectedProduct={selectedProduct}
                        openModal={openProductModal}
                        setOpenModal={setOpenProductModal}
                        mutate={async () => globalMutate(mutateUrl)}
                    />
                )}
            </ModalSuspense>
            <ReportIconItem
                color="primary"
                icon={<DollarIcon />}
                value={`${companyUnit} ${customRound(statsResponse?.data.total_revenue ?? 0, 0)}`}
                title={strings.reports_strings.total_revenue}
                loading={isStatsLoading}
            />
            <ReportIconItem
                color="yellow"
                icon={<PosTransactionIcon />}
                value={statsResponse?.data.no_of_transactions ?? 0}
                title={strings.reports_strings.no_of_trxn}
                loading={isStatsLoading}
            />
            <ReportIconItem
                color="green"
                icon={<PosAvgTransactionIcon />}
                value={`${companyUnit} ${customRound(statsResponse?.data.avg_transaction ?? 0, 0)}`}
                title={strings.reports_strings.avg_trxn_value}
                loading={isStatsLoading}
            />
            <ReportIconItem
                //
                color="orange"
                icon={<PosRefundIcon />}
                value={`${companyUnit} ${customRound(statsResponse?.data.total_refunds ?? 0, 0)}`}
                title={pos_strings.refund.refund}
                loading={isStatsLoading}
            />

            <Card className="col-span-2">
                <ReportTitleValueComponent title={strings.revenue}>
                    <div className="flex space-x-3 text-sm">
                        <div className="flex items-center space-x-1">
                            <span className="inline-block size-3 rounded-full" style={{ backgroundColor: chartColors.primary }} />
                            <p>
                                {pos_strings.product.products} ({`${getUnitKeyToValue(user?.company?.unit ?? '')} ${RevenueDataStats.products}`})
                            </p>
                        </div>
                        <div className="flex items-center space-x-1">
                            <span className="inline-block size-3 rounded-full" style={{ backgroundColor: chartColors.violet }} />
                            <p>
                                {strings.services} ({`${getUnitKeyToValue(user?.company?.unit ?? '')} ${RevenueDataStats.services}`})
                            </p>
                        </div>
                    </div>
                </ReportTitleValueComponent>
                {!isRevenueLoading && RevenueResponse?.data.length === 0 && <EmptyDataComponent />}
                <BarMultiChartComponent
                    data={RevenueResponse?.data ?? []}
                    dataLabel={[
                        { dataKey: 'products', label: pos_strings.product.products },
                        { dataKey: 'services', label: strings.services },
                    ]}
                />
            </Card>
            <Card className="col-span-2">
                <Heading text={strings.reports_strings.payment_gateway_preferences} />
                {!isPaymentGatewayLoading && PaymentGatewayData.length === 0 && <EmptyDataComponent />}
                <PieChartSingleColorComponent data={PaymentGatewayData} color="orange" />
            </Card>
            <ReportTableComponent<ClientLifeTimeValue>
                api={api.reports.pos('client_life_time', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.Name, strings.reports_strings.trxns, strings.revenue]}
                renderColumn={(item, index, rowIndex) => [
                    rowIndex + 1,
                    generateClientFullName(item),
                    `${item.receipts_count ?? 0}`,
                    cx(getUnitKeyToValue(user?.company?.unit ?? ''), `${customRound(item.receipts_sum_paid_amount, 0) ?? 0}`),
                ]}
                title={strings.reports_strings.client_lifetime_value}
            />

            <ReportTableComponent<UserPerformanceModel>
                api={api.reports.pos('practitioner_performance', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.Name, strings.revenue]}
                renderColumn={(item, index, rowIndex) => [
                    rowIndex + 1,
                    generateUserFullName(item),
                    cx(getUnitKeyToValue(user?.company?.unit ?? ''), `${customRound(item.receipts_sum_paid_amount, 0) ?? 0}`),
                ]}
                title={strings.reports_strings.practitioner_performance}
            />
            <Card className="col-span-2">
                <Heading text={strings.reports_strings.total_discount} />
                {!isTotalDiscountLoading && TotalDiscountData.length === 0 && <EmptyDataComponent />}
                <PieChartSingleColorComponent data={TotalDiscountData} color="violet" />
            </Card>
            <ReportTableComponent<TopSellingProductModel>
                api={api.reports.pos('top_selling_product', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, pos_strings.product.product_name, strings.reports_strings.qty_sold, strings.revenue]}
                renderColumn={(item, index, rowIndex) => [
                    rowIndex + 1,
                    item.name,
                    item.sold_quantity,
                    cx(getUnitKeyToValue(user?.company?.unit ?? ''), `${customRound(item.revenue, 0) ?? 0}`),
                ]}
                title={strings.reports_strings.top_selling_products}
            />
            <ReportTableComponent<TopBookedServiceModel>
                api={api.reports.pos('top_booked_services', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.service_name, strings.reports_strings.qty_sold, strings.revenue]}
                renderColumn={(item, index, rowIndex) => [
                    rowIndex + 1,
                    item.name,
                    item.sold_quantity,
                    cx(getUnitKeyToValue(user?.company?.unit ?? ''), `${customRound(item.revenue, 0) ?? 0}`),
                ]}
                title={strings.reports_strings.top_booked_services}
            />
            <ReportTableComponent<Product>
                api={api.reports.pos('low_stock_products', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, pos_strings.product.product_name, strings.reports_strings.qty_left, strings.reports_strings.action]}
                renderColumn={(item, index, rowIndex, url) => [
                    rowIndex + 1,
                    item.name,
                    item.stock,
                    <Button
                        size="small"
                        variant="ghost"
                        children={strings.add}
                        onClick={() => {
                            setSelectedProduct(item);
                            setMutateUrl(url);
                            setOpenProductModal(true);
                        }}
                    />,
                ]}
                title={strings.reports_strings.low_stock_products}
            />
            <ReportTableComponent<ClientWithRefund>
                api={api.reports.pos('refunds', startDate, endDate, period, selectedPractitioners)}
                className="col-span-2"
                headings={[strings.No, strings.TITLE, strings.amount]}
                renderColumn={(item, index, rowIndex) => [
                    rowIndex + 1,
                    generateClientFullName(item),
                    cx(getUnitKeyToValue(user?.company?.unit ?? ''), `${customRound(item.receipts_sum_refund, 0) ?? 0}`),
                ]}
                title={pos_strings.refund.refund}
            />
        </div>
    );
};

interface ClientWithRefund extends Client {
    receipts_sum_refund: number;
}

const POSReportsHoC: FC = () => <ReportsProvider section="pos" title={strings.reports_strings.reports_pos} children={<POSReports />} />;

export default POSReportsHoC;
