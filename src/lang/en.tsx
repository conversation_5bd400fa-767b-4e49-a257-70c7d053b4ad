const data = {
    MERIDIQ: 'MERIDIQ',
    Home: 'Home',
    welcomeToMeridiq: 'Welcome to Meridiq',
    loginTermsText: 'By clicking the button, you agree to our {0}.',
    loginSignupText: "Don't have an account? {0}",
    Clients: 'Clients',
    ClientAccess: 'Client Access',
    Treatments: 'Treatments',
    RegistrationPortal: 'Registration Portal',
    CompanySettings: 'Company Settings',
    Settings: 'Settings',
    UpgradePlan: 'Upgrade Plan',
    Support: 'Support',

    Email: 'E-mail',
    Password: 'Password',
    RegisterCompany: 'Register Company',
    Welcome_to: 'Welcome to',
    CompanyName: 'Company Name',
    OrganizationNumber: 'Organization Number',
    OrganizationNumberEg: '000000-0000',
    ReEnterPassword: 'Re Enter Password',
    SelectACountry: 'Select a country',

    // LOGIN

    login_subtext: 'The best and easiest way to keep your clients up to date.',
    EMAIL: 'EMAIL',
    PASSWORD: 'PASSWORD',
    login_remember_me: 'Remember me',
    login_forgot_password: 'Forgot Password',
    Login: 'Login',
    LoginNow: 'Login Now',
    SIGN_UP: 'REGISTER COMPANY',
    login_terms_of_use: 'Terms of use & privacy policy',

    // SIGN UP
    signup_subtext: 'The best and easiest way to keep your clients up to date.',
    signup_company_user: 'Company Super User',
    signup_company_user_subtext: 'This e-mail address will be linked to company account and subscription.',
    signup_goto_signin: 'Have an account? SIGN IN',
    signup_company: 'Company Name',
    signup_fname: 'Firstname*',
    signup_lname: 'Lastname*',
    signup_bday: 'Date Of Birth*',
    signup_email: 'Email Address (Username)*',
    signup_email_confirmation: 'Re-Enter Email Address*',
    signup_street_address: 'Street Address*',
    signup_state: 'State*',
    signup_zip: 'Zip Code*',
    signup_country: 'Country*',
    signup_language: 'Language*',
    signup_password: 'Password',
    signup_re_password: 'Re-Enter Password',
    signup_city: 'City*',
    sign_up_mobile_number: 'Mobile Number',

    // Forgot Password

    forgot_subtext: 'Enter your email and we send you a password reset link.',
    forgot_send_request: 'SEND REQUEST',

    // MASTER

    Search: 'Search',
    my_profile: 'My profile',
    logout: 'Logout',
    master_Start: 'Start',
    master_Journals: 'Journals',
    master_JournalAccess: 'Journal access',
    master_Clients: 'Client(s)',
    master_ClientAccess: 'Client access',
    master_ProductsTreatments: 'Treatments',
    master_LettersOfConsents: 'Letters of consents',
    master_RegistrationPortal: 'Registration Portal',
    master_CompanySettings: 'Company Settings',
    master_About: 'About',
    master_Support: 'Support',
    master_Setting: 'Settings',

    // Pages

    // Start Page
    start_welcome_heading: 'Welcome to MERIDIQ!',
    start_welcome_description_1:
        'Use the menu on the left to navigate between company settings, users, patients, letters of consents and other features.',
    start_welcome_description_2_1:
        'Is this the first time you are using MERIDIQ, it would be great to get familiar with the system by reading our guide which you can find here ',
    start_welcome_description_2_2:
        'Would you rather prefer a personal onboarding presentation, we can book an online meeting on Zoom. During the workshop we can go through the full system and answer any questions you might have.',
    start_welcome_description_3_1: 'To book a meeting, simply send an e-mail to ',
    start_welcome_description_3_2: ' with suggestions of date and time and we will get back to you as soon as possible with a confirmation.',
    start_welcome_description_4: 'For any other support questions you can contact us using the form on the “Support page” in the menu.',

    // About Page
    ABOUT: 'ABOUT',
    version_info: 'version 1.0',

    // SUPPORT Page
    SUPPORT: 'SUPPORT',
    SUBJECT: 'Subject',
    MESSAGE: 'Message',
    support_subtext:
        'If you have experienced any issues or want help with anything regarding the application. please fill out the form below and we will get back to you as soon as we can.',
    best_regards: 'Best regards',
    support_team_name: 'Aesthetic client support team',
    SEND_MESSSAGE: 'SEND MESSSAGE',

    // Product / Treatments
    treatments_top_info: 'Create, edit or delete products and treatments.',
    more_info: 'more info',
    NEW: 'NEW',
    Sort: 'Sort',
    TREATMENT_TREATMENT: 'TREATMENT',
    BRAND: 'DESCRIPTION',
    NOTES: 'NOTES',
    TOTAL_COST: 'TOTAL COST',
    save: 'Save',
    DISCARD: 'DISCARD',
    COST: 'Cost',
    UNIT: 'Unit',

    // Letter Of Consents
    NEW_LETTER: 'NEW LETTER',
    letterOf_top_info: 'Create, edit or delete letters of consents.',
    letterOf_more_info_ToolTip:
        'Create letters of consents for your customers to sign. Every question on each letters is equipped with Yes/No buttons, just fill in the question title and the rest will show during signing. Each letters signing area will be generated upon save and show during signing.',
    form_bottom_sign_line: 'All letters of consents will automatically get a Sign area when being signed in clients.',
    LETTER: 'LETTER',
    CONSENT_TITLE: 'Consent Title',
    ADD_NEW_QUESTION: 'ADD NEW QUESTION',
    SAVE_LETTER: 'SAVE LETTER',
    IS_PUBLISH_BEFORE_AFTER_PICTURES: 'Is publish before after pictures',
    HIDE_INACTIVE: 'Hide Inactive',

    // Client Access

    clientAccess_top_info: 'Choose between Full access or Custom access for your users.',
    clientAccess_more_info_ToolTip:
        'Set "Full access" if a user should be able to edit and see every client as standard. Set "Custom access" if you want to users to only be able to edit certain clients and display only editable as standard.',

    // Clients
    Name: 'Name',
    Phone: 'Phone',
    client_top_info: 'Search for clients, create or view existing ones.',
    Search_for_Client: 'Search for Client',
    NEW_CLIENT: 'New Client',
    export: 'Export',
    ADD_CLIENT: 'ADD CLIENT',
    UPDATE_CLIENT: 'Update Client',
    UPDATE_PROCEDURE: 'UPDATE PROCEDURE',
    CANCEL: 'Cancel',
    Firstname: 'First name',
    Lastname: 'Last name',
    SocialSecNumber: 'Date of Birth',
    EmailAddress: 'Email Address',
    PhoneNumber: 'Mobile Number',
    StreetAddress: 'Street Address',
    ZipCode: 'Zip Code',
    City: 'City',
    State: 'State',
    Country: 'Country',
    ADD_ANOTHER_ADDRESS: 'ADD ANOTHER ADDRESS',
    AddPhoto: 'Add Photo',
    DELETE_CLIENT: 'DELETE CLIENT',
    DELETE_CLIENT_1: 'Do you want to delete',
    DELETE_CLIENT_2: 'client?',
    DELETE_CLIENT_3: 'This process can not be reversed.',

    DELETE_COMPANY: 'Delete Company',
    DELETE_COMPANY_1: 'Do you want to delete',
    DELETE_COMPANY_2: 'company?',
    DELETE_COMPANY_3: 'This process can not be reversed.',

    // / Clients Journal
    Information: 'Information',
    SocialSecurity: 'Date of Birth',
    Contact: 'Contact',
    Address: 'Address',
    Procedures: 'Procedures',
    Procedure: 'Procedure',
    PROCEDURE_NAME: 'PROCEDURE NAME',
    LettersofConsents: 'Letters of Consents',
    VIEWHEALTHQUESTIONNAIRE: 'View/Update Health Questionnaire',
    VIEWAESTETHICINTEREST: 'View/Update Aestethic Interest',
    VIEWCOVID19: 'View/Update Covid-19 Questionnaire',
    information_permission_question: 'Are we allowed to publish before & after pictures?',
    NEWPROCEDURE: 'NEW PROCEDURE',
    NEWLETTER: 'NEW LETTER',
    NEW_COVID19: 'NEW COVID19',
    Signed: 'Signed',
    PendingSign: 'Pending Sign',
    DrSigned: 'Dr. Signed',
    By: 'By',

    // Yes No Else
    Yes: 'Yes',
    YesIfCoverEyes: 'Yes, if you cover my eyes',
    No: 'No',

    // New Procedure
    PROCEDURE_TITLE: 'Procedure Title',
    DATE_OF_PROCEDURE: 'Date of Procedure',
    Date_of_treatment: 'Date of treatment',
    Pictures: 'Pictures',
    USETEMPLATE: 'Use Template',
    ADDFROMFILES: 'Add From Files',
    TAKEPHOTOS: 'Take Photos',
    Admin: 'Admin',
    User: 'User',
    Users: 'Users',

    GeneralNotes: 'Notes & Files',

    // REGISTRATION PORTAL
    REGISTRATIONPORTAL: 'Registration Portal',
    choose_doctor: 'Choose Your Professional',
    choose_doctor_select_note: 'Don´t know',
    Select: 'Select',
    Current: 'Current',
    choose_your_professional: 'Choose your professional',
    NEXT_REGISTRATION: 'NEXT: REGISTRATION',
    Personalinformation: 'Personal information',
    NEWTREATMENT: 'NEW TREATMENT',
    Logout: 'Logout',

    PREVIOUS: 'Previous',

    // Steps

    Step1: 'Step 1/3',
    Step2: 'Step 2/3',
    Step3: 'Step 3/3',
    Step4: 'Step 4/3',

    // Step 1
    Professional: 'Professional',
    FIRSTNAME: 'Firstname',
    LASTNAME: 'Lastname',
    SOCIALSECURITYNUMBER: 'Date Of Birth',
    PHONENUMBER: 'Mobile Number',
    STREETADDRESS: 'Street Address',
    CITY: 'City',
    ZIPCODE: 'Zip Code',
    STATE: 'State',
    COUNTRY: 'Country',
    NEXT_HEALTHQUESTIONNAIRE: 'Next: Health Questionnaire',
    AddAnotherAddress: 'Add another address',

    // Step 2
    HealthQuestionnaire: 'Health Questionnaire',
    HealthQuestionnaire_top_info: 'Fill in the following medical questionnaires',
    UpdateHealthQuestionnaire: 'View/Update Health Questionnaire',
    yes_textarea: 'If yes, give more information:',
    general_note: 'General note',
    health_question_0: 'Are you pregnant, breastfeeding or are you trying to get pregnant?',
    health_question_1: 'Do you smoke?',
    health_question_2: 'Are you currently receiving any medical treatment?',
    health_question_3:
        'Have you previously received any aesthetic treatments (eg massage, laser, exfoliation, dermabrasion, Microneedling, IPL, Chemical Peeling, PRP etc.)?',
    health_question_4: 'Have you been treated with fillers, absorbable fillers, semi-permanent fillers or botulinum toxin?',
    health_question_5: 'Did you experience any side effects related to the treatment?',
    health_question_6: 'Have you ever had an autoimmune disease or a disease that affected the immune system?',
    health_question_7: 'Do you have any skin infection or other inflammatory problems (eg herpes, acne etc.)?',
    health_question_8: 'Have you recently used any glycolic acid or retinoids (such as isotretinoin or tretinoin)?',
    health_question_9: 'Do you have tattoos or permanent makeup in the treatment area?',
    health_question_10: 'Do you have type 1 or type 2 diabetes?',
    health_question_11: 'Do you have haemophilia?',
    health_question_12: 'Now have any ongoing infection in the body?',
    health_question_13: 'Have you ever had any skin cancer?',
    health_question_14: 'Do you have tanned skin?',
    health_question_15: 'Do you have Melasma or Kloasma?',
    health_question_16: 'Do you have active rosacea?',
    health_question_17: 'Are you currently taking steroids, acetylsalicylic acid (eg Treo or Aspirin) or any anticoagulant (eg Warfarin)?',
    health_question_18: 'Do you suffer from acute rheumatoid arthritis or recurrent sore throat?',
    health_question_19: 'Do you suffer from amyotrophic sclerosis (ALS) or peripheral neuromuscular disorder?',
    health_question_20: 'Do you suffer from myasthenia gravis or Eaton-Lambert syndrome?',
    health_question_21: 'Do you suffer from any allergies?',
    health_question_22: 'Do you suffer from untreated epilepsy?',
    health_question_23: 'Do you have a tendency to develop scarring?',
    health_question_24: 'Do you suffer from porphyria?',
    health_question_25: 'Have you used antibiotics within the last 4 weeks?',
    health_question_26: 'Are you reviewing any medical treatment right now?',
    health_question_27: "Do you suffer from disturbances in the heart's retinal system?",
    health_question_28: 'Have you been to or are planning a dentist visit in the near future? (2v before / 2v after)',
    health_bottom_info:
        'If you answered Yes to any of the above questions, your therapist will ask for more information. Your therapist may refuse to treat you if he / she thinks the treatment is not in your best interest.',
    NEXT_AESTETHIC_QUESTIONNAIRE: 'Next: Aestethic Questionnaire',

    health_question_new_0: 'Do you suffer from any allergies?',
    health_question_new_1: 'Do you use tobacco?',
    health_question_new_2: 'Are you pregnant, breastfeeding or trying to conceive?',
    health_question_new_3: 'Are you currently receiving any medical treatment?',
    health_question_new_4: 'Are you currently taking steroids, acetylsalicylic acid (eg Treo or Aspirin) or any anticoagulant (eg Warfarin)?',
    health_question_new_5: 'Have you recently used any glycolic acid or retinoids (such as isotretinoin or tretinoin)?',
    health_question_new_6: 'Do you have an ongoing infection in your body or have you taken antibiotics in the last four weeks?',
    health_question_new_7: 'Do you have an ongoing illness that is physical or mental?',
    health_question_new_8: 'Have you had problems with anxiety, depression, or other mental disorders?',
    health_question_new_9: 'Do you have an autoimmune disease or a disease that has affected the immune system?',
    health_question_new_10: 'Do you have diabetes?',
    health_question_new_11: 'Do you have any haemophilia?',
    health_question_new_12: 'Do you suffer from acute rheumatoid arthritis or recurrent sore throat?',
    health_question_new_13: 'Do you suffer from amyotrophic sclerosis (ALS) or peripheral neuromuscular disorder (nerve disease)?',
    health_question_new_14: 'Do you suffer from myasthenia gravis or Eaton-Lambert syndrome (muscle disease)?',
    health_question_new_15: 'Do you suffer from untreated epilepsy?',
    health_question_new_16: 'Do you have a tendency to develop large scars (keloids)?',
    health_question_new_17: 'Have you had any skin cancer before?',
    health_question_new_18: 'Do you suffer from porphyria (hereditary disease)?',
    health_question_new_19: 'Do you suffer from abnormal palpitations or other heart disease?',
    health_question_new_20: 'Have you been to or are you planning a dental visit soon? (2 weeks before / 2 weeks after)',
    health_question_new_21:
        'Have you previously received any aesthetic treatments (eg laser, peeling, dermabrasion, microneedling, IPL, Chemical Peeling, PRP, etc.)?',
    health_question_new_22: 'Have you previously received injectable treatments, thread lift or undergone facial surgery of any kind?',
    yes_textarea_22: 'If the answer is yes, what treatment have you received, what areas have been treated and when?',
    health_question_new_23: 'Did you experience any side effects related to the treatment?',
    health_question_new_24: 'Do you have any skin infection or inflammatory skin problems (eg herpes, acne, rosacea, etc.)?',
    health_question_new_25: 'Do you have tattoos or permanent makeup in the treatment area?',
    health_question_new_26: 'Do you have tanned skin now?',
    health_question_new_27: 'Do you have Melasma or Chloasma (pigmentation)?',
    health_question_new_28: 'Other comments about your health',

    // Step 3
    Aestethicinterest: 'Aesthetic Interest',
    UpdateAestethicInterest: 'View/Update Aesthetic Interest',

    aestethic_question_0: 'What is the reason for your visit today?',
    aestethic_question_1: 'I want consultation in the following area/s',
    aestethic_question_2: 'Have you had any beauty treatments before?',
    aestethic_question_3: 'Which three areas fit your expectations best?',
    aestethic_question_4: 'How would you describe your complexion',
    aestethic_question_5: 'If you could change something with your skin, what would it be?',
    aestethic_question_6: 'I would be interested in these treatments',
    aestethic_question_7: 'How did you find us?',
    aestethic_question_8: 'Are we allowed to publish before and after photos?',
    aestethic_question_9: 'Other comments',
    aestethic_question_10: 'Please circle in the areas you are interested in changing',

    aestethic_question_1_0: 'How I can look better for my age',
    aestethic_question_1_1: 'How I can change something that I have been thinking about for a long time',
    aestethic_question_1_2: 'How I can look more attractive',
    aestethic_question_1_3: 'How I can remove something that I am bothered by',
    aestethic_question_1_4: 'Other / do not want to be consulted',

    aestethic_question_2_0: 'Yes',
    aestethic_question_2_1: 'No',

    aestethic_question_3_0: 'I want to look less tired',
    aestethic_question_3_1: 'I want my skin to feel less soft',
    aestethic_question_3_2: 'I want to look younger',
    aestethic_question_3_3: 'I want to look less sad',
    aestethic_question_3_4: 'I want to look slimmer',
    aestethic_question_3_5: 'I want to look less angry',
    aestethic_question_3_6: 'I want softer features',
    aestethic_question_3_7: 'I want to look more beautiful',

    aestethic_question_4_0: 'Bad',
    aestethic_question_4_1: 'Ok',
    aestethic_question_4_2: 'Good',
    aestethic_question_4_3: 'Better',
    aestethic_question_4_4: 'Perfect',

    aestethic_question_5_0: 'Moisture',
    aestethic_question_5_1: 'Elasticity',
    aestethic_question_5_2: 'Skin texture',
    aestethic_question_5_3: 'Skin tone',

    aestethic_question_6_0: 'Microneedling',
    aestethic_question_6_1: 'Chemical peels',
    aestethic_question_6_2: 'Facial',
    aestethic_question_6_3: 'Lash & Brow',
    aestethic_question_6_4: 'Cosmetic Tattoo',
    aestethic_question_6_5: 'Peeling',
    aestethic_question_6_6: 'Botox',
    aestethic_question_6_7: 'Belkyra',
    aestethic_question_6_8: 'CO2 laser',
    aestethic_question_6_9: 'Fillers',
    aestethic_question_6_10: 'IPL Laser Hair Removal',
    aestethic_question_6_11: 'Lash lift',
    aestethic_question_6_12: 'Waxing',
    aestethic_question_6_13: 'Skin booster',
    aestethic_question_6_14: 'Profhilo',
    aestethic_question_6_15: 'PRP',
    aestethic_question_6_16: 'Other',

    aestethic_question_7_0: 'Through another clinic',
    aestethic_question_7_1: 'Advertising',
    aestethic_question_7_2: 'Through a friend or family member',
    aestethic_question_7_3: 'Journal',
    aestethic_question_7_4: 'Search engine (e.g. Google)',
    aestethic_question_7_5: 'Social Media',
    aestethic_question_7_6: 'Other',

    aestethic_question_8_0: 'Yes',
    aestethic_question_8_1: 'Yes, if you can cover my eyes',
    aestethic_question_8_2: 'No',

    NEXT_LETTER_OF_CONSENTS: 'Next: Letters of Consents',

    // new letter of consent questions

    aestethic_question_new_0: 'What is the reason for your visit today?',
    aestethic_question_new_1: 'I want to know more about how I can?',
    aestethic_question_new_2: 'Which three areas best suit your expectations?',
    aestethic_question_new_3: 'Why do you want to improve your appearance?',
    aestethic_question_new_4: 'How would you describe your skin?',
    aestethic_question_new_5: 'Please circle in the areas you are interested in changing',
    aestethic_question_new_6: 'If you could change something with your skin, what would it be?',
    aestethic_question_new_7: 'I would be interested in these treatments',
    aestethic_question_new_8: 'How did you find us?',
    aestethic_question_new_9: 'Are we allowed to publish before and after pictures?',
    aestethic_question_new_10: 'Other comments',

    aestethic_question_new_1_0: 'look better for my age',
    aestethic_question_new_1_1: 'change something that I have been thinking about for a long time',
    aestethic_question_new_1_2: 'look more attractive',
    aestethic_question_new_1_3: 'remove something that bothers me?',
    aestethic_question_new_1_4: 'Other',

    aestethic_question_new_2_0: 'I want my skin to be less flabby and saggy',
    aestethic_question_new_2_1: 'I want to look less sad',
    aestethic_question_new_2_2: 'I want to look less tired',
    aestethic_question_new_2_3: 'I want to look less angry',
    aestethic_question_new_2_4: 'I want to look more beautiful',
    aestethic_question_new_2_5: 'I want to look younger',
    aestethic_question_new_2_6: 'I want to look slimmer',
    aestethic_question_new_2_7: 'I want to look more masculine / feminine',

    aestethic_question_new_3_0: 'For myself',
    aestethic_question_new_3_1: 'For my partner',
    aestethic_question_new_3_2: 'To improve my social status',
    aestethic_question_new_3_3: 'For my potential position in my business life',

    aestethic_question_new_4_0: 'Bad',
    aestethic_question_new_4_1: 'Ok',
    aestethic_question_new_4_2: 'Good',
    aestethic_question_new_4_3: 'Perfect',

    aestethic_question_new_6_0: 'Less dryness',
    aestethic_question_new_6_1: 'Increased elasticity',
    aestethic_question_new_6_2: 'Improve skin structure',
    aestethic_question_new_6_3: 'Even skin tone',

    aestethic_question_new_7_0: 'Relaxing facial',
    aestethic_question_new_7_1: 'Fix eyelashes and eyebrows (lashlift, browlift, cosmetic tattoo)',
    aestethic_question_new_7_2: 'Antiaging och beautification (filler, botox, skin tightening)',
    aestethic_question_new_7_3: 'Reduce double chin ',
    aestethic_question_new_7_4: 'Skin treatments (increased glow, smaller scars, pores, superficial blood vessels)',
    aestethic_question_new_7_5: 'Hair removal (waxing, IPL)',
    aestethic_question_new_7_6: 'Hair Loss (PRP, Hair Filler)',
    aestethic_question_new_7_7: 'Laser treatment',
    aestethic_question_new_7_8: 'Body contouring (muscle stimulating, fat freezing)',
    aestethic_question_new_7_9: 'Other',

    aestethic_question_new_8_0: 'Through another clinic',
    aestethic_question_new_8_1: 'Advertising',
    aestethic_question_new_8_2: 'Through a friend or family member',
    aestethic_question_new_8_3: 'Newspaper',
    aestethic_question_new_8_4: 'Search engine (e.g. Google)',
    aestethic_question_new_8_5: 'Social Media',
    aestethic_question_new_8_6: 'Other',

    aestethic_question_new_9_0: 'Yes',
    aestethic_question_new_9_1: 'Yes, if you can cover my eyes',
    aestethic_question_new_9_2: 'No',

    UpdateCovid19Questionnaire: 'View/Update Covid-19 Questionnaire',
    Covid19Questionnaire: 'Covid-19 Questionnaire',
    NEXT_COVID19: 'Next: Covid-19 Questionnaire',

    covid19_0: 'Have you had a confirmed COVID-19 infection?',
    covid19_1: 'Have you received any treatment for COVID-19?',
    covid19_2:
        'I confirm that I have had one or more of the following symptoms in the last 14 days: fever, dryness, persistent cough or loss of taste or smell.',
    covid19_3: 'Has anyone in your closest environment or family had COVID-19 infection-related symptoms in the last two weeks?',
    covid19_4: 'Have you travelled abroad in the last 4 weeks?',
    covid19_5: 'I confirm that I am in the clinically extremely vulnerable category and therefore advised to shield at home by the government.',
    covid19_6: 'I confirm that (as far as I know) I have been in close contact with someone with confirmed COVID-19 infection for the past 14 days.',
    covid19_7: 'I agree to attend a face to face appointment during the COVID-19 pandemic',
    covid19_8: 'I have recently done a PVC test for Covid-19.',
    covid19_9: 'I have been tested negative for antibodies.',
    covid19_10: 'I have been tested positive for antibodies.',
    covid19_11: 'I am vaccinated against Covid-19.',
    // Feedback
    Bad: 'Bad',
    Okay: 'Okay',
    Good: 'Good',
    Better: 'Better',
    Perfect: 'Perfect',

    Other: 'Other',

    // Step 4
    Generalquestions: 'General questions',
    general_question1: 'How did you find us?',
    general_question2: 'Are we allowed to publish before and after photos?',
    general_question11: 'Through other clinic',
    general_question12: 'Advertising',
    general_question13: 'Via a friend or family member',
    general_question14: 'Magazine',
    general_question15: 'Search engine (e.g Google)',
    general_question16: 'Social media',
    general_question17: 'Other',
    SAVEANDFINISH: 'Save and Finish',

    // Step 4 Modal
    step_4_modal_Perfect: 'Perfect!',
    step_4_modal_Info: 'Your information has been added. Your doctor/nurse will soon come and get you.',
    CLOSE: 'CLOSE',

    // Extras or Lately Added
    AgreeTerms: 'Agree to our terms of uses & privacy policy.',

    // Company Setting
    USER_ROLE: 'User Role',
    ADD_USER: 'ADD USER',
    UPDATE_USER: 'Update User',
    TITLE: 'Title',
    OLD_PASSWORD: 'Old Password',
    UPDATE_COMPANY: 'Updated Company',

    // Setting
    COMPANY_SETTING: 'COMPANY SETTING',
    Language: 'Language',

    SAVEPROCEDURE: 'SAVE PROCEDURE',

    ChangePassword: 'Change Password',
    OldPassword: 'Old Password',
    NewPassword: 'New Password',
    ReEnterNewPassword: 'Re-enter New Password',
    UPDATE_PASSWORD: 'Update Password',

    Templates: 'Templates',
    NewTemplate: 'New Template',
    UpdateTemplate: 'Update Template',
    NEW_TEMPLATE: 'NEW TEMPLATE',
    Template: 'Template',
    TemplateName: 'Template Name',
    InviteAFriend: 'Invite a friend',
    ComingSoon: 'Coming Soon',
    Templates_top_info: 'Create, edit or delete Templates.',
    Clear: 'Clear',
    Undo: 'Undo',
    Sign: 'Sign',
    ADD_LETTER_OF_CONSENT: 'Add Letter Of Consent',
    UPDATE_LETTER_OF_CONSENT: 'Update Letter Of Consent',
    letter_of_consent: 'Letter of Consent',
    Done: 'Done',
    choose_file_here: 'Choose file here to upload',
    Next: 'Next',
    REGISTER_NEXT: 'NEXT',
    OTPVerificationBottomLine: 'We sent you an email containing a verification code on your previously entered email address.',
    OTPVerification: 'Verification Code',
    CHOOSE_PLAN: 'NEXT: CHOOSE PLAN',
    SelectPlan: 'Select Plan',
    Upgrade_Plans: 'Upgrade Plans',
    Upgrade_Plan: 'Upgrade Plan',
    SUBSCRIPTION: 'SUBSCRIPTION',
    Subscription: 'Subscription',
    IsBlocked: 'IsBlocked',
    IsReadOnly: 'IsReadOnly',
    cancelSubscription: 'Cancel Subscription',
    currentSubscription: 'Current Subscription',
    // _name: '_name',

    // Master Admin
    Companies: 'Companies',
    Company: 'Company',

    Subscription_text_detail:
        'To get started, kindly choose your preferred subscription option below and provide your billing address as well as your credit/debit card information in the next step. For a free trial, select 1 User with 21 Clients. Welcome aboard!',

    Subscription_support_detail_1: 'Select the number of Users or Clients that you need.',
    Subscription_support_detail_2:
        'If you have 4 Users in your clinic but 3000 Clients. Then you need to select the 6 user subscription so you can keep records of all your Clients. Because for one User, it includes 500 Patients.',
    Subscription_support_detail_3:
        'If you have 5 Users in your clinic but 1000 Clients. Then you need to select the 5 Users subscription, so each User has their own license.',
    Subscription_support_detail_4: 'We offer {0}',
    Example: 'Example',

    Confirmation: 'Confirmation',
    Cancel: 'Cancel',
    do_you_want_to_save: 'Do you want to save ?',

    invite_a_friend_para_1:
        'Invite a friend or colleague to use MERIDIQ. As we get more users, we will recieve more great suggestions for new functions. Help us to grow and develop the system, giving an opportunity for all clinics to afford secure patient record keeping.',
    invite_a_friend_para_2: 'Fill in your colleagues e-mail adress here below to send the invite, and we will take care of the rest.',
    invite_a_friend_btn: 'Send Invite',

    // 17-07-2020
    Actions: 'Actions',
    Loading_with_dots: 'Loading...',
    Loading: 'Loading',
    NotFound: 'Not Found',
    Cancelled: 'Cancelled',
    NotSubscribed: 'Not Subscribed',
    Subscribe: 'Subscribe',
    edit_profile: 'Edit Profile',
    Close: 'Close',
    Delete: 'Delete',
    DeleteUser: 'Delete User',
    DeleteUserQuestion: 'Do you want to delete this user?',
    MasterAdmin: 'Master Admin',
    Manbodyback: 'Man body back',
    Manface: 'Man face',
    Manbodyfront: 'Man body front',
    Womanbodyback: 'Woman body back',
    Womanface: 'Woman face',
    Womanbodyfront: 'Woman body front',
    Oops: 'Oops',
    it_seems_plans_limit_reached: "It seems that you have reached your plan's limit.",
    Click_below_button_to_upgrade_your_plan: 'Click below button to upgrade your plan.',
    RecentlyAdded: 'Recently Added',
    MostRecent: 'Most Recent',
    Custom_access: 'Custom access',
    Select_a_consent: 'Select a consent',
    Delete_Client_letter_of_consent: 'Delete Client letter of consent',
    Are_you_sure_you_want_to_delete_this_letter: 'Are you sure you want to delete this letter - :name ?',
    Are_we_allowed_to_publish_before_and_after_pictures: 'Are we allowed to publish before and after pictures?',
    DownloadAsset: 'Download Asset',
    Download: 'Download',
    Confirm: 'Confirm',
    image_override_question: 'It seems their is already a image present in page, Are you sure you want to override it?',
    Please_wait_while_we_save_your_record: 'Please wait while we save your record.',
    Select_treatment: 'Select treatment',
    AllCustomers: 'All Customers',
    MyCustomers: 'My Customers',
    AllClients: 'All Clients',
    MyClients: 'My Clients',
    NewUser: 'New User',
    You_have_now_sucessfully_created_your_profile_at: 'You have now sucessfully created your profile at',
    We_are_looking_forward_to_your_visit: 'We are looking forward to your visit',
    upgrade: 'Upgrade',
    cancel_subscription_question: 'Are you sure you want to cancel the subscription?',
    GBStorage: 'GB Storage',
    upgrade_plan_question: 'Are you sure you want to upgrade to this plan?',
    Payment_initializing: 'Payment initializing',
    please_wait_for_redirecting: 'please wait for redirecting.....',
    Pay: 'Pay',
    DeleteTemplate: 'Delete Template',
    DeleteTemplateQuestion: 'Do you want to delete this template?',
    InactivateTemplate: 'Inactivate Template',
    InactivateTemplateQuestion: 'Are you sure you want to inactivate this template?',
    activateTemplate: 'Activate Template',
    activateTemplateQuestion: 'Are you sure you want to activate this template?',
    All: 'All',
    Active: 'Active',
    InActive: 'Inactive',
    QuickGuide: 'Quick Guide',
    QuickGuide_start: 'Welcome to MERIDIQ - to get started, please use our quick guide on our website.',
    QuickGuideDontShowStartUp: "Don't show this up at startup",
    CompanyLogo: 'Company Logo',
    ProfilePicture: 'Profile Picture',
    start_writing_here: 'Start Writing here.',

    // 22-07-2020
    updating_with_dots: 'Updating...',
    Password_updated_successfully: 'Password updated successfully.',
    Letter_Consent_Saved_Successfully: 'Letter Of Consent Saved Successfully.',
    Health_Questionnarie_updated_successfully: 'Health Questionnarie updated successfully.',
    covid19_updated_successfully: 'Covid-19 Questionnaire updated successfully.',
    // etc
    Listing: 'Listing',
    client_out_of: 'client out of',

    // 1-09-2020
    Occupation: 'Occupation',
    FullAccess: 'Full Access',
    Treatment: 'Treatment',

    // 18-09-2020
    AddedLetterOfConsents: 'Added Letter Of Consent',

    // 21-09-2020
    Search_for_Companies: 'Search for Companies',

    // 12-10-2020
    used: 'used',
    usedClientCount: 'used',
    answer_is_required: 'answer is required',
    next: 'next',
    previous: 'Previous',
    mobile_number_is_required: 'Mobile number is required',
    mobile_number_must_be_numeric: 'Mobile number must be numeric',
    setting_loc: 'Letter of Consent',
    setting_covid19: 'Covid-19 Questionnaires',
    client_access_changed_successfully: 'Client access changed successfully',

    // 16-10-2020
    Warning: 'Warning',
    letter_of_consent_not_saved_warning: 'Letter of consent is not saved, Do you want to save it?',
    letter_of_consent_email_message: 'signed consent will be sent to this e-mail, ensure that it is correct.',
    letter_of_consent_email_message_2: 'Is it incorrect? Please inform your practitioner',

    // 13-11-2020
    Update: 'Update',
    VAT_number: 'VAT Number',
    // 02-11-2020
    email_is_required: 'E-mail is required',
    password_is_required: 'Password is required',
    re_password_is_required: 'Re-password is required',
    confirm_password_is_required: 'Confirm password is required',
    old_password_is_required: 'Old password is required',
    client_treatment_saved_success: 'Client Treatment saved successfully.',
    enter_unique_email: 'Please enter a unique email',
    change_color_here: 'Change colour here',

    // 8-12-2020
    update_payment_information: 'Update Payment Information',

    // 17-12-2020
    // How to Use Registeration Portal
    register_page_1:
        'In the registration portal, your patients will fill in the forms and information you have specified as requirements under "QUESTIONNAIRE" and "MANDATORY FIELDS". After they have filled in all the information, their profile will be created automatically and everything is then prepared for future visits / treatment.',
    register_page_2_1:
        'In order for your patients to be able to register in your medical record system, they need to visit the following unique link for your company: ',
    register_page_2_2: ' and fill in all their information.',
    register_page_3: 'You can send this link to your customers eg via your booking confirmation, e-mail, DM, social media etc...',
    register_page_4: 'You decide how your patients get the registration link.',
    register_page_5: 'Copy the link here:',
    register_page_6_1: 'Good luck and contact us at ',
    register_page_6_2: ' if you have any questions.',

    AfterCare: 'Send Information',
    BrowseFile: 'Browse File',
    send: 'Send',

    FAQ: 'Frequently Asked Questions?',
    please_provide_file: 'Please fill out the questionnaires, LOC, file, or template',

    // 21-11-2020
    to_the_portal: 'TO THE PORTAL',

    // 24-12-2020
    hide_inactive_loc: 'Hide inactive',
    add_treatment: 'Add treatment',
    update_treatment: 'Update treatment',
    total_cost: 'Total cost',

    last_login_at: 'last login at',
    created_at: 'Created at',
    dashboard: 'Dashboard',
    storageUsage: 'Storage Usage',
    reports: 'Reports',

    // 8-01-2020
    treatment_is_required: 'Treatment is required',
    start_date: 'Start date',
    end_date: 'End date',

    searching: 'Searching',
    no_data: 'No data',
    DataCount: 'Data count',

    // 19-Jan-2021
    // validations
    please_provide_valid_email: 'Please provide valid email',
    validate_captcha: 'Validate captcha',
    subject_is_required: 'Subject is required',
    message_is_required: 'Message is required',
    signature_or_signedfile_is_required: 'You need to sign Letter of Consent or the attached file',
    signature_is_required: 'Signature is required',
    sign_is_required: 'Sign is required',
    is_publish_before_after_pictures_is_required: 'Publish before after pictures is required',
    consent_is_required: 'Select a Letter of Consent',
    consent_title_is_required: 'Consent title is required',
    cost_is_required: 'Cost is required',
    cost_must_be_numeric: 'Cost must be numeric',
    cost_must_be_greater_than_zero: 'Cost must be greater than 0',
    template_name_is_required: 'Template name is required',
    image_is_required: 'Image is required',
    please_provide_valid_date_format: 'Please provide valid date format YYYY-MM-DD',
    please_provide_valid_date_format_custom: 'Please provide valid date format {0}',
    treatment_cost_must_be_number: 'Treatment cost must be number',
    treatment_cost_must_be_greater_than_zero: 'Treatment cost must be greater than 0',
    company_name_is_required: 'Company name is required',
    first_name_is_required: 'First name is required',
    last_name_is_required: 'Last name is required',
    street_address_is_required: 'Street address is required',
    city_is_required: 'City is required',
    state_is_required: 'State is required',
    zip_code_is_required: 'Zip code is required',
    country_is_required: 'Country is required',
    agree_to_our_terms_and_conditions: 'Agree to our terms and conditions',
    re_email_is_required: 'Re-email is required',
    email_not_matched: 'Email not matched',
    password_not_matched: 'Password not matched',
    user_role_is_required: 'User role is required',
    title_is_required: 'Title is required',
    phone_number_must_be_numeric: 'Mobile Number must be numeric',
    password_length_must_be_greater_then_8: 'Password length must be greater then 8',
    password_must_contain_a_lower_case_letter: 'Password must contain a lower case letter',
    password_must_contain_a_upper_case_letter: 'Password must contain a upper case letter',
    password_must_contain_a_number: 'Password must contain a number',
    country_code_is_required: 'Country Code is required',

    // New Strings Lookup
    Enter_OTP_here: 'Enter verification code here',

    ACTIVATE_2FA: 'Activate 2FA',

    Client_Welcome_Email: 'Send welcome email to client when registrering',
    User_Logs: 'User Logs',
    Client_Logs: 'Client Logs',
    Details: 'Details',
    On: 'On',
    DateTime: 'Date Time',

    Old: 'Old',
    New: 'New',

    NewNote: 'New Note',
    AddNote: 'Add Note',
    UpdateNote: 'Update Note',
    note_is_required_without_file: 'Notes is required without file',
    file_is_required_without_note: 'File is required without notes',
    Signed_at: 'Signed at',
    Signed_by: 'Signed by',
    LatestChange: 'Latest Change',
    LastChangedDate: 'Last Changed Date',
    Date: 'Date',
    Changed_by: 'Changed by',
    VerifiedSign: 'Verified Sign',
    VerifiedSignBy: 'Verified Sign by',
    VerifiedBy: 'Verified by',
    VerifiedSignAt: 'Verified Sign at',

    NewMedia: 'New Media',

    // 30-01-2021
    upgradePlanMessage:
        'Please note that MERIDIQ is a free service up to 21 clients. Do you like our product? Please upgrade to a paid version and support our development. Thank you!',
    otp: 'Verification Code',

    Questionnaire: 'Questionnaire',
    Questionnaires: 'Questionnaires',
    NewQuestionnaire: 'New Questionnaire',
    AddQuestionnaire: 'Add Questionnaire',
    UpdateQuestionnaire: 'Update Questionnaire',
    DeleteQuestionnaires: 'Delete Questionnaire',
    DeleteQuestionnairesQuestion: 'Are you sure you want to delete this questionnaire?',

    QuestionnaireQuestions: 'Questionnaire Questions',
    Question: 'Question',
    Questions: 'Questions',
    Type: 'Type',
    NewQuestion: 'New Question',
    AddQuestion: 'Add Question',
    UpdateQuestion: 'Update Question',
    DeleteQuestion: 'Delete Question',
    DeleteQuestionQuestion: 'Are you sure you want to delete this question?',

    Required: 'Required',
    question_is_required: 'question is required',
    type_is_required: 'type is required',
    required_is_required: 'required is required',

    update: 'Update',
    deleted: 'deleted',

    ClientQuestionnaire: 'Client Questionnaire',
    ClientQuestionnaires: 'Client Questionnaires',
    NoQuestions: 'No Questions',
    ClientProcedures: 'Client Procedures',
    Order: 'Order',
    order_is_required: 'order is required',
    order_number_must_be_numeric: 'order number must be numeric',

    out_of: 'out of',

    verification_code_in_email: 'Input verification code (sent to your email)',
    resend_code: 'resend code',
    resend_code_in: 'resend code in',

    standard_questionnaires: 'Standard Questionnaires',
    own_questionnaires: 'Custom Questionnaire',
    view_or_update: 'View/Update',

    Won: 'Won',
    Lost: 'Lost',
    NotDecided: 'Not Decided',
    Contacted: 'Contacted',

    question_yes_no_info: 'When answering Yes, additional text is required',
    more_info_required: 'more info is required when answer is yes',
    please_provide_valid_answer: 'please provide valid answer',
    please_provide_some_explanation: 'please provide some explanation',
    GoToHome: 'Go To Home',
    Submit: 'Submit',
    SupportSubmit: 'Submit',
    anymore_detail: 'Please provide additional information',

    Are_you_sure_you_want_to: 'Are you sure you want to',
    this_treatment: 'this treatment?',
    this_letter_of_consent: 'this letter of consent?',
    click_here_to_upload_image: 'Click here to upload image',
    click_here_to_upload_images: 'Click here to upload images',
    description_is_required: 'description is required',
    EditProfile: 'Edit Profile',
    Theme: 'Theme',

    CreateProcedure: 'Create Procedure',
    EditProcedure: 'Edit Procedure',
    NoInternet: 'No Internet Connection',
    NewUpdate: 'A new version is available!',
    Redirecting: 'Redirecting',

    supprt_link: 'https://meridiq.com/documentation/',
    cant_downgrade_plan: 'You cannot downgrade your plan!',
    cant_downgrade_plan_1: 'You cannot downgrade your plan! Your current configuration is greater than selected plan.',
    no_data_changed: 'No data changed',
    View: 'View',
    view_later: 'View later',

    excl_VAT: 'excl. VAT',
    monthly_subscription: 'Monthly Subscription (excl. VAT)',
    Up_to: 'Up to',
    UnlimitedStorage: 'Unlimited Storage',
    CustomerSupport: 'Customer Support',
    Register: 'Register',
    and: 'och',

    // Theme
    dark: 'Dark',
    light: 'Light',
    system: 'System',

    skip: 'Skip',

    per_month: 'per month',
    per_yearly: 'per yearly',
    send_email_to_clients: 'Automatically send signed consents to client',

    // client media
    media: 'Media',
    add: 'Add',
    clientMedia: 'Client Media',
    images_has_selected: 'images has selected',
    client_media_are_you_sure: 'Do you want to delete',

    use_media: 'Use Media',
    add_media: 'Add Media',
    loadMore: 'Load More',

    important: 'Important',
    not_important: 'Not Important',
    RESTORE_CLIENT: 'ACTIVATE CLIENT',
    RESTORE_CLIENT_1: 'Are you sure you want to activate',
    RESTORE_CLIENT_2: 'client?',

    INACTIVATE_CLIENT: 'IN-ACTIVATE CLIENT',
    INACTIVATE_CLIENT_1: 'Are you sure you want to inactivate',
    INACTIVATE_CLIENT_2: 'client?',

    next_of_kind: 'Next of Kin',
    client_next_of_kind: 'Client Next of Kin',
    relation_is_required: 'Relation is required',
    relation: 'Relation',
    click_to_edit: 'Click to Edit',
    exit_editing: 'Exit Editing',
    file: 'File',

    upload_media_message: 'Click here to upload images to patient Media library',

    // Settings -> Registration Portal -> Client Toggles
    setting_registration_portal: 'Registration Portal',
    setting_health_information: 'Health Information',
    setting_field: 'Field',
    setting_view: 'View',
    setting_required: 'Required',
    setting_profile: 'Profile Image',
    setting_name: 'Name',
    setting_email: 'Email',
    setting_date_of_birth: 'Date Of Birth',
    setting_phone: 'Mobile Number',
    setting_occupation: 'Occupation',
    setting_street_address: 'Street Address',
    setting_city: 'City',
    setting_zip_code: 'Zip Code',
    setting_state: 'State',
    setting_country: 'Country',

    // New Validation Message
    date_of_birth_is_required: 'Date of birth is required',
    occupation_is_required: 'Occupation is required',

    processing: 'Processing',
    pen: 'Pen',
    cross: 'Cross',
    circle: 'Circle',

    DELETE_COMPANY_CLIENT_FIELD_1: 'Delete Field',
    DELETE_COMPANY_CLIENT_FIELD_2: 'Do you want to delete',
    DELETE_COMPANY_CLIENT_FIELD_3: 'This process can not be reversed.',

    CustomQuestionnaire: 'Custom Questionnaire',

    name_field_is_required: 'Name field is required',

    paragraph: 'Paragraph',
    heading_1: 'Heading 1',
    heading_2: 'Heading 2',
    heading_3: 'Heading 3',

    penFat: 'Pen Fat',
    crossFat: 'Cross Fat',
    filledCircle: 'Filled Circle',
    gradientCircle: 'Gradient Circle',

    Image: 'Image',
    Text: 'Text',

    add_treatment_template: 'Add Treatment Template',
    add_text_template: 'Add Text Template',

    consent: 'Consent',
    consent_approval: 'Consent approval',
    approve_consent: 'Approve Consent',
    require_consent: 'Require Consent',
    send_consent_mail_client: 'Send Consent Email to Client',
    cancel: 'Cancel',
    consent_title: 'Personal Consent',
    consent_body: `
    <p>
        Consent to handle Personal Information<br>
        Processing of your personal information - Consent.<br>
        I hereby consent to the processing of my data by {company_name}.
        The personal information that will be processed includes the following types:<br>
        {fields}
        <br>
        All information is stored electronically and encrypted at MERIDIQ´s
        (<a href="http://www.meridiq.com" target="_blank" title="" rel="noopener">www.meridiq.com</a>) hosting service.
    </p>
`,
    remove_consent: 'Remove Consent',
    page: 'Page',
    numbers: 'Numbers',
    consent_agree0: 'I hereby consent to the ',
    consent_agree1: 'processing of the personal data',
    consent_agree2: ' that I have provided',
    please_provide_consent: 'Please provide your consent',
    refercode: 'Discount code',
    refercode_subscription: 'Discount code',
    i_accept_the_consent: 'I, {0} hereby accept this consent',
    i_accept_all_the_consent: 'I, hereby accept all the selected consents',
    please_accept_the_consent: 'Please accept the consent',
    companyInvoices: 'Invoices',

    payment_completed: 'Payment Completed',
    payment_failed: 'Payment Failed',

    incomplete_payment_title: 'Payment Failed',
    incomplete_payment_message: 'Your payment for subscription has failed. please try to pay again from invoice or change card and try again.',
    edit: 'Edit',
    expires: 'Expires',
    cardDetails: 'Card Details',
    billingDetails: 'Billing Details',
    superUser: 'super user',
    start: 'Start',
    mandatoryFields: 'Mandatory fields',
    systemSettings: 'System Settings',
    companyInformation: 'Company Information',
    users: 'Users',
    billing: 'Billing',
    status: 'Status',
    invoiceNo: 'Invoice no.',
    amount: 'Amount',
    read_only_title: 'Read Only',
    read_only_message: 'This account is in read-only mode, please update your payment details and pay the outstanding balance to gain access.',

    InvoiceStatusUpcoming: 'Upcoming',
    InvoiceStatusVoid: 'Void',
    InvoiceStatusUncollectible: 'Uncollectible',
    InvoiceStatusPaid: 'Paid',
    InvoiceStatusPending: 'Pending',
    media_max_select: 'You can select upto {0} images',
    super_user_email_when_new_client_register: 'Email notification to super user when client registers via registration portal',
    custom: 'Custom',
    loggedOutMessage: 'Another login has been detected for this account and you have been logged out from this device.',
    only_images_allowed: 'Only images are allowed',
    maintanance_notice: 'Maintanance Notice',
    yes_no: 'Yes/No',
    yes_no_textbox: 'Yes/No with Textbox',
    textbox: 'Textbox',
    dateOfBirthLabel: 'Date of Birth (YYYY-MM-DD)',
    dateOfBirthLabel_custom: 'Date of Birth ({0})',
    inactivate: 'Inactivate',
    activate: 'Activate',
    downgradePlan: 'Downgrade Plan',
    notesFiles_max_select: 'You can select upto :limit files',
    choose_files_here: 'Choose files here to upload',
    beforeAfter: 'Before / After',
    fullscreen: 'Fullscreen',
    side_by_side: 'Side by Side',
    side_by_side_short: 'S|S',
    fifty_fifty: '50|50',
    download: 'Download',
    add_to_client_media: 'Add to Client Media',
    exit: 'Exit',
    upload_before_image: 'Upload before image',
    upload_after_image: 'Upload after image',
    back: 'Back',
    before_after_intro_title: 'Before and After image tool',
    before_after_intro_sub_title: 'Click on the button to start!',

    dontHaveAccountYet: "Don't have an account yet? {0}",
    alreadyHaveAnAccount: 'Already have an account? {0}',
    createAnAccountForFree: 'Sign up for an account and get a 14-day free trial.',
    createAnAccount: 'Create an account',
    confirmPassword: 'Confirm Password',
    letsSetUpYourAccount: "Let's set up your account",
    numberOfEmployees: 'Number of Employees',
    numberOfUsers: 'Number of Users',
    startJourney: 'Start Journey',
    youGet: 'You Get',
    continue: 'Continue',
    forgot_again_message: "Check your email to confirm your address. Didn't receive a confirmation email?",
    forgot_again_resend: 'Click here to resend',
    dashboard_description:
        'To give you a better understanding of our system, you can go through our onboarding videos and contact us for a personalized onboarding service: ',
    onboardingVideos: 'Onboarding videos',
    bookAnOnboardingMeeting: 'Book an onboarding meeting',
    learnMoreAboutMeridiq: 'Learn more about Meridiq',
    watchVideos: 'Watch videos',
    book: 'Book',
    learnMore: 'Learn more',
    team: 'Team',
    managedBy: 'Managed By',
    filter: 'Filter',
    reg_portal_1:
        'Your patients will register themselves using the Registration Portal. After they have filled in all the information, their profile will be created automatically and everything is then prepared for future visits / treatment.',
    reg_portal_2: 'You can send/copy this link to your customers eg via your booking confirmation, e-mail, DM, social media etc...',
    reviewPortal: 'Review portal',
    copiedToClipboard: 'Copied to clipboard',
    failedToCopy: 'Failed to copy',
    poweredBy: 'Powered by {0}',
    approved: 'Approved',
    not_approved: 'Not Approved',
    records: 'Records',
    beforeOrAfter: 'Before / After',
    ViewAll: 'View All',
    backToClientDashboard: "Back to client's dashboard",
    addARecord: 'Add a Record',
    selectToAddARecord: 'Select to add a record',
    addAQuestionner: 'Add a Questionnaire',
    selectToAddAQuestionner: 'Select to add a questionnaire',
    create: 'Create',
    NotSigned: 'Not Signed',
    unsigned: 'Unsigned',
    files: 'Files',
    send_by: 'Send by',
    editMyPlan: 'Edit my plan',
    MyPlan: 'My Plan',
    updateBillingInfo: 'Update billing info',
    updateCardInfo: 'Update card info',
    RecommendMeridiq: 'Recommend MERIDIQ',
    RecommendMessage1:
        'Invite a friend or colleague to use MERIDIQ. As we get more users, we will recieve more great suggestions for new functions. Help us to grow and develop the system, giving an opportunity for all clinics to afford secure patient record keeping.',
    RecommendMessage2: 'Fill in your colleagues e-mail adress here below to send the invite, and we will take care of the rest.',
    upgradeYourMeridiqPlan: 'Upgrade your MERIDIQ plan',
    upgradePlanPageMessage:
        'To get started, kindly choose your preferred subscription option below and provide your billing address as well as your credit/debit card information in the next step. For a free trial, select 1 User with 21 Clients. Welcome aboard!',
    CurrentPlan: 'Current Plan',

    show_loc_signature: 'Use checkbox when signing letter of consent',
    clientMediaSelect: 'Client Media Library',
    clientBeforeAfterSelectText: 'Click here to upload an image or choose below from the media library',

    not_verified: 'not verified',
    verified: 'Verified',
    this_client_is_verified_by: 'This client is verified by',
    id: 'ID',
    driving_license: 'Driving License',
    passport: 'Passport',
    other: 'Other',
    note_is_required: 'notes is required',
    PersonalID: 'Swedish Personal ID',
    PersonalID_with_format: 'Swedish Personal ID (YYYYMMDD-XXXX)',
    PersonalID_format: 'YYYYMMDD-XXXX',
    PersonalID_with_format_not_valid: 'Please provide valid Swedish personal ID',
    personal_id_is_required: 'Swedish Personal ID is required',

    danishId: 'Danish CPR number',
    danishId_with_format: 'Danish CPR number (DDMMYY-XXXX)',
    danishId_format: 'DDMMYY-XXXX',
    danishId_with_format_not_valid: 'Please provide valid Danish CPR number',
    danishId_is_required: 'Danish CPR number is required',

    // Booking calendar
    calendar: 'Calendar',
    booking_calendar: 'Booking Calendar',
    schedule_calendar: 'Schedule Calendar',
    services: 'Services ',
    booked_time_slot: 'Booked Time Slot',
    time_client_booked: 'Time Client Booked',
    practitioner: 'Practitioner',
    booking_records: 'Booking Records',
    add_client: 'Add Client',
    upload_profile_picture: 'Upload Profile Picture',
    category: 'Category',
    category_name: 'Category Name',
    add_service: 'Add Service',
    update_service: 'Update Service',
    add_category: 'Add Category',
    update_category: 'Update Category',
    service: 'Service',
    color: 'Color',
    price: 'Price',
    duration: 'Duration',
    category_name_is_required: 'Category name is required',
    viewMore: 'View More',
    service_name_is_required: 'Service name is required',
    category_is_required: 'Category is required',
    color_is_required: 'Color is required',
    price_is_required: 'Price is required',
    duration_is_required: 'Duration is required',
    booking_page: 'Booking Page',
    go_to_booking_page: 'Go To Booking Page',
    booking_page_1:
        'Your clients will choose the service they need and a convenient time of the booking. Additionally, they will complete the form you have requested for their personal information. A booking confirmation will be sent to them once the reservation is complete.',
    start_booking: 'Start Booking',
    en: 'EN',
    select_a_service: 'Select a Service',
    select_your_practitioner: 'Select Your Practitioner',
    your_information: 'Your Information',
    full_name: 'Full Name',
    date_and_time: 'Date and Time',
    booking_page_3_note_1: 'Please let us know if you have any special requests. Thank you.',
    booking_page_3_note_2: 'You are booking an appointment with {0}:',
    booking_page_3_note_3: 'I approve the {0} and {1}',
    booking_page_3_note_4: 'booking policy and privacy policy.',
    booking_page_booking_policy: 'Booking policy',
    booking_page_privacy_policy: 'Personal data policy',
    booking_page_1_note_1:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Dictumst consectetur egestas ipsum aliquam dictum amet morbi aliquam.',
    book_a_service: 'Book a Service',
    booking_settings: 'Booking Settings',
    record_settings: 'Record Settings',
    email_settings: 'Email Settings',
    email_settings_note: 'Email settings for bookings, cancellations, etc., for clients and practitioners',
    scheduling_policy: 'Scheduling Policy',
    scheduling_policy_note: 'Settings for when customers can book services',
    time_increments: 'Time increments',
    time_increments_note: 'Show available times in increments of:',
    minimum_lead_time: 'Minimum lead time',
    minimum_lead_time_note: 'This is how long preparation you want for each appointment.Maximum 24 hours.',
    minimun_lead_time_note2:
        'Example: Are you able to respond within one hour for a booking? If not, you should give at least a 1-hour minimum lead time.',
    maximum_lead_time: 'Maximum lead time',
    maximum_lead_time_note: 'Maximum number of days in advance that a booking can be made.',
    internal_booking_confirmation: 'Internal Booking Confirmation',
    internal_booking_confirmation_note: 'When a client books a service an internal confirmation is sent to this email.',
    internal_booking_cancellation: 'Internal Booking Cancellation',
    internal_booking_cancellation_note: 'When a client cancels a booked an internal confirmation of the cancellation is sent to this email.',
    client_email_reminder: 'Client Email Reminder',
    client_email_reminder_note: 'Configure how long in advance an email reminder of a booking is sent out to the client.',
    booking_policy: 'Booking Policy',
    paste_your_own_url: 'Paste your own URL to your booking policy page',
    use_text_below: 'OR, use text below as your booking policy',
    enter_url: 'Enter URL',
    enter_your_personal_data: 'Enter your personal data collection and usage terms here',
    custom_booking_text: 'Custom Booking Text',
    custom_booking_text_note: 'Add text here to include it in client booking confirmation',
    security: 'Security',
    security_note: "Verify client's email address",
    booking_widget: 'Booking Widget - Embed on your website',
    booking_widget_note:
        'Use this code on your own website to display the booking function. Copy the code and enter it directly into your web page or send to your web designer.',
    company_theme_color: "Company's Theme Color",
    company_theme_color_note: 'Choose the color of the registration portal and booking page',
    background_image: 'Background Image',
    upload_your_own_cover_image: 'Upload your own background image to the registration portal and booking page',
    file_size: 'JPG and PNG are allowed. Maximum file size 5 MB.',
    more_colors: 'More colors',
    less_colors: 'Less colors',
    confirm_booking: 'Confirm Booking',
    otp_confirmation: 'A OTP(one time password) has been sent to',
    please_enter_otp_below: 'Please enter the OTP below to confirmation a booking.',
    require_to_any_help: 'If you require to any type of help. {0}',
    contact_us: 'Contact us',
    resend_otp: 'Resend OTP',
    your_booking_confirmed: 'Your booking is confirmed!',
    booking_confirmed_note: 'Please check out the following booking information',
    placeholder_note: 'Note : Please arrive at least 5 minutes before your appointment time.',
    reschedule: 'Reschedule',
    new_booking: 'New Booking',
    schedule: 'Schedule',
    backToTeamDashboard: "Back to Team's dashboard",
    bookings: 'Bookings',
    system_access: 'System Access',
    available_for_booking: 'Available for Booking',
    available_for_booking_note: 'Once you allow a user access to booking, he/she will be able to access to the calendar and booking schedules.',
    client: 'Client',
    hour: 'hour',
    hours: 'Hours',
    set_unavailable_time: 'Set Unavailable Time',
    Unavailable_time: 'Unavailable Time',
    all_day: 'All Day',
    start_time: 'Start Time',
    end_time: 'End Time',
    start_date_is_required: 'Start date is required',
    start_time_is_required: 'Start time is required',
    end_date_is_required: 'End date is required',
    end_time_is_required: 'End time is required',
    business_hours: 'Business Hours',
    closed: 'Closed',
    time_slot: 'Time Slot',
    add_new_booking: 'Add New Booking',
    update_booking: 'Update Booking',
    client_is_required: 'Client is required',
    service_is_required: 'Service is required',

    practitioner_is_required: 'Practitioner is required',
    booking_detail: 'Booking Detail',
    send_booking: 'Send Booking',
    client_name: 'Client Name',
    client_email: 'Client Email',
    client_phone_number: 'Client Phone Number',
    client_address: 'Client Address',
    booking_date: 'Booking Date',
    description: 'Description',
    this_booking: 'this booking',
    booked: 'Booked',
    client_booking_records: 'Client Booking Records',
    please_select_practitioner_first: 'Please Select Practitioner First',
    send_email: 'Send Email',
    booking_confirmation: 'Booking Confirmed!',
    cancel_booking_massage: 'Are you not interest to schedule meeting,',
    cancel_booking: 'Cancel Booking',
    days: 'Days',
    booking: 'Booking',
    schedule_note: 'Schedule expires in 3 days, plan ahead',
    select_a_user: 'Select a user',
    copy_procedure_question: 'Do you want to copy this procedure?',
    open: 'Open',
    close: 'Close',
    open_close: 'O/C',
    save_as_template: 'Save as Template',
    save_weekly_template: 'Working Schedule Editor',
    apply_weekly_template: 'Apply Weekly Template',
    select_date: 'Select Date',
    select_users: 'Select Users',
    choose_date: 'Choose Date',
    available_timeslots: 'Available Timeslots',
    code: 'code',
    available_practitioners: 'Available Practitioners at',
    today: 'Today',
    service_access_changed_successfully: 'Service access changed successfully',
    minutes: 'minutes',
    other_services: 'Other Services',
    file_size_error: 'Picture size should be less than 5 MB',
    booking_policy_text: 'Booking and Privacy Policy',
    daysNameShort: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'],
    bookable_services: 'Bookable Services',
    categories: 'Categories',
    Daily: 'Daily',
    DailyShort: 'D',
    Weekly: 'Weekly',
    WeeklyShort: 'W',
    Monthly: 'Monthly',
    MonthlyShort: 'M',
    Yearly: 'Yearly',
    reset: 'Reset',
    booking_beta_modal_1:
        "You now have a Booking System! As it is already a part of your subscriptions, you have unlimited access to your new service.Our customers have deemed this a top request, and we continuously strive to serve them. Simply click on your Booking System at the bottom of the main Menu to activate it. Don't be afraid to let us know your requirements (mail us at {0}) because we will keep adding features to your booking system based on your feedback. We will then make every effort to make it happen.",
    booking_beta_modal_2: 'Email us at {0} with your ideas for improvements!',
    create_schedule: 'Create Schedule',
    sunday: 'Sunday',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    day_of_week: 'Day Of Week',
    working_hours: 'Working Hours',
    add_another_interval: 'Add another interval',
    custom_recurrence: 'Custom Recurrence',
    repeat_every: 'Repeat every',
    ends: 'Ends',
    after: 'After',
    never: 'Never',
    week: 'Week',
    occurrences: 'occurrences',
    booking_portal_weekly_view_note: 'This week is fully booked. Please see the next available date: {0}',
    booking_portal_weekly_view_note_2: 'We apologize, but there are no available time slots at the moment.',
    special_request: 'Special Request',
    special_request_note: 'Information shared with client',
    show: 'Show',
    no_show: 'No Show',
    company_text_color: 'Use black text instead of white :',
    remove: 'Remove',
    time: 'Time',
    am: 'AM',
    pm: 'PM',
    find_slot: 'Go to next available time',
    no_show_comment: 'No Show Comment',
    remove_no_show: 'Remove No Show',
    working_hours_save: 'If you previously have created scheduled days, those will be overwritten with this new schedule',
    this_practitioner_is_deleted: 'This Practitioner is Deleted',
    please_select_one_option: 'Please select one option',
    start_end_time_not_null: 'start-end time is not a null',
    please_at_list_minimum_one_number_input: 'Enter at least one number',
    card_not_found:
        'We have recently made updates in your account regarding invoicing. We need your help to update or confirm your latest credit card details. Please click here to update this information.',
    note_questionnaire: 'Fill out a questionnaire about your client here',
    note_send_info: 'E-mail information to your client here',
    note_records: 'Create a record or sign a consent form here',
    note_media: "Add your client's media here",
    add_calendar: 'Add Calendar',
    copyright: 'Copyright © 2024',
    modify_booking: 'Modify Booking',
    we_are_looking_forward: 'We are looking forward to seeing you next time!',
    cancel_booking_note: "You've canceled the booking",
    schedule_updated: 'Schedule Updated!',
    registration_settings: 'Registration Settings',
    record_system: 'Record System',
    booking_system: 'Booking System',
    pos_system: 'POS System',
    pos: 'POS',
    convert_to_super_user_desc: 'Convert to super user',
    change_to_super_user_desc:
        "by clicking yes, your role will be changed to Admin and this user's role will become Super User. this process is irreversible, if you want to become Super User again then you have to contact a new Super User for that.",
    logged_out: "You've been logged out",
    logged_out_desc: 'Please log back in',
    first_day_in_week: 'First day in week',
    booking_appointment: 'Booking Appointment',
    ical_file: 'iCal File',
    merge_clients: 'Merge Clients',
    congratulations: 'Congratulations!',
    bookable_category: 'Bookable Category',
    configurations: 'Configurations',
    merge_client_accounts: 'Merge Client Accounts',
    merge_client_account_note:
        'By merging the client accounts, all the records of two accounts including procedure, consents, questionnaires will be saved in one account.',
    choose_name: 'Choose Name',
    choose_email_address: 'Choose Email Address',
    choose_phone_number: 'Choose Phone Number',
    choose_date_of_birth: 'Choose Date of Birth',
    choose_occupation: 'Choose Occupation',
    close_merge: 'Close Merge',
    general_setting: 'General Settings',
    choose_personal_id: 'Choose Personal Id',
    choose_country: 'Choose Country',
    choose_state: 'Choose State',
    choose_city: 'Choose City',
    choose_zip_code: 'Choose Zip Code',
    choose_street_address: 'Choose Street Address',
    merge_confirm_note: 'I {0} confirm that this merge of two clients is correct and may proceed. Its not possible to reverse the merge.',
    client_name_required: 'Client name is required',
    merge: 'Merge',
    client_seleted: '{0} selected',
    filter_on_service: 'Filter on service',
    none: 'None',
    merge_error: 'Please select two clients',
    merge_error_1: 'Only two clients can be selected',
    recurrence: 'Recurrence',
    does_not_repeat: 'Does not repeat',
    repeat_every_other_week: 'Repeat every other week',
    repeat_weekly: 'Repeat weekly',
    client_download_message:
        'The download of a client record may take some time to finish, please do not start another export or refresh or close MERIDIQ. The download should start automatically when finished.',
    clear_schedule: 'Clear Schedule',
    clear_schedule_msg: 'This removes existing schedule from user(s).',
    ends_is_required: 'Ends is required',
    after_occurence_is_required: 'After occurrence is required',
    get_direction: 'Get Direction',
    time_margin: 'Time Margin',
    not_supported: 'File preview is not available',
    suggested_time: 'Suggested Time',
    custom_time: 'Custom Time',
    check_for_update: 'Check for update',
    up_to_date: 'Up to date',
    book_client: 'Book client',
    private_time: 'Private time',
    users_selected: 'Users selected',
    non_blocking: 'Non-blocking',
    blocks_bookings: 'Blocks Bookings',
    please_at_list_one_option_selected: 'Please at list one option selected',
    maximum_concurrent_bookings: 'Maximum concurrent bookings',
    use_custom_date_time: 'Custom date and time',
    enter_valid_time: 'Enter valid time',
    we_are_sorry_to_see_you_go: 'We are sorry to see you go',
    cancel_subscription_info:
        'Your request has been sent to our support department which will shortly be in contact with you.Thank you so much for your patience.',
    date_format: 'Date Format',
    cancel_private_time: 'Cancel Private Time',
    cancel_subscription_note: 'Are you sure you still want to cancel your subscription?',
    congrats: 'Congrats',
    sign_procedure: 'Sign Procedure',
    sign_note: 'Sign Note',
    sign_letter_of_consent: 'Sign Letter of Consent',
    save_as_draft: 'Submit Draft',
    save_and_sign: 'Submit and Sign',
    start_time_end_time_equal: 'Start time and end time can not be a same',
    end_time_start_time_greater: 'End time must be greater than start time',
    yearly_save: 'Yearly (Save 20%)',
    yearly_subscription: 'Yearly Subscription (excl. VAT)',
    can_we_publish_your_before_and_after_pictures: 'Can we publish your before and after pictures?',
    letter_of_consent_option1: 'I do not authorize',
    letter_of_consent_option2: 'I do authorize publishing before/after pictures with just the part where I did the treatment',
    letter_of_consent_option3: 'I do authorize publishing before/after pictures with eyes covered',
    letter_of_consent_option4: 'I do authorize publishing before/after pictures',
    select_option: 'Select a option',
    about_publishing_your_before_after_picture: 'About publishing your before/after pictures',
    publishing_before_after_picture: 'Publishing before/after picture',
    booking_time_confirm_note: 'The practitioner is busy during the selected time slot. Book anyway?',
    from: 'From',
    to: 'To',
    type: 'Type',
    sign_health_questionnaire: 'Sign Health Questionnaire',
    sign_aestethic_interest: 'Sign Aesthetic Interest',
    sign_covid_19questionnaire: 'Sign Covid-19 Questionnaire',
    sign_quetionnaire: 'Sign Questionnaire',
    show_fields: 'Show Fields',
    last_added: 'Last Added',
    last_updated: 'Last Updated',
    last_viewd: 'Last Viewed',
    booking_reschedule_message: 'Do you wish to send an email to the client regarding the reschedule?',
    default: 'Default',
    total: 'Total',
    completed: 'Complete',
    client_everyone: 'Client - Everyone',
    booking_everyone: 'Booking - Everyone',
    top_ten_client_bookings: 'Top 10 Client Bookings',
    most_booking: 'Most Bookings',
    most_cancellations: 'Most Cancellation',
    most_no_shows: 'Most No Show',
    team_member: 'Team Member',
    everyone: 'Everyone',
    start_date_end_date_message: 'The end date must be a date after or equal to start date.',
    selected: 'Selected',
    select_services: 'Select Services',
    old_time: 'Old time',
    new_time: 'New time',
    booking_drop_popup_msg: 'Send an email to the client regarding the reschedule?',
    number_of_clients: 'Number of Clients',
    number_of_bookings: 'Number of Bookings',
    do_you_want_to_change_this_booking: 'Do you want to change this booking?',
    view_services: 'View Services',
    ok: 'Ok',
    services_schedule: 'Services Schedule',
    top_ten_services: 'Top 10 Booking Services',
    most_popular: 'Most Popular',
    least_popular: 'Least Popular',
    booking_count: 'Count',
    all_services: 'All Services',
    UnlimitedClients: 'Unlimited Clients',
    import: 'Import',
    unique: 'Unique',
    successfully_imported: 'Successfully imported',
    not_imported: 'Not imported',
    select_fields: 'Select fields',
    select_required_fields: 'Select all required fields',
    practitioner_name_or_email_required: 'Practitioner name or Practitioner E-mail is required',
    download_sample: 'Download Sample',
    showing_out_of: 'Showing {0} out of {1}',
    interval: 'Interval',
    show_text_import_client:
        "We are happy to assist you in importing your clients. If you require help with this process, please don't hesitate to send us an email (<EMAIL>) and attach your file you wish to import. It's important to note that there may be a charge for this service if we need to adapt the file to our standard.",
    show_text_import_booking:
        "We are happy to assist you in importing your bookings. If you require help with this process, please don't hesitate to send us an email (<EMAIL>) and attach your file you wish to import. It's important to note that there may be a charge for this service if we need to adapt the file to our standard.",
    service_name: 'Service Name',
    service_price: 'Service Price',
    service_duration: 'Service Duration',
    client_first_name: 'Client First Name',
    client_last_name: 'Client Last Name',
    client_mobile_number: 'Client Mobile Number',
    practitioner_name: 'Practitioner Name',
    practitioner_email: 'Practitioner Email',
    client_personal_id: 'Client Personal ID',
    error: 'Error',
    no_practitioner: 'No Practitioner',
    timezone: 'Time Zone',
    free_user_for_record: '14 days free trial',
    free_user_for_booking: 'Free, unlimited users and clients',
    set_up_system_error: 'Please select at least one system ',
    available_for_record: 'Available for Record',
    unlimited_clients: 'Unlimited clients',
    record: 'Record',
    unlimited_users: 'Unlimited users',
    nrs_rating: 'NRS rating',
    enable_nrs_rating_in_procedure_records: 'Enable NRS rating in procedure records',
    fill_out_questionnaires_and_letters_of_consent: 'Fill out Questionnaires and Letters of Consent',
    added_and_signed_letters_of_consent: 'Added and signed Letters of Consent',
    please_fill_all_letter_of_consent: 'Please fill all letter of consents',
    questionnaire_and_loc_reminder: 'Questionnaire and Letter of Consent Reminder',
    questionnaire_and_loc_reminder_note:
        'Configure how long in advance an email reminder to fill out questionnaire and letter of consent (connected to the service they booked) is sent out to the client.',
    sms_questionnaire_and_loc_reminder_note:
        'Configure how long in advance an sms reminder to fill out questionnaire and letter of consent (connected to the service they booked) is sent out to the client.',
    sms_questionnaire_and_loc_reminder_template: 'Select Questionnaire and Letter of Consent reminder template',
    select_which_template_that_will_be_used: 'Select which template that will be used.',
    select_sms_reminder_template: 'Select SMS reminder template',
    booking_reschedule: 'Booking Rescheduled',
    booking_reminder: 'Booking Reminder',
    same_personal_id: 'Same Personal Id',
    unlimited: 'Unlimited',
    free_to_use: 'Free to use (Life time)',
    unlimited_bookings: 'Unlimited bookings',
    unlimited_schedules: 'Unlimited schedules',
    unlimited_patients: 'Unlimited patients',
    unlimited_records: 'Unlimited records',
    unlimited_storage: 'Unlimited storage',
    unlimited_products: 'Unlimited products',
    cancel_anytime: 'Cancel anytime',
    yearly_contract: 'Yearly contract',
    billed_annually: 'Billed annually',
    billed_monthly: 'Billed monthly',
    unlimited_transactions: (fee: string | number) => `Unlimited transactions (${fee}% fee per transactions)`,
    sms_from_per_messsage: (price: string | number) => `SMS (from ${price} per message)`,
    including_booking_system: 'Including booking system',
    no_of_users: (no: string | number) => `${no} User(s)`,
    fourteen_day_trial: '14 Days Free Trial',
    generating: 'Generating',
    preparing: 'Preparing',
    use_bank_id_verification: 'BankID verification for clients',
    verified_with_bankid: 'Verified with BankID',
    verifie_with_bankid: 'Verified with BankID',
    mandatory_bank_id: 'Mandatory BankID',
    sign_with_bank_id: 'Sign with BankID',
    bank_id_modal_title: 'Open BankID app and press the scan QR icon.',
    bank_id_modal_title_2: 'Then scan this QR code:',
    bank_id_open_msg: 'Open BankID on this device instead',
    bank_id_required: 'Complete bankId verification',
    bank_id_verification: 'BankID Verification',
    confirm_your_bank_id: 'Confirm your Bank ID',
    prescription_sign_bankid_message: 'Since your BankID is verified, directly sign with BankID',
    restart: 'Restart',
    write_your_message: 'Write your messsage',
    select_questionnaires_loc_or_file: 'Select Questionnaires, LOC or File',
    notes: 'Notes',
    send_questionnaires_loc_or_File: 'Send SMS, Questionnaires, LOC or File',
    your_organization_has_some_duplicate_clients: 'Your organization has some duplicate clients',
    you_have_filled_out_the_questionnarie_and_letter_of_consent: 'You have filled out the Questionnaire and Letter of Consent.',
    completed_questionnaire_and_loc: 'Completed Ques./LoC',
    letters_of_consent: 'Letters of Consent',
    thank_you_for_filling_out_the_requested_information: 'Thank you for filling out the requested information.',
    already_used: 'Already Used',
    free: 'Free',
    skip_for_now: 'Skip for now',
    ff_days_free_trial: '14 Days FREE Trial',
    have_a_promocode: 'Have a Promocode?',
    no_card_details_needed: 'no card details needed',
    no_card_details_needed_till_free_trial: '14 day trial period included',
    copy: 'Copy',
    copied: 'Copied',
    paste: 'Paste',
    link_to_portal: 'Link to Portal',
    convert_to_super_user: 'Convert to Super User',
    logs: 'Logs',
    list: 'List',
    password_about_to_expire: 'Password is about to expire!',
    password_will_expire_in_days: 'Your current password will expire in {0} days.',
    password_expired: 'Password Expired',
    remind_me_later: 'Remind me later',
    password_expired_update_notice: 'Your password is expired, please update your password to keep using your account.',
    questionary_note: 'Hi {0}, please read the attached document that {1} sent you.',
    please_provide_valid_url: 'Please provide valid url',
    select_amount: 'Select Amount',
    group_booking_quantity: 'Group booking, quantity',
    type_of_booking: 'Type Of Booking',
    select_booking: 'Select Booking',
    single: 'Single',
    group: 'Group',
    currency: 'Currency',
    internal_booking_confirmation_to_practitioner_email: 'Internal booking confirmation to practitioner email',
    select_date_range: 'Select date range',
    clear_schedule_date_error: 'The end date cannot be before the start date.',
    maximum_client_limit_reached: 'Maximum client limit reached',
    this_booking_for: 'this booking for {0}',
    booking_client_email: 'Client Email',
    booking_cancellation: 'Booking cancellation',
    booking_cancellation_note: 'When a client cancels a booking, a confirmation of the cancellation is sent to this email.',
    email_reminder: 'Email Reminder',
    email_reminder_note: 'How long in advance an email reminder of a booking is sent out to the client.',
    internal_email: 'Internal Email',
    booking_confirmed: 'Booking Confirmed!',
    re_verify: 'Re Verify',
    internal_booking_confirmation_to_practitioner_email_note:
        'When a client books a service, confirmation of the booking is sent to practitioner email.',
    internal_booking_cancellation_to_practitioner_email: 'Internal booking cancellation to practitioner email',
    internal_booking_cancellation_to_practitioner_email_note:
        'When a client cancels a booked, confirmation of the cancellation is sent to practitioner email.',
    internal_booking_reschedule_to_practitioner_email: 'Internal booking reschedule to practitioner email',
    internal_booking_reschedule_to_practitioner_email_note:
        'When a client reschedules a booked service, confirmation of the reschedule is sent to practitioner email.',
    internal_booking_reschedule: 'Internal Booking Reschedule',
    internal_booking_reschedule_note: 'When a client reschedules a booked service, an internal confirmation of the reschedule is sent to this email.',
    sms: 'SMS',
    sms_credits: 'SMS/Video Credits',
    this_booking_cancelled: "The booking has been canceled, you don't need to fill out the requested information.",
    booking_cancelled: 'Booking Cancelled',
    booking_cancel_text: 'Are you sure you want to cancel this booking?',
    we_have_released_some_new_features: 'We have released some new features!',
    please_click_reload_to_update: 'Please click reload to update',
    reload: 'Reload',
    your_booking_cancelled: 'Your booking is cancelled!',
    group_booking_quantity_error: 'Group booking quantity is required',
    signed: 'Signed',
    not_signed: 'Not Signed',
    prescriptions: 'Prescriptions',
    prescription: 'Prescription',
    update_prescription: 'Update Prescription',
    prescription_delete_msg: 'Are you sure you want to inactive this prescription? This action cannot be undone.',
    prescription_inactivated: 'The prescription has been inactivated',
    end_date_message: 'The end date must be a date after today.',
    needs_prescription: 'Needs Prescription',
    prescribed: 'Prescribed',
    cancellation_and_modify: 'Cancellation and Modify',
    cancellation_and_modify_text: 'Set the minimum time for how far in advance a booking can be cancelled or modified.',
    cancellation_and_modify_client_text:
        'It is not possible to cancel or modify this booking due to the cancellation and modify policy for :clinic_name. Please contact :clinic_name for more information.',

    write_here: 'Write here',
    add_prescription: 'Add Prescription',
    select_prescriptions: 'Select Prescriptions',
    new_prescription: 'New Prescription',
    send_prescription: 'Send Prescription',
    booking_portal: 'Booking Portal',
    reason_for_cancellation: 'Reason for cancellation',
    i_am_discontinuing_my_business: 'I am discontinuing my business',
    i_will_start_using_another_service_instead_of_meridiq: 'I will start using another service instead of MERIDIQ',
    please_tell_us_to_which_system_youre_changing_and_why: "Please tell us to which system you're changing and why.",
    i_m_not_satisfied_with_the_meridiq_service: "I'm not satisfied with the MERIDIQ service",
    other_reason: 'Other reason',
    do_you_want_a_read_only_access_to_your_account:
        'Do you want a read-only access to your account. This allows you to keep access to your clients but in a read only mode and you don’t have an active subscription.',
    cancel_subscription_read_only_note1:
        'If you proceed with this option, an email has been <NAME_EMAIL> that will get back to you with an offer to keep a read-only access to your account.',
    cancel_subscription_read_only_note2:
        'If you proceed with this option, your account and all its data will be deleted on the end of the subscription period.',
    consent_on_data_deleting: 'Consent on Data Deleting',
    consent_on_date_deleting_note1:
        "As per the termination of our subscription agreement, we understand that our data will be permanently deleted and will not be recoverable in any form. Please find below the necessary details to identify our clinic's account:",
    superuser_email: 'Superuser email',
    subscription_termination_date: 'Subscription Termination Date',
    consent_on_date_deleting_note2:
        'We affirm that we have taken all necessary steps to back up any essential data on our end, and we are aware that once the data is deleted, it cannot be restored or retrieved in any manner.',
    consent_on_date_deleting_note3:
        "By providing this consent, we release your online service from any further obligations related to the storage and maintenance of our clinic's data after the termination date mentioned above.",
    i_agree: 'I Agree',
    please_tell_us: 'Please tell us',
    day_left: 'Day(s) left',
    consent_on_date_deleting_note4:
        'After you have approved the consent, you will receive an email to the provided email address. You will need to approve this message to complete the process of canceling your subscription.',
    cancellation_verified: 'Cancellation verified',
    cancellation_verified_note: 'You have now verified your cancellation.',
    cancellation_verified_note_1: 'Your account and its data will be deleted on the expiration date.',
    cancellation_verified_note_2: 'You are always welcome to join MERIDIQ again!',
    ends_at: 'Ends at',
    the_record_system: 'The record system has a 14-day trail.',
    skip_for_later: 'Skip for later',
    cancellation_email_note: `As per the termination of our subscription agreement, we understand that our data will be permanently deleted and will not be recoverable in any form. Please find below the necessary details to identify our account:`,
    cancellation_email_notes_1: `We affirm that we have taken all necessary steps to back up any essential data on our end, and we are aware that once the data is deleted, it cannot be restored or retrieved in any manner.`,
    cancellation_email_notes_2: `By providing this consent, we release your online service from any further obligations related to the storage and maintenance of our data after the termination date mentioned above.`,
    sign_prescription: 'Sign Prescription',

    smsSettings: 'SMS Settings',
    smsSettingsDescription: 'SMS settings for bookings, cancellations, etc., for clients and practitioners',
    clientSMS: 'Client SMS',
    smsReminder: 'SMS Reminder',
    smsReminderDescription: 'How long in advance an SMS reminder of a booking is sent out to the client.',
    smsBookingConfirmation: 'SMS booking confirmation',
    smsBookingConfirmationDescription: 'A SMS will be sent directly after a booking is confirmed.',
    selectBookingConfirmationTemplate: 'Select booking confirmation template',
    selectBookingConfirmationTemplateDescription: 'Select which template that will be used.',
    selectBookingReminderTemplate: 'Select SMS reminder template',
    selectBookingReminderTemplateDescription: 'Select which template that will be used.',
    smsBookingReminder: 'SMS booking reminder',
    smsBookingReminderDescription: 'A SMS reminder of a booking is sent out to the client',
    smsBookingCancellation: 'SMS booking cancellation',
    smsBookingCancellationDescription: 'A SMS will be sent to the client to confirm the cancellation of a booking.',
    selectBookingCancellationTemplate: 'Select booking cancellation template',
    selectBookingCancellationTemplateDesc: 'Select which template that will be used.',
    internalSMS: 'Internal SMS',
    internalSMSDescription: 'SMS booking confirmation',
    internalSMSBookingCancellation: 'SMS booking cancellation',
    internalSMSBookingCancellationDescription: 'When a client cancels a booking, a SMS of the cancellation is sent to the practitioner.',
    internalSelectBookingConfirmationTemplate: 'Select booking confirmation template',
    internalSelectWhichTemplateWillBeUsed: 'Select which template will be used',
    internalWhenAClientCancelsABooking: 'When a client cancels a booking, an SMS of the cancellation is sent to the practitioner.',
    internalSelectBookingCancellationTemplate: 'Select booking cancellation template',
    internalSMSBookingConfirmation: 'SMS booking confirmation',
    internalSMSBookingConfirmationDescription: 'When a client books a service, an SMS confirmation of the booking is sent to the practitioner.',
    internalSMSBookingConfirmationTemplate: 'Select booking confirmation template',
    internalSMSBookingConfirmationTemplateDescription: 'Select which template that will be used.',
    internalSMSBookingCancellationTemplate: 'Select booking cancellation template',
    internalSMSBookingCancellationTemplateDescription: 'Select which template that will be used.',
    create_template: 'Create Template',
    update_template: 'Update Template',
    delete_sms_template_message: 'Do you want to delete {0}?',
    de_acive_sms_template_message: 'Are you sure you want to inactivate {0}?',
    re_acive_sms_template_message: 'Are you sure you want to activate {0}?',
    prescriber: 'Prescriber',
    join_request_sent: 'Join Request Sent',
    resend_join_request: 'Resend Join Request',
    user_prescriber_note:
        'Prescriber will get email for join request, Prescriber will be added in this team once they approve join request. request will expiry after 24 hours.',
    send_join_request: 'Send Join Request',
    apply_promo_code: 'Apply Promo code',
    apply: 'Apply',
    promo_code: 'Promo code',
    booking_history: 'Booking History',
    booking_count_error: 'Max {0} clients allowed',
    single_booking: 'Single Booking',
    select_practitioner_for_the_time_slot: 'Select a practitioner for the {0} time slot.',
    change: 'Change',
    text_is_required: 'Text is required',
    template_details: 'Template details',
    send_sms_email: 'Send SMS/Email',
    send_sms: 'Send SMS',
    create_sms_template: 'Create SMS Template',
    update_sms_template: 'Update SMS Template',
    sms_text: 'SMS text',
    insert_value: 'Insert value',
    please_enter_sms: 'Please enter SMS text',
    clinic: 'Clinic',
    not_prescribed: 'Not Prescribed',
    client_details: 'Client Details',
    pending_prescription: 'Pending Prescription ',
    fees: 'Fees',
    clinics: 'Clinics',
    clinic_detail: 'Clinic Detail',
    stop_service: 'Stop Service',
    meridiq_billing: 'Meridiq Billing',
    clinic_billing: 'Clinic Billing',
    billing_status: 'Billing Status',
    revenue: 'Revenue',
    meridiq_fee: 'Meridiq Fee',
    net_earning: 'Net Earning',
    signed_prescriptions: 'Signed Prescriptions',
    earning: 'Earning',
    paid: 'Paid',
    pending: 'Pending',
    profile_information: 'Profile Information',
    system_setting: 'System Setting',
    change_password: 'Change Password',
    receive_email_notification: 'Receive email notification',
    receive_email_notification_note: `When receive  request for prescription, notification email sent to the MAL's email`,
    receive_reports_on: 'Receive email reports on',
    receive_reports_on_note: 'You will get reports on added email',
    authentication_2fa: '2FA Authentication',
    authentication_2fa_note:
        'Two Factor Authentication, or 2FA, is an extra layer of protection used to ensure the security of online accounts beyond just a username and password. ',
    current_password: 'Current Password',
    confirm_new_password: 'Confirm New Password',
    update_fees: 'Update Fees',
    stopped_service: 'Stopped Service',
    prescription_default_fees: 'Prescription Default Fees',
    current_password_is_required: 'Current Password is required',
    new_password_is_required: 'New Password is required',
    confirm_new_password_is_required: 'Confirm New Password is required',
    email_otp: 'Email OTP',
    resend: 'Resend',
    request_to: 'Request to',
    monthly_fees_for_meridiq_are_automatically_deducted: 'Monthly fees for Meridiq are automatically deducted.',
    every_month_auto_invoice_will_be_paid:
        'The meridiq fees for each prescription is {0} and it will be deducted from your account on 2nd of each month.',
    request_ticket_to: 'Request ticket to',
    create_support_ticket: 'Create Support Ticket',
    create_support_ticket_note:
        'If you have experienced any issues or want help with anything regarding the application. please fill out the form below and we will get back to you as soon as we can.',
    back_to_clinic_dashboard: `Back to clinic dashboard`,
    stop_service_text:
        "All your existing prescription's data will be inaccessible once you stop your service, are you sure you want to stop the service ?",
    signed_prescription: 'Signed Prescription',
    back_to_prescription_dashboard: `Back to prescription dashboard`,
    prescription_login_terms_text: `By login you're agreeing to our {0} and {1}`,
    privacy: 'Privacy',
    terms_conditions: 'Terms & Conditions',
    doctors: 'Doctors',
    change_card: 'Change card',
    manage_cards: 'Manage Cards',
    add_new_card: 'Add New Card',
    active_in: 'Active in',
    add_card_note:
        'Note: If you select any option avove,it will replace any existing connection can you had with that system(your card still remain stored in system).',
    hide_on_booking_portal: 'Hide on Booking Portal',
    hide: 'Hide',
    address_is_required: 'Address is required',
    please_verify_discount_code: 'please verify discount code',
    discount_code_is_invalid: 'Discount code is invalid',
    applied: 'Applied',
    generateNew: 'Generate New',
    generatedOn: 'Generated On',
    update_card: 'Update Card',
    update_card_info: 'Update Card Info',
    update_card_info_error: 'It seems that there is some problem with processing payment by your card, please update your card info and try again',
    Send_join_request: 'Send Join Request',
    join_prescriber_note:
        'Prescriber will get email for join request, Prescriber will be added in this team once they approve join request. request will expiry after 24 hours.',
    join_request: 'Join Request',
    accept_request: 'Accept  Request',
    join_request_will_auto_expired_after_hours: 'Join request will auto expired after 24 hours.',
    meridiq_susbscription: 'Meridiq Subscription',
    license_agreement: 'License Agreement',
    otp_sent_successfully: 'We sent you an email containing a verification code.',
    otp_sent: 'OTP sent',
    new_login_term_policy: 'I have read and agree with the {0} & {1}.',
    terms_of_use: 'Terms of Use',
    privacy_policy: 'Privacy Policy',
    i_accept_this_license_agreement: 'I accept this license agreement',
    delete_card: 'Do you want to delete this card?',
    unpaid: 'Unpaid',
    invitation_accepted: 'Invitation Accepted',
    invitation_from: 'Invitation from {0} is accepted. You are now connected with {1}.',
    hi: 'Hi',
    back_to_prescription: 'Back to prescription',

    expired: 'Expired',

    // POS side bar
    sidebar: {
        point_of_sale_system: 'Point of Sale System',
        payments: 'Payments',
        xreport: 'X Report',
        report: 'Report',
        changelog: 'Change log',

        gift_card: 'Gift Card',
        receipt: 'Receipt',
        receipt_detail: 'Receipt Detail',
        product_category: 'Product Category',
        products: 'Products',
        refund: 'Refund',
    },
    send_to: 'Send to',
    date_must_be_after_message: 'The date must be a date after today.',
    prescription_fees_changes_text: 'Your monthly price is increasing to {0} on {1}.',
    prescription_registration_text: 'Contact {0} if you want to register.',
    notification: 'Notification',
    notifications: 'Notifications',
    all_notifications: 'All Notifications',
    mark_all_read: 'Mark All Read',
    read: 'Read',
    unread: 'Unread',
    you_re_all_up_to_date: `You’re all up to date!`,
    type_your_message_here: 'Type your message here.',
    yrs: 'yrs.',
    disable: 'Disable',
    sms_activity: 'SMS Activity',
    sms_credit_left: 'Credits left',
    buy_credits: 'Buy Credits',
    buy: 'Buy',
    auto_renewal: 'Auto Renewal',
    automatic_renewal_when_credit_reach_below: 'Automatic renewal when credit reach below 25.',
    enable_automatic_renewal_when_reaching_below_credits: 'Enable automatic renewal when reaching below 25 credits.',
    please_select_template: 'Please select a template',
    set_auto_renewal: 'Set Auto Renewal',
    are_you_sure_you_want_to_disable_autopay: 'Are you sure you want to disable autopay?',
    sync: 'Sync',
    credit: 'Credit',
    please_select_plan: 'Please select plan.',
    sms_warning_text: 'This SMS have limit of 160 characters, messages that exceed this will be sent as multiple messages and charged accordingly.',
    join_waitlist: 'Join waitlist',
    joined_waitlist: 'Joined waitlist.',
    join_the_waitlist: 'Join the waitlist!',
    join_waitlist_sms_message:
        'SMS feature is not yet available in your country.\nSign up for our waitlist to be notified when our service launches in your country.',
    transaction_charged_currency: 'Transaction will be charged in ',
    free_trail_period: 'Free trail period',
    NO: 'No',
    delete_media: 'Delete',
    communication: 'Communication',
    record_templates: 'Record Templates',
    sms_templates: 'SMS Templates',
    already_joined_the_waitlist: 'Already joined the waitlist',
    select_plan: 'Select Plan',
    discount: 'Discount',
    pos_subscription_note: 'This is the yearly contract, once you subscribe you cannot cancel your plan for one year.',
    add_note: 'Add Note',
    update_note: 'Update Note',
    this_note: 'This Note',
    back_to_booking_detail: 'Back to booking detail',
    detail_is_required: 'Details is required',
    open_hours: 'Open hours',
    open_hours_modal_text: 'Open hours setting will apply on Booking and Schedule.',
    do_you_want_to_delete: 'Do you want to delete :name?',
    do_you_want_to: 'Do you want to',
    main_dashboard: 'Main Dashboard',
    period: 'Period',
    activity: 'Activity',
    back_to_dashboard: 'Back to Dashboard',
    add_new_client: 'Add new client',
    Are_you_sure_you_want_to_delete_this_note: 'Are you sure you want to delete this note - :name?',
    open_link: 'Open Link',
    mark_as_read: 'Mark as read',
    collapse: 'Collapse',
    expand: 'Expand',
    booking_notification: `Booking  notification to it's practitioner`,
    booking_notification_note: `When some customer book a booking, send notification to it's practitioner`,
    questionnaire_notification: `Questionnaire or LOC notification`,
    questionnaire_notification_note: `When use fill-up questionnaire or LOC then we can send notification`,
    reschedule_notification: `Reschedule Notification to it’s practitioner`,
    reschedule_notification_note: `When user reschedule the booking from his/her side, send notification to it’s practitioner`,
    booking_practiotioner_notification: `Note made on booking notification to it’s practitioner`,
    booking_practiotioner_notification_note: `If another practitioner makes a note in higher booking, The practitioner that's booked will be be notified`,
    payment_failed_notification: `Payment Failed notification`,
    payment_failed_notification_note: `If payments fails, the superuser will then receive a notification`,
    notification_settings: 'Notification Settings',
    added_by_xx_on_xx: 'Added by {0} on {1}',
    cancel_your_subscription:
        'If you agree to cancel your subscription, you account and all its data will be deleted on the end of the subscription period, {0}.',
    cancel_your_subscription_agree_text: 'I {0} agree to this',
    booking_details: 'Booking Details',
    free_trial: 'Free trial',
    assigned: 'Assigned',
    block_all: 'Block all',
    enable_all: 'Enable all',
    cancel_plan: 'Cancel plan',
    your_plan: 'Your plan',
    unlimited_trxn: 'Unlimited transactions.',
    fees_per_trxn: 'fee {0}% per transactions.',
    billed_yearly: 'Billed yearly',
    total_per_month: 'Total per month',
    total_per_year: 'Total per year',
    vat: 'Vat',
    back_to_setting: 'Back to setting',
    exp_date: 'Exp. date',
    send_sms_to_client: 'Send SMS to client',
    send_email_to_client: 'Send E-mail to client',
    send_sms_to_practitioner: 'Send SMS to Practitioner',
    portal_group_booking_modify: 'You cannot modify a group booking.',
    client_language: 'Client language',
    add_information: 'Add information',
    at_least_one_field_is_required: 'At least one field is required',
    dont_ask_again_on_this_device: "Don't ask again on this device",
    available_for_pos: 'Available for pos',
    successful_subscribed: 'Successfully Subscribed',
    mobile_number_error: 'The mobile number must be at least 7 characters',
    zip_code_less_than_error: 'The zip code must be at least 11115',
    zip_code_greater_than_error: 'The zip code may not be greater than 98499',
    subscription_error: `You cannot change your plan before {0}`,
    system_language: 'System language',
    company_is_not_verified_to_send_sms: `Note: Your company is not verified to send SMS.`,
    for_verification: '{0} for verification.',
    schedule_sidebar_service_note: 'You can select multiple services and add schedule on any days',
    clear_all: 'Clear all',
    import_data_is_locked: 'Import data is locked',
    import_data_is_locked_note: 'Please contact MERIDIQ Support and they will assist you with the import.',
    mic_access_denied: 'Could not access microphone. Please check your browser settings.',
    portal_link_copied: '{0} portal link copied.',
    note_by: 'Note by',
    email_templates: 'Email Templates',
    create_email_template: 'Create Email Template',
    update_email_template: 'Update Email Template',
    email_verification: 'Email verification',
    meridiq_email_verification: 'MERIDIQ is now verifying your account, we will be in contact with your shortly.',
    add_cancel_note: 'Add Cancel Note',
    enter_details: 'Enter details',
    cancel_note: 'Cancel Note',
    updated_by: 'Updated by',
    note: 'Note',
    emailTemplates: {
        emailBookingConfirmationTitle: 'Email booking confirmation',
        emailBookingConfirmationDescription: 'An email will be sent to the client to confirm a booking.',
        selectBookingConfirmationTemplateTitle: 'Select booking confirmation template',
        selectBookingConfirmationTemplateDescription: 'Select which template that will be used.',
        emailBookingCancellationTitle: 'Email booking cancellation',
        emailBookingCancellationDescription: 'An email will be sent to the client to confirm the cancellation of a booking.',
        selectBookingCancellationTemplateTitle: 'Select booking cancellation template',
        selectBookingCancellationTemplateDescription: 'Select which template that will be used.',
        emailBookingRescheduledTitle: 'Email booking rescheduled',
        emailBookingRescheduledDescription: 'An email will be sent to the client to confirm the rescheduling of a booking.',
        selectBookingRescheduledTemplateTitle: 'Select booking rescheduled template',
        selectBookingRescheduledTemplateDescription: 'Select which template that will be used.',
        emailBookingUpdatedTitle: 'Email booking updated',
        emailBookingUpdatedDescription: 'An email will be sent to the client to confirm the update of a booking',
        selectBookingUpdatedTemplateTitle: 'Select booking updated template',
        selectBookingUpdatedTemplateDescription: 'Select which template that will be used.',
        emailBookingReminderTitle: 'Email booking reminder',
        emailBookingReminderDescription: 'An email will be sent to the client as a reminder for a booking',
        selectBookingReminderTemplateTitle: 'Select booking reminder template',
        selectBookingReminderTemplateDescription: 'Select which template that will be used.',
        emailBookingPractitionerChangedTitle: 'Email booking practitioner changed',
        emailBookingPractitionerChangedDescription: 'An email will be sent to the client to confirm a change of practitioner for a booking',
        selectBookingPractitionerChangedTemplateTitle: 'Select booking practitioner changed template',
        selectBookingPractitionerChangedTemplateDescription: 'Select which template that will be used.',
        emailSignedLOCTitle: 'Email signed LOC',
        emailSignedLOCDescription: 'An email will be sent to the client when a Letter of Consent is signed',
        selectSignedLOCTemplateTitle: 'Select signed LOC template',
        selectSignedLOCTemplateDescription: 'Select which template that will be used.',
        emailAfterCareInstructionsTitle: 'Email LOC and Questionnarie',
        emailAfterCareInstructionsDescription: 'An email will be sent to the client with LOC and Questionnarie instructions',
        selectAfterCareTemplateTitle: 'Select LOC and Questionnarie template',
        selectAfterCareTemplateDescription: 'Select which template that will be used.',
        emailInternalBookingConfirmationTitle: 'Email internal booking confirmation',
        emailInternalBookingConfirmationDescription: 'An email will be sent internally to confirm a booking',
        selectInternalBookingConfirmationTemplateTitle: 'Select internal booking confirmation template',
        selectInternalBookingConfirmationTemplateDescription: 'Select which template will be used for internal booking confirmations.',
        emailInternalBookingCancellationTitle: 'Email internal booking cancellation',
        emailInternalBookingCancellationDescription: 'An email will be sent internally to confirm the cancellation of a booking',
        selectInternalBookingCancellationTemplateTitle: 'Select internal booking cancellation template',
        selectInternalBookingCancellationTemplateDescription: 'Select which template will be used for internal booking cancellations.',
        emailInternalBookingPractitionerChangedTitle: 'Email internal booking practitioner changed',
        emailInternalBookingPractitionerChangedDescription: 'An email will be sent internally to confirm a change of practitioner for a booking',
        selectInternalBookingPractitionerChangedTemplateTitle: 'Select internal booking practitioner changed template',
        selectInternalBookingPractitionerChangedTemplateDescription:
            'Select which template will be used for internal notifications of practitioner changes',
        emailInternalBookingRescheduledTitle: 'Email internal booking rescheduled',
        emailInternalBookingRescheduledDescription: 'An email will be sent internally to confirm the rescheduling of a booking.',
        selectInternalBookingRescheduledTemplateTitle: 'Select internal booking rescheduled template',
        selectInternalBookingRescheduledTemplateDescription: 'Select which template will be used for internal notifications of rescheduled bookings',
        emailClientBookingExtraStepsReminderTitle: 'Email client booking extra steps reminder',
        emailClientBookingExtraStepsReminderDescription:
            'An email will be sent to the client as a reminder for any extra steps needed for their booking',
        selectClientBookingExtraStepsReminderTemplateTitle: 'Select client booking extra steps reminder template',
        selectClientBookingExtraStepsReminderTemplateDescription: 'Select which template will be used for client booking extra steps reminders',
        clientRegisterEmail: 'Email when client registers',
        clientRegisterEmailDesc: 'An email will be sent to the client when they register from portal or created by user',
        selectClientRegistrationTemplate: 'Select client registration template',
        selectInternalBookingRescheduleTemplateTitle: 'Select internal booking reschedule template',
        selectInternalBookingRescheduleTemplateDescription: 'Select which template will be used for internal booking reschedules.',
    },
    rejected: 'Rejected',
    no_card_added: 'No card added',
    your_email_has_been: 'Your email has been {0}.',
    you_will_be_redirected_in: 'You will be redirected in {0}',
    hippa_terms: 'HIPPA Terms',
    payment_summary: 'Payment Summary',
    subtotal: 'Subtotal',
    tax: 'Tax',
    net: 'Net',
    booking_for: 'Booking for',
    gross: 'Gross',
    online_payment: 'Online Payment',
    your_slot_will_be_reserved_for: 'Your slot will be reserved for',
    collect_request_expired: 'Collect request expired',
    please_try_again: 'Please try again',
    refresh: 'Refresh',
    to_check_status_click: 'To check status, click',
    note_do_not_close_this_page_or_press_back: 'NOTE: Do not close this page or press back',
    personal_data_policy: 'Personal Data Policy',
    file_upload: 'File upload',
    file_upload_questionner_max_limit: 'You can select up to {0} files, with a maximum size of {1}MB each.',
    load_default: 'Load default',
    payment_type: 'Payment type',
    online: 'Online',
    offline: 'Offline',
    your_payment_has_failed: 'Your payment has failed.',
    go_to_new_booking: 'Go to new booking',
    note_payment_link_expired: `NOTE: After clicking the 'Pay' button, the payment link will expire in 15 minutes.`,
    connect_calendar: 'Connect calendar',
    connect: 'Connect',
    cal: 'Cal {0}',
    cal_includes_only_information_about_time_slot: 'Includes only information about time slot',
    cal_include_personal_details_about_booking: 'Includes personal details about booking',
    calendar_name: 'Calendar name',
    calendar_name_error: 'Calendar name is required.',
    connected_with: 'Connected with {0}',
    enter_calendar_name: 'Enter calendar name',
    enter_calendar_name_and_click: `Enter calendar name and click 'Connect'`,
    price_must_be_greater_than_zero: 'price must be greater than 0',
    verify_to_use_sms: 'Verify to use SMS',
    sms_verification_title: 'Verify you mobile number to buy SMS credits.',
    sms_verification_desc_1: 'We will send 6 digit verification code on below number',
    sms_verification_desc_2: 'We have sent verification code to {0}',
    send_otp: 'Send OTP',
    verify: 'Verify',
    attach_video_call_link: 'Attach video call link',
    video_call: 'Video call',
    join_video_call: 'Join Video call',
    cancel_video_call: 'Cancel Video call',
    end_video_call: 'End Video call',
    include_video_meeting_link: 'Include video meeting link',
    call_has_been_ended: 'Call has been ended',
    marketing: 'Marketing',
    name_of_message: 'Name of Message',
    credits: 'Credits',
    credit_used: 'Credit Used',
    content_in_sms: 'Content in SMS',
    more_details: 'More Details',
    add_message_name_and_choose_clients_to_send_the_message: 'Add message name and choose clients to send the message.',
    name_for_this_message: 'Name for this message',
    choose_clients: 'Choose clients',
    target_type: 'Target Type',
    condition: 'Condition',
    available_credits: 'Available Credits',
    send_sms_credits: 'Send SMS - {0} Credits',
    offer_message: 'Offer Message',
    new_clients: 'New Clients',
    inactive_clients: 'Inactive Clients',
    custom_segments: 'Custom Segments',
    target_every_client_in_the_database: 'Target every client in the database',
    above: 'Above',
    below: 'Below',
    equal: 'Equal',
    age: 'Age',
    target_client_who_joined: 'Target client who joined',
    in_last: 'In last',
    birthday: 'Birthday',
    target_clients_who_last_booked: 'Target clients who last booked service in the last',
    in_the_last: 'In the last',
    target_clients_who_last_signed_the_LoC_in_the_last: 'Target clients who last signed the LoC in the last',
    target_clients_who_last_submitted_questionnaires_in_the_last: 'Target clients who last submitted questionnaires in the last',
    months: 'months',
    years: 'years',
    quality_management_system: 'Quality Mgmt. System',
    document_reports: 'Document & Reports',
    medical_device: 'Medical Device',
    device_name: 'Device name',
    added_on: 'Added On',
    upcoming_maintenance: 'Upcoming maintenance',
    model: 'Model',
    serial_number: 'Serial number',
    brand: 'Brand',
    supplier: 'Supplier',
    performed_maintenance: 'Performed maintenance',
    compliance_declaration_check_performed: 'Compliance declaration check performed',
    upload_manuals: 'Upload manuals',
    upload_maintenance_protocol: 'Upload maintenance protocol',
    versions: 'Versions',
    version: 'Version',
    device_name_required: 'Device name is required.',
    model_name_required: 'Model name is required.',
    brand_name_required: 'Brand name is required.',
    serial_number_required: 'Serial number is required.',
    supplier_name_required: 'Supplier name is required.',
    html_editor: 'Rich Text',
    maintenance_doc: 'Maintenance Doc',
    supplier_agreements: 'Supplier Agreements',
    upload_protocal: 'Upload protocal',
    maintanance: 'Maintanance',
    process: 'Process',
    patient_safety: 'Patient safety',
    deviation_mgmt: 'Deviation mgmt.',
    the_business: 'The business',
    subscription: {
        title: 'We offer {0}.',
    },
    unlimited_users_and_clients: 'Unlimited users and clients',
    quality_management: 'Quality Management',
    including_hours_with_a_Quality_Management_consultant: 'Including hours with a Quality Management consultant',
    version_control_for_all_documents: 'Version control for all documents',
    support_for_medical_devices: 'Support for medical devices',
    free_templates_for_management_systems: 'Free templates for management systems',
    coming_soon: 'Coming soon',
    one_time_activation_fee: 'One time activation fee',
    point_of_Sale_is_not_enabled_Please_contact_your_admin: 'Point of Sale is not enabled. Please contact your admin.',
    downgrade_plan_message: 'Would you like to downgrade your plan from yearly to monthly? If so,',
    please_contact_support_for_assistance: 'please contact support for assistance.',
    upgrade_plan_message: 'Would you like to upgrade your plan from monthly to yearly? If so,',
    information_to_you: 'Information to you',
    the_booking_system_will_be_paid_starting_from: 'The booking system will be paid starting from "1st January 2025".',
    give_your_input: 'Give your input',
    opt_in: 'Opt in',
    the_booking_system_will_be_included_in_the_current_paid_plan: 'The booking system will be included in the current paid plan ({0}/{1}/user).',
    opt_out: 'Opt out',
    the_booking_system_will_end_on_at_midnight: 'The booking system will end on 31st December 2024 on at midnight.',
    if_you_are_not_using_the_booking_system: 'If you are not using the booking system you can inactivate it under "General Settings" -> "Team"',
    do_not_show_this_again: 'Do not show this again',
    your_price_will_be_deducted_based_on_the_number_of_active_booking_users:
        'Your price will be deducted based on the number of active booking users.',
    active_user: 'Active User',
    important_note: 'Important Note',
    show_less: 'Show Less',
    summary: 'Summary',
    show_details: 'Show Details',
    sub_total: 'Sub Total',
    support_for_gift_cards: 'Support for gift cards',
    product_library_with_inventory: 'Product library with inventory',
    qty: 'Qty',
    total_price: 'Total Price',
    order_id: 'Order Id',
    google_ratings: 'Google Ratings',
    google_ratings_note:
        'Display your verified Google reviews and ratings directly on your booking and registration portal to build trust with customers. Reviews are synced automatically every month.',
    fetch_ratings: 'Fetch Ratings',
    reviews: 'Reviews',
    View_on_google_maps: 'View on google maps',
    or: 'Or',
    video_call_success_modal_messages: [
        'Video call link has been sent to client.',
        'You can start the video call by clicking the button below',
        'You can also find the video call link in {0} section',
    ],
    video_call_joining_link_attached: 'Video call joining link will be attached at the end of the message.',
    not_enough_credits_for_video_call: "You don't have enough video/sms credits.\nBuy credits to get started with video calls",
    at: 'at',
    day: 'day',
    month: 'month',
    year: 'year',
    tomorrow: 'Tomorrow',
    in_next: 'in next',
    invalid_format_use: 'Invalid format. Use "(number) {0}.',
    the_message_will_be_sent_to_all_client_from_database: 'The message will be sent to all client from database',
    before: 'before',
    ago: 'ago',
    upcoming_birthday: 'Upcoming Birthday',
    insufficient_credits_to_send_SMS: 'Insufficient credits to send SMS',
    type_sms: 'Type SMS',
    client_filter: 'Client Filter',
    number_is_required: 'Number is required',
    number_of_client: 'Number of Client',
    create_campaigns: 'Create Campaigns',
    select_the_type_of_message_you_want_to_create_campaign: 'Select the type of message you want to send sms.',
    custom_message_that_you_configure_yourself: 'Custom message that you configure yourself.',
    follow_up_message: 'Follow up message',
    follow_up_message_sent_some_time_after_the_appointment_is_held: 'Follow up messages sent some time after the appointment is held.',
    send_message_new_clients_join_in_last_thirty_days: 'Send a message to new clients who have joined in the last 30 days.',
    birthday_offer_message: 'Birthday Offer',
    birthday_offer_message_sent_before_hours_of_client_birthday: 'Send a birthday offer message before 24 hours of client birthday.',
    select_template: 'Select Template',
    no_client_found: 'No client found',
    how_marketing_sms_works: 'How Marketing SMS Works',
    client_segmentation: 'Client Segmentation',
    client_segmentation_note: 'Send targeted messages by filtering clients based on their behaviour or preferences.',
    personalisation: 'Personalisation',
    personalisation_note: 'Use dynamic fields like client names or service details to make messages more engaging.',
    template_library: 'Template Library',
    template_library_note: 'Save and reuse frequently sent SMS messages.',
    credit_management: 'Credit Management',
    credit_management_note: 'Easily track and top-up SMS credits as needed.',
    send_marketing_sms: 'Send Marketing SMS',
    booking_service: 'Booking Service',
    booking_service_note: 'Send a message to clients who have last booked any service before 3 months.',
    any: 'Any',
    the_selected_time_frame_must_be_within_the_next_a_year: 'The selected time frame must be within the next 1 year.',
    click_buy_credits: 'Click Buy Credits',
    clients_list: 'Clients List',
    delivered: 'Delivered',
    failed: 'Failed',
    reports_strings: {
        no_of_trxn: 'No. of Trxn.',
        avg_trxn_value: 'Avg. Trxn Value',
        new_client_registrations: 'New client registrations',
        procedure_performed: 'Procedure performed',
        top_questionnaires: 'Top questionnaires',
        most_procedures: 'Most procedures',
        most_notes: 'Most notes',
        prescription_signed: 'Prescription signed',
        client_age: 'Client age',
        top_practitioners: 'Top practitioners',
        top_booking_services: 'Top booking services',
        top_service_category: 'Top service category',
        direct: 'Direct',
        payment_gateway_preferences: 'Payment Gateway Preferences',
        trxns: 'Trxns',
        client_lifetime_value: 'Client lifetime value',
        practitioner_performance: 'Practitioner performance',
        total_discount: 'Total discount',
        qty_sold: 'Qty sold',
        top_selling_products: 'Top selling products',
        top_booked_services: 'Top booked services',
        qty_left: 'Qty left',
        action: 'Action',
        low_stock_products: 'Low stock products',
        by_mal: 'by MAL',
        by_own: 'by Own',
        manual: 'Manual',
        total_revenue: 'Total Revenue',
        total_single_booking: 'Total single booking',
        total_group_booking: 'Total group booking',
        booking_revenue: 'Booking revenue',
        total_clients: 'Total clients',
        total_procedure: 'Total procedure',
        sms_send: 'SMS send',
        sms_sent: 'SMS sent',
        direct_discount: 'Direct discount',
        reports_record: 'Reports - record',
        reports_booking: 'Reports - booking',
        reports_pos: 'Reports - pos',
    },
    cancel_for_booking: 'Cancel',
    client_does_not_have_an_email: `Client doesn't have an email`,
    add_holidays: 'Add holidays',
    holiday_text_note_one: 'Add holidays, marked dates will be reflected on the booking portal.',
    holiday_text_note_two: 'Clinic booking portal will not consider this days as close days.',
    company_holidays: 'Company Holidays',
    select_holidays: 'Select Holidays',
    global_religious_holiday: 'Global religious holiday',
    holidays: 'Holidays',
    country_holidays: 'Country Holidays',
    cancelled_for_booking: 'Cancelled',
    total_excl_tax: 'Total excl. tax',
    expired_today: 'Expired today',
    documents: 'Documents',
    documentation: 'Documentation',
    prescription_alert: 'Prescription Alert',
    renew: 'Renew',
    do_not_show_again: `Don't show again`,
    continue_to_free_trial: 'Continue to free trial',
    client_signature: 'Client signature',
    practitioner_signature: 'Practitioner signature',
    something_went_wrong_please_contact_support: 'Something went wrong!, <NAME_EMAIL>',
    card_3d_failed_payment: 'Extra confirmation is needed to process your payment.\nPlease confirm your payment',
    total_amount_due: 'Total amount due',
    any_issues_contact_support: "If you have any issues regarding this payment, <NAME_EMAIL>",
    magnetic_booking: 'Magnetic Booking',
    magnetic_booking_desc: 'if enable, system hides some slots that produces 10-20 min gaps',
};

export default data;
