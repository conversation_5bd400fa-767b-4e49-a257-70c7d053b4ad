import { generateUserFullName } from '@/helper';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import useMatchMultipleRoutes from '@hooks/useMatchMultipleRoutes';
import { usePaginationSWR, usePaginationSWRResponse } from '@hooks/usePaginationSWR';
import { ClientLetterOfConsentPaginatedResponse } from '@interface/common';
import { ClientLetterOfConsent } from '@interface/model/clientLetterOfConsent';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import { FC, PropsWithChildren, createContext, lazy, useContext, useState } from 'react';

const ClientLetterOfConsentDeleteModal = lazy(() => import('@components/Clients/Client/LetterOfConsents/ClientLetterOfConsentDeleteModal'));
const ClientLetterOfConsentVerifySignModal = lazy(() => import('@components/Clients/Client/LetterOfConsents/ClientLetterOfConsentVerifySignModal'));
const ClientLetterOfConsentViewModal = lazy(() => import('@components/Clients/Client/LetterOfConsents/ClientLetterOfConsentViewModal'));
const ClientLetterOfConsentModal = lazy(() => import('@components/Clients/Client/LetterOfConsents/ClientLetterOfConsentModal'));
const ClientRecordsCancelModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelModal'));
const ClientRecordsCancelNoteViewModal = lazy(() => import('@components/Clients/Client/ClientRecordsCancelNoteViewModal'));

interface IContext extends usePaginationSWRResponse<ClientLetterOfConsentPaginatedResponse, Error> {
    openLoC: boolean;
    setOpenLoC: React.Dispatch<React.SetStateAction<boolean>>;
    openLoCDeleteModal: boolean;
    setOpenLoCDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
    openLoCSignModal: boolean;
    setOpenLoCSignModal: React.Dispatch<React.SetStateAction<boolean>>;
    openLoCViewModal: boolean;
    setOpenLoCViewModal: React.Dispatch<React.SetStateAction<boolean>>;
    selectedLetterOfConsent?: ClientLetterOfConsent;
    setSelectedLetterOfConsent: React.Dispatch<React.SetStateAction<ClientLetterOfConsent | undefined>>;
    openLoCCancelModal: boolean;
    setOpenLoCCancelModal: React.Dispatch<React.SetStateAction<boolean>>;
    openLoCCancelNoteViewModal: boolean;
    setOpenLoCCancelNoteViewModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const MainCardLoCContext = createContext<IContext>({} as IContext);

const MainCardLoCProvider: FC<PropsWithChildren> = ({ children }) => {
    const { clientId, mutateCounts } = useClient();

    const isClientPage = useMatchMultipleRoutes([
        'clients/:clientId',
        'settings/team/:userId/booking/:bookingId/client/:clientId',
        'calendar/booking/:bookingId/client/:clientId',
    ]);

    const [openLoC, setOpenLoC] = useState(false);
    const [openLoCDeleteModal, setOpenLoCDeleteModal] = useState(false);
    const [openLoCSignModal, setOpenLoCSignModal] = useState(false);
    const [openLoCViewModal, setOpenLoCViewModal] = useState(false);
    const [selectedLetterOfConsent, setSelectedLetterOfConsent] = useState<ClientLetterOfConsent>();
    const [openLoCCancelModal, setOpenLoCCancelModal] = useState(false);
    const [openLoCCancelNoteViewModal, setOpenLoCCancelNoteViewModal] = useState(false);

    const swr = usePaginationSWR<ClientLetterOfConsentPaginatedResponse, Error>(api.clientLetterOfConsents(clientId), {
        limit: isClientPage ? 3 : 20,
    });

    const loCMutate = async () => {
        mutateCounts();
        swr.mutate();
    };

    return (
        <MainCardLoCContext.Provider
            value={{
                openLoC,
                setOpenLoC,
                openLoCDeleteModal,
                setOpenLoCDeleteModal,
                openLoCSignModal,
                setOpenLoCSignModal,
                openLoCViewModal,
                setOpenLoCViewModal,
                selectedLetterOfConsent,
                setSelectedLetterOfConsent,
                openLoCCancelModal,
                setOpenLoCCancelModal,
                openLoCCancelNoteViewModal,
                setOpenLoCCancelNoteViewModal,
                ...swr,
            }}
        >
            <ModalSuspense>
                {openLoC && (
                    <ClientLetterOfConsentModal
                        key={`loc_modal`}
                        openModal={openLoC}
                        setOpenModal={setOpenLoC}
                        mutate={loCMutate}
                        setOpenSignModal={setOpenLoCSignModal}
                        setSelectedLetterOfConsent={setSelectedLetterOfConsent}
                    />
                )}
                {openLoCDeleteModal && (
                    <ClientLetterOfConsentDeleteModal
                        open={openLoCDeleteModal}
                        handleClose={() => {
                            setOpenLoCDeleteModal(false);
                            setSelectedLetterOfConsent(undefined);
                        }}
                        mutate={loCMutate}
                        selectedClientLetterOfConsent={selectedLetterOfConsent}
                    />
                )}
                {openLoCViewModal && (
                    <ClientLetterOfConsentViewModal
                        openModal={openLoCViewModal}
                        handleClose={() => {
                            setOpenLoCViewModal(false);
                            setSelectedLetterOfConsent(undefined);
                        }}
                        selectedClientLetterOfConsent={selectedLetterOfConsent}
                    />
                )}
                {openLoCSignModal && (
                    <ClientLetterOfConsentVerifySignModal
                        openModal={openLoCSignModal}
                        setOpenModal={setOpenLoCSignModal}
                        mutate={loCMutate}
                        clientMutate={mutateCounts}
                        selectedLetterOfConsent={selectedLetterOfConsent}
                    />
                )}
                {openLoCCancelModal && (
                    <ClientRecordsCancelModal
                        openModal={openLoCCancelModal}
                        setOpenModal={setOpenLoCCancelModal}
                        mutate={loCMutate}
                        url={api.clientLetterOfConsentUpdate.replace(':id', selectedLetterOfConsent?.id.toString() || '')}
                    />
                )}
                {openLoCCancelNoteViewModal && (
                    <ClientRecordsCancelNoteViewModal
                        openModal={openLoCCancelNoteViewModal}
                        handleModelClose={() => setOpenLoCCancelNoteViewModal(false)}
                        cancelledNoteDetails={selectedLetterOfConsent?.cancel_note}
                        cancelNoteDate={selectedLetterOfConsent?.cancelled_at}
                        cancelByuUserName={generateUserFullName(selectedLetterOfConsent?.cancelled_by)}
                    />
                )}
            </ModalSuspense>
            {children}
        </MainCardLoCContext.Provider>
    );
};

export const useMainCardLoC = () => {
    const context = useContext(MainCardLoCContext);
    if (!context) {
        throw new Error('useMainCardLoC must be used within a MainCardLoCProvider');
    }
    return context;
};

export default MainCardLoCProvider;
