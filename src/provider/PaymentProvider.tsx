import { commonFetch, customRound, generateTaxPercentageLevel } from '@/helper';
import { IPaymentValues } from '@components/PointsOfSale/Payment/Payment';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import useDebounce from '@hooks/useDebounce';
import { useLocalStorageNew } from '@hooks/useLocalStorageNew';
import useQuery from '@hooks/useQuery';
import { BookingDetailResponse, GiftCardResponse, ProductResponse, ServiceResponse, TerminalResponse } from '@interface/common';
import { Terminal } from '@interface/model/Terminal';
import { GiftCard } from '@interface/model/giftCard';
import { Product } from '@interface/model/product';
import { Service } from '@interface/model/service';
import pos_strings from '@lang/pos/Lang';
import cx from 'classix';
import { useFormikContext } from 'formik';
import React, { createContext, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
const ConfirmCompanyDetailModal = React.lazy(() => import('./../components/PointsOfSale/Sidebar/modal/ConfirmCompanyDetailModal'));
const FortnoxConnectionModal = React.lazy(() => import('./../components/PointsOfSale/Sidebar/modal/FortnoxConnectionModal'));

interface PaymentProps {
    children?: React.ReactNode;
    updatedValues?: () => void;
}

export interface TaxPercentageLevel {
    net?: number;
    gross?: number;
    tax?: number;
    percentage?: string;
}

export const PaymentContext = createContext<{
    viva: {
        apply: () => void;
        status: "pending" | "in-process" | "connected",
    },
    fortnox: {
        apply: () => void;
        status: "pending" | "connected",
    },
    product: {
        selected: (id: number) => boolean;
        add: (id: number) => void;
        remove: (id: number) => void;
        increaseQuantity: (id: number) => Promise<void>;
        decreaseQuantity: (id: number) => Promise<void>;
        set: (products: Product[]) => void;
        fetch: () => Promise<void>;
        products: Product[];
        loading: boolean;
        search?: string;
        setSearch: (value?: string) => void;
    };
    service: {
        services: Service[];
        selected: (id: number) => boolean;
        add: (id: number) => void;
        remove: (id: number) => void;
        increaseQuantity: (id: number) => Promise<void>;
        decreaseQuantity: (id: number) => Promise<void>;
        set: (services: Service[]) => void;
        fetch: () => Promise<void>;
        loading: boolean;
        search?: string;
        setSearch: (value?: string) => void;
    };

    discount: {
        on: boolean;
        switch: () => Promise<void>;
        canAdd: boolean;
        total: string;
        limit: number;
        limitValue: number;
    };

    gift_card: {
        on: boolean;
        switch: () => void;
        checkByCode: () => Promise<GiftCardResponse | undefined>;
        canAdd: boolean;
        total: string;
        limit: number;
        data?: GiftCard;
        error?: Error;
        loading: boolean;
    };

    summary: {
        giftCard?: string;
        subTotal: string;
        totalTax: string;
        total: string;
        payable: string;
        vivaPayable: string;
        taxPercentageLevel: TaxPercentageLevel[];
    };

    terminal: {
        data: Terminal[];
        loading: boolean;
        selected?: Terminal;
        reload: () => void;
        required: boolean;
    };
}>({
    viva: {
        apply: () => {},
        status: 'pending',
    },
    fortnox: {
        apply: () => {},
        status: 'pending',
    },
    product: {
        selected: () => false,
        add: () => {},
        remove: () => {},
        increaseQuantity: async () => {},
        decreaseQuantity: async () => {},
        set: () => {},
        fetch: async () => {},
        products: [],
        loading: true,
        setSearch: () => {},
    },
    service: {
        selected: () => false,
        add: () => {},
        remove: () => {},
        increaseQuantity: async () => {},
        decreaseQuantity: async () => {},
        set: () => {},
        fetch: async () => {},
        services: [],
        loading: true,
        setSearch: () => {},
    },

    discount: {
        on: false,
        switch: async () => {},
        canAdd: false,
        total: '0',
        limit: 0,
        limitValue: 0,
    },

    gift_card: {
        on: false,
        switch: () => {},
        checkByCode: async () => undefined,
        canAdd: false,
        total: '0',
        limit: 0,
        loading: false,
    },

    summary: {
        subTotal: '0',
        totalTax: '0',
        total: '0',
        payable: '0',
        vivaPayable: '0',
        taxPercentageLevel: [],
    },

    terminal: {
        data: [],
        loading: false,
        reload: () => {},
        required: true,
    },
});

const giftCardParams = {
    rollbackOnError: false,
};

export const PaymentProvider: React.FC<PaymentProps> = ({ children, updatedValues }) => {
    const { user } = useAuth();

    const [openPOSEditModal, setOpenPOSEditModal] = React.useState(false);
    const [openFortnoxModal, setOpenFortnoxModal] = React.useState(false);
    const hasAppliedForViva = React.useMemo(() => !!user?.company?.viva_account_id, [user?.company?.viva_account_id]);
    const hasConnectedToViva = React.useMemo(() => !!user?.company?.viva_merchant_id, [user?.company?.viva_merchant_id]);
    const hasConnectedToFortnox = React.useMemo(() => !!user?.company?.connected_to_fortnox, [user?.company?.connected_to_fortnox]);

    const { isSubmitting, isValidating, values, setFieldValue, errors, validateField } = useFormikContext<IPaymentValues>();

    const [searchParams, setSearchParams] = useSearchParams();
    const bookingId = searchParams.get('booking');
    const bookingClientId = searchParams.get('booking_client');

    const { data: bookingData } = useSWR<BookingDetailResponse, Error>(bookingId ? api.bookingSingleDetail(bookingId) : undefined);

    const booking = bookingData?.data;

    const hasSetClientOnce = useRef(false);
    const hasSetServiceOnce = useRef(false);

    const [storedValue] = useLocalStorageNew<string>(`${user?.id}_${user?.company_id}_terminal_id`, '');

    const [productSearch, setProductSearch] = useState<string>();
    const [serviceSearch, setServiceSearch] = useState<string>();

    const productSearchDebounce = useDebounce(productSearch, 500);
    const serviceSearchDebounce = useDebounce(serviceSearch, 500);

    const {
        data: productData,
        isLoading: productLoading,
        mutate: fetchProduct,
    } = useSWR<ProductResponse, Error>(api.pos.product.list({ search: productSearchDebounce }));
    const {
        data: serviceData,
        isLoading: serviceLoading,
        mutate: fetchService,
    } = useSWR<ServiceResponse, Error>(api.services({ price_only: true, search: serviceSearchDebounce }));

    const { data: terminalData, isLoading: terminalLoading, mutate: reloadTerminals } = useSWR<TerminalResponse, Error>(api.pos.terminal.list);

    const {
        data: giftCardData,
        trigger: fetchGiftCard,
        error: giftCardError,
        isMutating: giftCardLoading,
    } = useSWRMutation<GiftCardResponse, Error>(
        values.gift_card ? api.pos.giftCard.code(values.gift_card_id) : '',
        (url: string) => commonFetch(url),
        giftCardParams,
    );

    useEffect(() => {
        if (updatedValues) {
            updatedValues();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values]);

    useEffect(() => {
        if (errors.server) {
            fetchProduct();
            fetchService();
            // fetchGiftCard();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [errors.server]);

    const terminals = React.useMemo(() => (terminalData?.data ?? []).filter((t) => !t.deleted_at), [terminalData?.data]);
    const gift_card = React.useMemo(
        () => (!giftCardError && !giftCardLoading && giftCardData?.data ? giftCardData.data : undefined),
        [giftCardData, giftCardError, giftCardLoading],
    );

    useEffect(() => {
        if (values.terminal_id && !terminalLoading && !terminals.find((t) => t.terminal_id === values.terminal_id)) {
            setFieldValue('terminal_id', '');
        } else if (!values.terminal_id && storedValue && terminals.find((t) => t.terminal_id === storedValue)) {
            setFieldValue('terminal_id', storedValue);
        }
    }, [terminals, storedValue, values.terminal_id, setFieldValue, terminalLoading]);

    const canAddGiftCard = useMemo(() => !!(values.products.length || values.services.length), [values.products.length, values.services.length]);
    const canAddDiscount = useMemo(() => !!(values.products.length || values.services.length), [values.products.length, values.services.length]);

    useEffect(() => {
        const data = async () => {
            if (!!giftCardError) {
                await setFieldValue('gift_card_error', giftCardError);
                await setFieldValue('gift_card_data', undefined);
                await validateField('gift_card_id');
            } else if (!giftCardError && giftCardData?.data) {
                await setFieldValue('gift_card_error', undefined);
                await setFieldValue('gift_card_data', giftCardData.data);
                await validateField('gift_card_id');
            }

            if (!values.gift_card_id || (values.gift_card_id && values.gift_card_id.length < 5)) {
                await setFieldValue('gift_card_data', undefined);
                await setFieldValue('gift_card_error', undefined);
            }
        };

        data();
    }, [giftCardData?.data, setFieldValue, giftCardError, validateField, values.gift_card_id]);

    useEffect(() => {
        if (!(values.products.length || values.services.length)) {
            setFieldValue('gift_card', false);
            setFieldValue('discount', false);
        }
    }, [setFieldValue, values.products.length, values.services.length]);

    const products = useMemo(() => productData?.data ?? [], [productData?.data]);
    const services = useMemo(() => serviceData?.data ?? [], [serviceData?.data]);

    const switchDiscount = useCallback(async () => {
        if (!values.discount && !canAddDiscount) {
            toast.error(pos_strings.payment.validation.add_service_or_products_before_activating_discount);
            return;
        }

        await setFieldValue('discount', !values.discount);
    }, [canAddDiscount, setFieldValue, values.discount]);

    const switchGiftCard = useCallback(() => {
        if (!values.gift_card && !canAddGiftCard) {
            toast.error(pos_strings.payment.validation.add_service_or_products_before_activating_gift_card);
            return;
        }

        setFieldValue('gift_card', !values.gift_card);
    }, [canAddGiftCard, setFieldValue, values.gift_card]);

    const subTotal = useMemo(() => {
        const prodSum = values.products.reduce((p, n) => p + parseFloat(n.selling_price) * (n.selected_quantity ?? 1), 0);
        const servSum = values.services.reduce((p, n) => p + parseFloat(n?.price ?? '0') * (n.selected_quantity ?? 0), 0);

        return customRound(servSum + prodSum).toString();
    }, [values.products, values.services]);

    const totalDiscount = useMemo(() => {
        if (!values.discount || !values.discount_value || errors.discount || errors.discount_type || errors.discount_value) return '0';

        const value = parseFloat(values.discount_value);

        if (values.discount_type === 'percentage') {
            return customRound((value / 100) * parseFloat(subTotal)).toString();
        }

        return customRound(value).toString();
    }, [values.discount, values.discount_value, values.discount_type, errors.discount, errors.discount_type, errors.discount_value, subTotal]);

    const discountPercentage = useMemo(() => {
        const value = parseFloat(values?.discount_value ? values?.discount_value : '0');

        if (values.discount_type === 'percentage') {
            return value ?? 0;
        }

        return (value / parseFloat(subTotal ?? '0')) * 100;
    }, [values.discount_value, values.discount_type, subTotal]);

    const totalTax = useMemo(() => {
        const prodSum = values.products.reduce((p, n) => {
            const tax = (parseInt(n.tax_information) / 100) * (parseFloat(n.selling_price) * (n.selected_quantity ?? 1));

            return p + tax;
        }, 0);
        const servSum = values.services.reduce((p, n) => {
            const tax = (parseInt(n.tax_information ?? '0') / 100) * (parseFloat(n.price ?? '0') * (n.selected_quantity ?? 1));

            return p + tax;
        }, 0);

        return customRound(servSum + prodSum).toString();
    }, [values.products, values.services]);

    const total = useMemo(() => {
        return customRound(parseFloat(subTotal)).toString();
    }, [subTotal]);

    const totalGiftAmount = useMemo(() => {
        if (!values.gift_card) return '0';
        if (errors.gift_card_value || errors.gift_card_id) return '0';
        if (!values.gift_card_value) return '0';

        return customRound(parseFloat(values.gift_card_value.toString() ?? '0')).toString();
    }, [errors.gift_card_id, errors.gift_card_value, values.gift_card, values.gift_card_value]);

    const payable = useMemo(() => {
        return customRound(parseFloat(subTotal) - parseFloat(totalDiscount)).toString();
    }, [subTotal, totalDiscount]);

    const vivaPayable = useMemo(() => {
        return customRound(parseFloat(payable) - parseFloat(totalGiftAmount)).toString();
    }, [payable, totalGiftAmount]);

    // const viewVivaPayable = useMemo(() => {
    //     return ;
    // }, [payable, totalGiftAmount]);

    const discountLimit = useMemo(() => {
        if (values.discount_type === 'value') {
            return parseFloat(total);
        }

        return 100;
    }, [total, values.discount_type]);

    const discountLimitValue = useMemo(() => {
        return parseFloat(total);
    }, [total]);

    const giftCardLimit = useMemo(() => {
        if (!values.gift_card) return 0;

        let current = parseFloat(gift_card?.current_value ?? '0');

        const remaining = parseFloat(total) - parseFloat(totalDiscount);

        current = current < remaining ? current : remaining;

        return current;
    }, [gift_card?.current_value, total, totalDiscount, values.gift_card]);

    const taxPercentageLevel = useMemo(() => {
        return generateTaxPercentageLevel(discountPercentage, [
            ...values.services.map((p) => ({
                tax_percentage: parseFloat(p.tax_information ?? '0'),
                quantity: parseInt(p.selected_quantity?.toString() ?? '1'),
                price: parseFloat(p.price?.toString() ?? '0'),
            })),
            ...values.products.map((p) => ({
                tax_percentage: parseFloat(p.tax_information ?? '0'),
                quantity: parseInt(p.selected_quantity?.toString() ?? '1'),
                price: parseFloat(p.selling_price ?? '0'),
            })),
        ]);
    }, [discountPercentage, values.products, values.services]);

    useEffect(() => {
        setFieldValue('subTotal', subTotal);

        setFieldValue('totalTax', totalTax);

        setFieldValue('total', total);

        setFieldValue('payable', payable);
    }, [total, subTotal, totalTax, setFieldValue, payable]);

    // Product Functions
    const increaseProductQuantity = useCallback(
        async (productId: number) => {
            if (isSubmitting || isValidating) return;

            const prodValue = [
                ...(values.products?.map((p, i) => {
                    if (p.id === productId && parseInt(p.stock) > (p.selected_quantity ?? 0)) {
                        return {
                            ...p,
                            selected_quantity: (p.selected_quantity ?? 0) + 1,
                        };
                    }

                    return p;
                }) ?? []),
            ];

            await setFieldValue('products', prodValue);
        },
        [isSubmitting, isValidating, setFieldValue, values.products],
    );

    const decreaseProductQuantity = useCallback(
        async (productId: number) => {
            if (isSubmitting || isValidating) return;

            const prodValue = [
                ...(values.products?.map((p, i) => {
                    if (p.id === productId) {
                        return {
                            ...p,
                            selected_quantity: (p.selected_quantity ?? 0) - 1,
                        };
                    }

                    return p;
                }) ?? []),
            ];

            await setFieldValue(
                'products',
                prodValue.filter((p) => p.selected_quantity),
            );
        },
        [isSubmitting, isValidating, setFieldValue, values.products],
    );

    const selectedProduct = React.useCallback(
        (id: number) => {
            return !!values.products.find((p) => p.id === id);
        },
        [values.products],
    );

    const addProduct = React.useCallback(
        (id: number) => {
            const hasProduct = values.products.find((p) => p.id);

            if (hasProduct) return;

            const product = products.find((p) => p.id === id);

            if (product && parseInt(product.stock) <= 0) return;

            setFieldValue('products', [
                ...values.products,
                {
                    ...product,
                    selected_quantity: 1,
                },
            ]);
        },
        [products, setFieldValue, values.products],
    );

    const removeProduct = React.useCallback(
        (id: number) => {
            setFieldValue('products', [...values.products.filter((p) => p.id !== id)]);
        },
        [setFieldValue, values.products],
    );

    const setProducts = React.useCallback(
        (products: Product[]) => {
            setFieldValue('products', [
                ...products.map((p) => {
                    return {
                        ...p,
                        selected_quantity: values.products.find((pr) => pr.id === p.id)?.selected_quantity ?? 1,
                    };
                }),
            ]);
        },
        [setFieldValue, values.products],
    );

    // Service Functions
    const increaseServiceQuantity = async (serviceId: number) => {
        if (isSubmitting || isValidating) return;

        const services = [
            ...(values.services?.map((p, i) => {
                if (p.id === serviceId) {
                    return {
                        ...p,
                        selected_quantity: (p.selected_quantity ?? 0) + 1,
                    };
                }

                return p;
            }) ?? []),
        ];

        await setFieldValue('services', services);
    };

    const decreaseServiceQuantity = async (serviceId: number) => {
        if (isSubmitting || isValidating) return;

        const services = [
            ...(values.services?.map((p, i) => {
                if (p.id === serviceId) {
                    return {
                        ...p,
                        selected_quantity: (p.selected_quantity ?? 0) - 1,
                    };
                }

                return p;
            }) ?? []),
        ];

        await setFieldValue(
            'services',
            services.filter((p) => p.selected_quantity),
        );
    };

    const selectedService = React.useCallback(
        (id: number) => {
            return !!values.services.find((s) => s.id === id);
        },
        [values.services],
    );

    const addService = React.useCallback(
        (id: number) => {
            const hasService = values.services.find((s) => s.id);

            if (hasService) return;

            const service = services.find((s) => s.id === id);

            if (!service) return;

            setFieldValue('services', [
                ...values.services,
                {
                    ...service,
                    selected_quantity: 1,
                },
            ]);
        },
        [services, setFieldValue, values.services],
    );

    const removeService = React.useCallback(
        (id: number) => {
            setFieldValue('services', [...values.services.filter((p) => p.id !== id)]);
        },
        [setFieldValue, values.services],
    );

    const setServices = React.useCallback(
        (services: Service[]) => {
            setFieldValue('services', [
                ...services.map((p) => {
                    return {
                        ...p,
                        selected_quantity: values.services.find((pr) => pr.id === p.id)?.selected_quantity ?? 1,
                        user_id: booking?.service?.id === p.id ? booking?.user_id : null,
                    };
                }),
            ]);
        },
        [setFieldValue, values.services, booking],
    );

    const applyForViva = React.useCallback(() => {
        if(!hasAppliedForViva && !hasConnectedToViva) {
            setOpenPOSEditModal(true);
        }
    }, [hasAppliedForViva, hasConnectedToViva]);

    const { trigger: fortnoxConnectTrigger, error: fortnoxConnectError, isMutating: fortnoxConnecting } = useSWRMutation(
        api.pos.fortnox.auth.connect,
        (url: string) => commonFetch(url)
    );

    const applyForFortnox = React.useCallback(async () => {
        if(!hasConnectedToFortnox) {
            try {
                const response = await fortnoxConnectTrigger();
                if (response?.data) {
                    // Redirect to Fortnox OAuth URL
                    window.location.href = response.data;
                } else {
                    toast.error(response?.message || 'Failed to connect to Fortnox');
                }
            } catch (error) {
                toast.error('Failed to connect to Fortnox');
            }
        }
    }, [hasConnectedToFortnox, fortnoxConnectTrigger]);

    useEffect(() => {
        if (services.length && booking?.service && !hasSetServiceOnce.current) {
            setServices(services.filter((s) => booking?.service?.id === s.id));
            hasSetServiceOnce.current = true;
        }
    }, [booking, services]);

    useEffect(() => {
        if (booking?.client && !hasSetClientOnce.current) {
            setFieldValue('client_id', booking.client.id);
            setFieldValue('client', booking.client);
            setFieldValue('related_id', booking.id);
            setFieldValue('related_type', 'booking');

            hasSetClientOnce.current = true;
        }

        if (booking?.clients?.length && bookingClientId && !hasSetClientOnce.current) {
            const bookingClient = booking.clients.find((c) => c?.pivot?.id === parseInt(bookingClientId ?? '0'));

            setFieldValue('client_id', bookingClient?.id);
            setFieldValue('client', bookingClient);
            setFieldValue('related_id', bookingClient?.pivot?.id);
            setFieldValue('related_type', 'booking_client');

            hasSetClientOnce.current = true;
        }
    }, [booking, bookingClientId]);
    console.log(cx(
        !hasAppliedForViva && !hasConnectedToViva && 'pending',
        hasAppliedForViva && !hasConnectedToViva && 'in-process',
        hasAppliedForViva && hasConnectedToViva && 'connected',
    ) as 'pending' | 'in-process' | 'connected');

    return (
        <PaymentContext.Provider
            value={{
                viva: {
                    apply: applyForViva,
                    status: cx(
                        !hasAppliedForViva && !hasConnectedToViva && 'pending',
                        hasAppliedForViva && !hasConnectedToViva && 'in-process',
                        hasAppliedForViva && hasConnectedToViva && 'connected',
                    ) as 'pending' | 'in-process' | 'connected',
                },
                fortnox: {
                    apply: applyForFortnox,
                    status: hasConnectedToFortnox ? 'connected' : 'pending',
                },

                product: {
                    add: addProduct,
                    remove: removeProduct,
                    selected: selectedProduct,
                    increaseQuantity: increaseProductQuantity,
                    decreaseQuantity: decreaseProductQuantity,
                    set: setProducts,
                    fetch: async () => {
                        await fetchProduct();
                    },
                    products: products,
                    loading: productLoading,
                    search: productSearch,
                    setSearch: (value) => setProductSearch(value),
                },
                service: {
                    loading: serviceLoading,
                    services: services,
                    add: addService,
                    remove: removeService,
                    selected: selectedService,
                    increaseQuantity: increaseServiceQuantity,
                    decreaseQuantity: decreaseServiceQuantity,
                    set: setServices,
                    fetch: async () => {
                        await fetchService();
                    },
                    search: serviceSearch,
                    setSearch: (value) => setServiceSearch(value),
                },

                discount: {
                    on: values.discount,
                    switch: switchDiscount,
                    canAdd: canAddDiscount,
                    total: totalDiscount,
                    limit: discountLimit,
                    limitValue: discountLimitValue,
                },

                gift_card: {
                    on: values.gift_card,
                    switch: switchGiftCard,
                    checkByCode: fetchGiftCard,
                    canAdd: canAddGiftCard,
                    total: totalGiftAmount,
                    limit: giftCardLimit,
                    data: gift_card,
                    error: giftCardError,
                    loading: giftCardLoading,
                },

                summary: {
                    subTotal,
                    totalTax,
                    total,
                    payable,
                    vivaPayable,
                    giftCard: gift_card && !giftCardError && totalGiftAmount ? totalGiftAmount : undefined,
                    taxPercentageLevel,
                },

                terminal: {
                    data: terminals,
                    loading: terminalLoading,
                    selected: terminals.find((t) => t.terminal_id === values.terminal_id),
                    reload: reloadTerminals,
                    required: !!vivaPayable && !!(parseFloat(vivaPayable?.toString()) > 0),
                },
            }}
        >
            <>
                {openPOSEditModal && (
                    <ConfirmCompanyDetailModal
                        openModal={openPOSEditModal}
                        handleClose={(submit) => {
                            setOpenPOSEditModal(false);
                        }}
                    />
                )}
                {children}
            </>
        </PaymentContext.Provider>
    );
};
