import CustomTabs from '@components/form/CustomTabs';
import Heading from '@components/heading/Heading';
import strings from '@lang/Lang';
import Documents from '../../components/Documents/Documents';
import React from 'react';

const Documentation = () => {
    const [tab, setTab] = React.useState<number | null>(0);

    const tabs = [
        { text: strings.documents, Component: <Documents /> },
    ]

    function onTabChange(index: number) {
        if (tab === null || tab === index) setTab(tab !== null ? null : index)
    }
    return (
        <div className="grid max-w-full">
            <CustomTabs
                tabs={tabs}
                selectedIndex={tab}
                onChange={onTabChange}
                heading={<Heading text={strings.documentation} />}
                card
            />
        </div>
    );
};
export default Documentation;
