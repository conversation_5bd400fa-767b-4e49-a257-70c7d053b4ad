import * as React from "react"
import { SVGProps } from "react"
const MedicalDeviceIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width="1em"
        height="1em"
        viewBox="0 0 22 22"
        fill="currentColor"

        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M20.5268 12.3995L17.5466 9.30055C17.3654 9.11188 17.1404 8.96968 16.8959 8.88828C16.6695 8.78963 16.43 8.73957 16.1838 8.73957H8.38071C8.22554 8.73957 8.07916 8.66753 7.9845 8.54456L4.16849 3.58727L4.14624 3.5614C3.7981 3.1994 3.33525 3.00021 2.84328 3C2.3509 3 1.88786 3.1994 1.54012 3.5614C1.19177 3.9234 1 4.40466 1 4.91642C1 5.42271 1.18773 5.89913 1.52879 6.25986L6.01966 11.979L6.04029 12.0028C6.30267 12.2752 6.63301 12.4573 6.98783 12.5278C7.11831 12.5575 7.2504 12.5724 7.38047 12.5724H8.37662C8.65276 12.5724 8.87662 12.7963 8.87662 13.0724V16.1162C8.87662 16.3923 8.65276 16.6162 8.37662 16.6162H7.69072C7.41458 16.6162 7.19073 16.8401 7.19073 17.1162V18.5C7.19073 18.7761 7.41458 19 7.69073 19H14.5127C14.7889 19 15.0127 18.7761 15.0127 18.5V17.1162C15.0127 16.8401 14.7889 16.6162 14.5127 16.6162H13.6241C13.348 16.6162 13.1241 16.3923 13.1241 16.1162V13.0726C13.1241 12.7965 13.348 12.5726 13.6241 12.5726H15.9032C16.0392 12.5726 16.1693 12.628 16.2636 12.726L18.2385 14.7793C18.5538 15.1074 18.9681 15.2715 19.3826 15.2715C19.7971 15.2715 20.2116 15.1074 20.5268 14.7793C21.1577 14.1232 21.1577 13.0556 20.5268 12.3995ZM13.9055 17.2472C14.1816 17.2472 14.4054 17.4711 14.4054 17.7472V17.869C14.4054 18.1451 14.1816 18.369 13.9054 18.369H8.29719C8.02105 18.369 7.79719 18.1451 7.79719 17.869V17.7472C7.79719 17.4711 8.02105 17.2472 8.29719 17.2472H13.9055ZM9.48309 16.6162V13.0726C9.48309 12.7965 9.70695 12.5726 9.98309 12.5726H12.0175C12.2936 12.5726 12.5175 12.7965 12.5175 13.0726V16.1164C12.5175 16.3926 12.2936 16.6164 12.0175 16.6164H9.4833C9.48319 16.6164 9.48309 16.6163 9.48309 16.6162ZM20.0977 14.3332C19.7035 14.7431 19.0614 14.7431 18.6675 14.3332L16.5083 12.0878C16.4139 11.9897 16.2836 11.9343 16.1475 11.9344L7.38027 11.9414C7.29349 11.9414 7.20509 11.9313 7.1092 11.9096C6.8711 11.8623 6.65364 11.7441 6.47947 11.567L1.98941 5.84907L1.96877 5.82551C1.73513 5.58257 1.60647 5.2599 1.60647 4.91663C1.60647 4.57335 1.73513 4.25048 1.96877 4.00774C2.20222 3.76501 2.51253 3.63123 2.84267 3.63123C3.16755 3.63123 3.47342 3.7608 3.70565 3.99617L7.693 9.1758C7.78766 9.29876 7.93403 9.3708 8.0892 9.3708H16.1838C16.3525 9.3708 16.5166 9.40572 16.6709 9.47471L16.7013 9.48649C16.8601 9.53718 17 9.6249 17.1174 9.74689L20.0975 12.8458C20.492 13.2558 20.492 13.923 20.0977 14.3332Z"
            stroke="currentColor"
            strokeWidth={0.5}
        />
    </svg>
)
export default MedicalDeviceIcon;
