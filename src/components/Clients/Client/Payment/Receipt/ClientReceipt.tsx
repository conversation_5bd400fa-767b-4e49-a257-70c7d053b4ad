import ViewAllButton from '@components/form/ViewAllButton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { ReceiptPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EmptyData from '@partials/Error/EmptyData';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import Table from '@partials/Table/PageTable';
import ClientReceiptListItem from './ClientReceiptListItem';
import { useNavigate } from 'react-router';

const ClientReceipt = () => {
    const { clientId } = useClient();
    const { data, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<ReceiptPaginatedResponse, Error>(
        api.pos.receipt.list({ client_id: clientId }),
        {
            limit: 3,
        },
    );
    const navigate = useNavigate();

    if (loading) {
        return <SectionLoading />;
    }

    return (
        <Table>
            <Table.Head>
                <Table.ThSort
                    sort={orderBy === 'viva_receipt_id' && orderDirection}
                    onClick={() => {
                        handleOrder('viva_receipt_id');
                    }}
                    children={strings.id}
                />
                <Table.ThSort
                    sort={orderBy === 'created_at' && orderDirection}
                    onClick={() => {
                        handleOrder('created_at');
                    }}
                    children={strings.time}
                />
                <Table.ThSort
                    sort={orderBy === 'total_formatted' && orderDirection}
                    onClick={() => {
                        handleOrder('total_formatted');
                    }}
                    children={pos_strings.value}
                />
                <Table.ThSort
                    sort={orderBy === 'payment_mode' && orderDirection}
                    onClick={() => {
                        handleOrder('payment_mode');
                    }}
                    children={pos_strings.receipt.payment_by}
                />
                <Table.ThSort
                    sort={orderBy === 'status' && orderDirection}
                    onClick={() => {
                        handleOrder('status');
                    }}
                    children={`${strings.status}`}
                />
                <Table.Th children={strings.Actions} className="text-right" />
            </Table.Head>
            <Table.Body>
                {data?.data.map((receipt) => (
                    <ClientReceiptListItem
                        key={receipt.id}
                        receipt={receipt}
                        onViewClick={() => {
                            navigate(`receipt/${receipt.id}/detail`);
                        }}
                    />
                ))}
                {data?.data.length === 0 ? (
                    <EmptyData cardHeight="h-[20vh]" />
                ) : (
                    <tr>
                        <td colSpan={10}>{<ViewAllButton to={`/clients/${clientId}/receipts`} />}</td>
                    </tr>
                )}
            </Table.Body>
        </Table>
    );
};
export default ClientReceipt;
