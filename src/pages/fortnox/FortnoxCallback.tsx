import Button from '@components/form/Button';
import Card from '@components/card';
import Heading from '@components/heading/Heading';
import api from '@configs/api';
import useAuth from '@hooks/useAuth';
import pos_strings from '@lang/pos/Lang';
import FortnoxIcon from '@partials/Icons/Fortnox';
import CheckIcon from '@partials/Icons/Check';
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { commonFetch } from '../../helper';

const FortnoxCallback: React.FC = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { mutate } = useAuth();
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState<string>();

    useEffect(() => {
        const handleCallback = async () => {
            try {
                const code = searchParams.get('code');
                const state = searchParams.get('state');

                if (!code || !state) {
                    setError('Missing authorization parameters');
                    setLoading(false);
                    return;
                }

                const url = api.pos.fortnox.auth.callback + `?code=${code}&state=${state}`;
                const response = await commonFetch(url, {
                    method: 'GET',
                });

                if (response.status === '1') {
                    setSuccess(true);
                    await mutate(); // Refresh user data to get updated company info
                    toast.success(pos_strings.fortnox.successful_connected);
                } else {
                    setError(response.message || 'Failed to connect to Fortnox');
                }
            } catch (err) {
                setError('An error occurred while connecting to Fortnox');
                console.error('Fortnox callback error:', err);
            } finally {
                setLoading(false);
            }
        };

        handleCallback();
    }, [searchParams, mutate]);

    const handleContinue = () => {
        navigate('/point-of-sale/payment');
    };

    return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
            <Card className="w-full max-w-md p-8">
                <div className="flex flex-col items-center space-y-6">
                    <FortnoxIcon size={80} />
                    
                    <Heading text={pos_strings.fortnox.connect_to_fortnox} className="text-center">
                        {loading ? 'Connecting to Fortnox...' : success ? 'Connected Successfully!' : 'Connection Failed'}
                    </Heading>

                    {loading && (
                        <div className="flex items-center space-x-2">
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                            <span>Processing your connection...</span>
                        </div>
                    )}

                    {success && (
                        <div className="flex flex-col items-center space-y-4">
                            <CheckIcon className="h-16 w-16 text-green-500" />
                            <p className="text-center text-gray-600 dark:text-gray-400">
                                You are now connected to Fortnox and can use invoice payments in your Point of Sale system.
                            </p>
                        </div>
                    )}

                    {error && (
                        <div className="flex flex-col items-center space-y-4">
                            <div className="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
                                <span className="text-red-500 text-2xl">✕</span>
                            </div>
                            <p className="text-center text-red-600 dark:text-red-400">
                                {error}
                            </p>
                        </div>
                    )}

                    {!loading && (
                        <Button
                            onClick={handleContinue}
                            className="w-full"
                        >
                            Continue to Point of Sale
                        </Button>
                    )}
                </div>
            </Card>
        </div>
    );
};

export default FortnoxCallback;
