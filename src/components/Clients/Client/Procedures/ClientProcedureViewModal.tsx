import AddButton from '@components/form/AddButton';
import Image from '@components/form/Image';
import { ClientFileBatchResponse, SettingResponse } from '@interface/common';
import { useHours } from '@provider/TimeProvider';
import React, { useState } from 'react';
import useSWR from 'swr';
import api from '../../../../configs/api';
import { generateUserFullName, getUnitKeyToValue } from '../../../../helper';
import useAuth from '../../../../hooks/useAuth';
import { ClientTreatment } from '../../../../interfaces/model/clientTreatment';
import strings from '../../../../lang/Lang';
import Modal from '../../../../partials/MaterialModal/Modal';
import ViewModalTextItem from '../../../../partials/ViewModal/ViewModalTextItem';
import ClientFileBatchAddPicturesModal from '../Media/ClientFileBatchAddPicturesModal';
import useClient from '@hooks/useClient';

const ClientMediaFileBatchView = React.lazy(() => import('../Media/ClientMediaFileBatchView'));

export interface ClientProcedureViewModalProps {
    selectedClientTreatment?: ClientTreatment;
    openModal?: boolean;
    handleClose?: () => void;
}

const ClientProcedureViewModal: React.FC<ClientProcedureViewModalProps> = ({
    selectedClientTreatment,
    openModal = false,
    handleClose = () => {},
}) => {
    const { user } = useAuth();
    const { clientId } = useClient();

    const { data: settingData } = useSWR<SettingResponse, Error>(api.setting);
    const enableNRS = settingData?.data.find((setting) => setting.key === api.NRS_RATING && setting.value === '1')?.value;

    const { data, mutate } = useSWR<ClientFileBatchResponse>(api.clientFileBatch.list(clientId, selectedClientTreatment?.id, 'procedure'));

    const { renderDate, renderDateTime } = useHours();
    const [openImageAddModal, setOpenImageAddModal] = useState(false);

    return (
        <>
            {openImageAddModal && (
                <ClientFileBatchAddPicturesModal
                    openModal={openImageAddModal}
                    setOpenModal={setOpenImageAddModal}
                    onSuccess={mutate}
                    signable_id={selectedClientTreatment?.id ?? 0}
                    signable_type="procedure"
                />
            )}
            <Modal open={openImageAddModal ? false : openModal} title={strings.Procedure} handleClose={handleClose} size="large">
                <div className="p-4">
                    <ViewModalTextItem title={strings.DATE_OF_PROCEDURE} value={renderDate(selectedClientTreatment?.date)} showIfEmpty />
                    <ViewModalTextItem title={strings.Name} value={selectedClientTreatment?.name} showIfEmpty />
                    <ViewModalTextItem
                        title={strings.total_cost}
                        value={`${getUnitKeyToValue(user?.company?.unit || '')}${(selectedClientTreatment?.details || []).reduce(
                            (cost, treatment) => cost + parseFloat(treatment.actual_cost),
                            0,
                        )}`}
                    />
                    {selectedClientTreatment?.prescriptions?.length !== 0 && (
                        <div className="mb-4">
                            <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.prescriptions}</p>
                            <ol className="divide-y dark:divide-gray-700">
                                {selectedClientTreatment?.prescriptions?.map((v) => (
                                    <li className="py-1" key={v.id}>
                                        <p className="inline-flex items-center">
                                            <span className="font-medium">{v.title}</span>
                                            {/* <span className="ml-2 text-xs text-gray-700 dark:text-gray-300">
                                                {strings.end_date}: {renderDate(v.expire_at)}
                                            </span> */}
                                        </p>
                                    </li>
                                ))}
                            </ol>
                        </div>
                    )}
                    {enableNRS && <ViewModalTextItem title={strings.nrs_rating} value={selectedClientTreatment?.nrs_rating || '-'} />}
                    <ViewModalTextItem
                        title={strings.NOTES}
                        value={`${
                            selectedClientTreatment?.notes_html ? (selectedClientTreatment?.notes_html ?? '') : (selectedClientTreatment?.notes ?? '')
                        }`}
                        html={true}
                        showIfEmpty
                    />
                    <ViewModalTextItem
                        title={strings.Signed_at}
                        show={!!selectedClientTreatment?.signed_at}
                        value={`${renderDateTime(selectedClientTreatment?.signed_at, { utc: true, isShowLocal: true }) || ''} ${
                            strings.By
                        } ${generateUserFullName(selectedClientTreatment?.signed_by)}`}
                    />
                    <ViewModalTextItem
                        title={strings.Treatments}
                        show={!!selectedClientTreatment?.details?.length}
                        value={selectedClientTreatment?.details
                            ?.map((treatment) => `${treatment.name} ${`(${getUnitKeyToValue(treatment.unit)}${treatment.actual_cost})`}`)
                            .join('\n')}
                    />
                    <ViewModalTextItem
                        title={strings.VerifiedSign}
                        show={!!selectedClientTreatment?.signed_at}
                        value={`${api.storage}${selectedClientTreatment?.sign}`}
                        image
                    />
                    <div className="mb-4">
                        <div className="mb-4 flex items-center justify-between">
                            <p className="text-sm uppercase text-primary dark:text-primaryLight">{strings.Pictures}</p>
                            <AddButton
                                onClick={() => {
                                    setOpenImageAddModal(true);
                                }}
                            />
                        </div>
                        {selectedClientTreatment?.images.map((image) => (
                            <Image
                                key={image}
                                src={api.storageUrl(image) || ''}
                                alt={'Procedure'}
                                className="mb-2 w-auto rounded border dark:border-dimGray"
                            />
                        ))}
                        {data?.data.map((batch) => <ClientMediaFileBatchView key={batch.id} batch={batch} />)}
                    </div>
                </div>
            </Modal>
        </>
    );
};

export default ClientProcedureViewModal;
