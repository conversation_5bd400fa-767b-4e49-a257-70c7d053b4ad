import BankIdComponent from '@components/BankId/PrescriptionBankId';
import strings from '@lang/Lang';
import Modal from '@partials/MaterialModal/Modal';

export interface BankIdSuccessResponse {
    personalNumber: string;
    name: string;
    givenName: string;
    surname: string;
}

export interface BankIdModalProps {
    open?: boolean;
    onClose: () => void;
    onSuccess: (res: BankIdSuccessResponse) => void;
}

export interface BankIdResponse {
    order_ref: string;
    start_token: string;
    start_time: string;
    start_secret: string;
    auto_start_token: string;
}

const BankIdModal: React.FC<BankIdModalProps> = ({ open, onSuccess, onClose }) => {
    const handleClose = () => {
        if (onClose) onClose();
    };

    return (
        <Modal open={open} title={strings.confirm_your_bank_id} handleClose={handleClose} size="medium">
            <div className="flex h-full flex-col items-center p-4 pb-8 text-center">
                <BankIdComponent onSuccess={onSuccess} onClose={handleClose} />
            </div>
        </Modal>
    );
};

export default BankIdModal;
