import Skeleton from '@components/Skeleton/Skeleton';
import Card from '@components/card';
import AddButton from '@components/form/AddButton';
import Button from '@components/form/Button';
import Heading from '@components/heading/Heading';
import ModalSuspense from '@partials/Loadings/ModalLoading';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration'; // import plugin
import LocalizedFormat from 'dayjs/plugin/localizedFormat'; // import plugin
import relativeTime from 'dayjs/plugin/relativeTime'; // import plugin
import React, { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import useSWRInfinite from 'swr/infinite';
import api from '../../../../configs/api';
import { getSignedUrl } from '../../../../helper';
import { CommonModelPaginatedResponse } from '../../../../interfaces/common';
import { File } from '../../../../interfaces/model/File';
import { MediaData } from '../../../../interfaces/model/media_data';
import strings from '../../../../lang/Lang';
import BackToClientDashboard from '../sections/BackToClientDashboard';
import ClientMediaImageItem from './ClientMediaImageItem';
import ClientMediaListUL from './ClientMediaListUL';
import ClientMediaTitle from './ClientMediaTitle';

import NewLightBox from '@components/LightBox/LightBox';

const ClientMediaModal = React.lazy(() => import('./ClientMediaModal'));
const ClientMediaDeleteModal = React.lazy(() => import('./ClientMediaDeleteModal'));

dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.extend(LocalizedFormat);

export interface ClientMediasProps { }

async function download(file: File, name: string) {
    try {
        const url = await getSignedUrl(file.filename);
        const response = await fetch(url);
        const data = await response.blob();
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(data);
        a.setAttribute('download', name);
        a.click();
    } catch (err) { }
}

const ClientMedias: React.FC<ClientMediasProps> = () => {
    const { clientId }: { clientId?: string } = useParams();
    const [deleteOpen, setDeleteOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File>();
    const [openLightbox, setOpenLightbox] = useState<boolean>(false);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [data, setData] = useState<MediaData[] | undefined>();
    const [openModal, setOpenModal] = React.useState(false);
    const [multiDeleteSelectedIds, setMultiDeleteSelectedIds] = useState<number[]>([]);
    const isMultiDeleting = useMemo(() => multiDeleteSelectedIds.length > 0, [multiDeleteSelectedIds]);

    const {
        data: response,
        error,
        size,
        setSize,
        mutate,
    } = useSWRInfinite<CommonModelPaginatedResponse<MediaData>, Error>((pageIndex) => api.clientMedia(clientId, pageIndex + 1));

    useEffect(() => {
        setData(response?.flatMap((dt) => dt.data));
    }, [response]);

    const files = data?.flatMap((value) => value.data.map((file) => file)) ?? [];
    const urls = files.map((file) => file.url) ?? [];

    const isLoadingInitialData = !response && !error;
    const isEmpty = response?.at(0)?.data.length === 0;
    const isLoadingMore = isLoadingInitialData || (size > 0 && response && typeof response[size - 1] === 'undefined');
    const isReachingEnd = isEmpty || (response && response[response.length - 1]?.data.length < 5);

    function addOrRemoveId(id: number) {
        setMultiDeleteSelectedIds((oldValue) => {
            const arr = [...oldValue];
            if (arr.includes(id)) {
                arr.splice(arr.indexOf(id), 1); // remove array of value
                return arr;
            }
            arr.push(id); // add array of value
            return arr;
        });
    }

    return (
        <>
            <ModalSuspense>
                {openModal && <ClientMediaModal openModal={openModal} setOpenModal={setOpenModal} mutate={mutate} />}
                {deleteOpen && (
                    <ClientMediaDeleteModal
                        open={deleteOpen}
                        selectedFileIds={multiDeleteSelectedIds}
                        handleClose={() => setDeleteOpen(false)}
                        selectedFile={selectedFile}
                        onSuccess={async () => {
                            setData((data) => {
                                return data
                                    ?.map((mediaData) => ({
                                        date: mediaData.date,
                                        data: mediaData.data.filter((file) =>
                                            isMultiDeleting ? !multiDeleteSelectedIds.includes(file.id) : file.id !== selectedFile?.id,
                                        ),
                                    }))
                                    .filter((data) => !!data.data.length);
                            });
                            setMultiDeleteSelectedIds([]);
                        }}
                    />
                )}
            </ModalSuspense>
            <BackToClientDashboard />
            <Card>
                <div className="mb-4 flex flex-col md:flex-row justify-between gap-3">
                    <Heading text={strings.media} variant="bigTitle" />
                    <div className="flex gap-3 flex-wrap">
                        {isMultiDeleting && (
                            <Button
                                onClick={() => setDeleteOpen(true)}
                                children={`${strings.delete_media} (${multiDeleteSelectedIds.length})`}
                                size="small"
                            />
                        )}
                        <Link to={`/clients/${clientId}/before-after`}>
                            <Button size="small">{strings.beforeAfter}</Button>
                        </Link>
                        <AddButton onClick={() => setOpenModal(true)} />
                    </div>
                </div>
                <div className="masonry mb-8">
                    {data?.map((media, upperIndex) => {
                        return (
                            <React.Fragment key={media.date}>
                                <ClientMediaTitle title={dayjs(media.date).format('ddd, D MMM YYYY')} />
                                <ClientMediaListUL>
                                    {media.data.map((file, indexInner) => (
                                        <ClientMediaImageItem
                                            key={file.id}
                                            isDeleting={isMultiDeleting}
                                            isSelected={multiDeleteSelectedIds.includes(file.id)}
                                            onSelect={() => addOrRemoveId(file.id)}
                                            url={file.thumbnail ? api.storageUrl(file.thumbnail) : api.storageUrl(file.filename)}
                                            onDeleteClick={() => {
                                                setSelectedFile(file);
                                                setDeleteOpen(true);
                                            }}
                                            onImageClick={() => {
                                                if (upperIndex === 0) {
                                                    setSelectedIndex(indexInner);
                                                    setOpenLightbox(true);
                                                    return;
                                                }

                                                let nextIndex = 0;
                                                for (let i = 0; i < upperIndex; i++) {
                                                    nextIndex = nextIndex + data[i].data.length;
                                                }
                                                nextIndex = nextIndex + indexInner;

                                                setSelectedIndex(nextIndex);
                                                setOpenLightbox(true);
                                            }}
                                        />
                                    ))}
                                </ClientMediaListUL>
                            </React.Fragment>
                        );
                    })}
                    {isLoadingInitialData || isLoadingMore ? (
                        <div className="masonry mb-8">
                            <Skeleton className="w-36 text-2xl" />
                            <ul className="">
                                <li className="h-screen max-h-[200px] w-40">
                                    <Skeleton className="h-full w-full" />
                                </li>
                                <li className="h-screen max-h-[200px] w-56">
                                    <Skeleton className="h-full w-full" />
                                </li>
                                <li className="h-screen max-h-[200px] w-64">
                                    <Skeleton className="h-full w-full" />
                                </li>
                                <li className="h-screen max-h-[200px] w-40">
                                    <Skeleton className="h-full w-full" />
                                </li>
                                <li />
                            </ul>
                        </div>
                    ) : (
                        <></>
                    )}
                </div>
                {/* hasMore */}
                {data && !error && !isEmpty && !isReachingEnd && (
                    <Button loading={isLoadingMore} disabled={isLoadingMore} onClick={() => setSize(size + 1)}>
                        {strings.loadMore}
                    </Button>
                )}

                <ModalSuspense>
                    {openLightbox && (
                        <NewLightBox
                            open={openLightbox}
                            imageURLs={urls}
                            selectedIndex={selectedIndex}
                            handleClose={() => setOpenLightbox(false)}
                            handleDownload={async (index) => {
                                const file = files[index];
                                await download(file, file.filename ?? '');
                            }}
                        />
                    )}
                </ModalSuspense>
            </Card>
        </>
    );
};

export default ClientMedias;
