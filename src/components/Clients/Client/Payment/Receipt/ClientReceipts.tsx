import Card from '@components/card';
import PaginationCompo from '@components/form/Pagination';
import Skeleton from '@components/Skeleton/Skeleton';
import api from '@configs/api';
import useClient from '@hooks/useClient';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { ReceiptPaginatedResponse } from '@interface/common';
import strings from '@lang/Lang';
import pos_strings from '@lang/pos/Lang';
import EmptyData from '@partials/Error/EmptyData';
import Table from '@partials/Table/PageTable';
import BackToClientDashboard from '../../sections/BackToClientDashboard';
import ClientReceiptListItem from './ClientReceiptListItem';
import { useNavigate } from 'react-router';
import Heading from '@components/heading/Heading';
import Input from '@components/form/Input';
import SearchIcon from '@icons/Search';
import CalendarSelect from '@components/Calendar/Custom/CalendarSelect';
import React from 'react';
import { useHours } from '@provider/TimeProvider';

const ClientReceipts = () => {
    const { clientId } = useClient();
    const [date, setDate] = React.useState<string>();
    const { parseDate } = useHours();

    const { data, page, setPage, search, setSearch, loading, orderBy, orderDirection, handleOrder } = usePaginationSWR<
        ReceiptPaginatedResponse,
        Error
    >(
        api.pos.receipt.list({
            client_id: clientId,
            start_date: date ? parseDate(date).format('YYYY-MM-DD') : undefined,
            end_date: date ? parseDate(date).format('YYYY-MM-DD') : undefined,
        }),
        {
            limit: 20,
        },
    );
    const navigate = useNavigate();

    return (
        <>
            <BackToClientDashboard />
            <Card>
                <div className="flex items-center justify-between pb-4 space-x-3">
                    <Heading text={pos_strings.receipt.receipt} />
                    <div className='flex gap-3'>
                        <Input
                            placeholder={strings.Search}
                            value={search || ''}
                            icon={<SearchIcon className="text-mediumGray" />}
                            onChange={(event) => setSearch(event.target.value)}
                        />
                        <CalendarSelect
                            alignRight
                            placeholder={strings.select_date}
                            selectedDate={date}
                            onChange={(date) => {
                                setDate(date);
                            }}
                            removeDate={true}
                        />
                    </div>
                </div>
                <Table>
                    <Table.Head>
                        <Table.ThSort
                            sort={orderBy === 'viva_receipt_id' && orderDirection}
                            onClick={() => {
                                handleOrder('viva_receipt_id');
                            }}
                            children={strings.id}
                        />
                        <Table.ThSort
                            sort={orderBy === 'created_at' && orderDirection}
                            onClick={() => {
                                handleOrder('created_at');
                            }}
                            children={strings.time}
                        />
                        <Table.ThSort
                            sort={orderBy === 'total_formatted' && orderDirection}
                            onClick={() => {
                                handleOrder('total_formatted');
                            }}
                            children={pos_strings.value}
                        />
                        <Table.ThSort
                            sort={orderBy === 'payment_mode' && orderDirection}
                            onClick={() => {
                                handleOrder('payment_mode');
                            }}
                            children={pos_strings.receipt.payment_by}
                        />
                        <Table.ThSort
                            sort={orderBy === 'status' && orderDirection}
                            onClick={() => {
                                handleOrder('status');
                            }}
                            children={`${strings.status}`}
                        />
                        <Table.Th children={strings.Actions} className="text-right" />
                    </Table.Head>
                    <Table.Body>
                        {data?.data.map((receipt) => (
                            <ClientReceiptListItem
                                key={receipt.id}
                                receipt={receipt}
                                onViewClick={() => {
                                    navigate(`/clients/${clientId}/receipt/${receipt.id}/detail`);
                                }}
                            />
                        ))}
                        {loading && <ClientReceiptSkeleton limit={10} />}
                        {!loading && !data?.data.length && <EmptyData cardHeight="h-[60vh]" />}
                    </Table.Body>
                </Table>
                <PaginationCompo
                    pageSize={data?.per_page ?? 0}
                    totalCount={data?.total ?? 0}
                    currentPage={page}
                    onPageChange={(page) => setPage(page)}
                />
            </Card>
        </>
    );
};
function ClientReceiptSkeleton({ limit }: { limit: number }) {
    return (
        <>
            {[...Array(limit)].map((value, key) => {
                return (
                    <tr key={key}>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="p-2">
                            <Skeleton className="h-10 w-full cursor-wait" />
                        </td>
                        <td className="flex justify-end space-x-1.5 p-2">
                            <Skeleton className="mx-1 h-9 w-9" variant="circular" />
                        </td>
                    </tr>
                );
            })}
        </>
    );
}
export default ClientReceipts;
