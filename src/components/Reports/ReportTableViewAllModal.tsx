import Modal from '@partials/MaterialModal/Modal';
import { ReportTableComponentProps } from './ReportTableComponent';
import Table from '@partials/Table/PageTable';
import { usePaginationSWR } from '@hooks/usePaginationSWR';
import { CommonModelPaginatedResponse } from '@interface/common';
import { SectionLoading } from '@partials/Loadings/SectionLoading';
import PaginationCompo from '@components/form/Pagination';

export interface ReportTableViewAllModalProps<T> extends ReportTableComponentProps<T> {
    openModal: boolean;
    onClose: () => void;
}

function ReportTableViewAllModal<T>({ openModal, onClose, headings, api, renderColumn, title }: ReportTableViewAllModalProps<T>) {
    const { data, loading, page, setPage, url } = usePaginationSWR<CommonModelPaginatedResponse<T>, Error>(!openModal ? null : api, { limit: 10 });

    return (
        <Modal open={openModal} title={title} handleClose={onClose} size="medium">
            <div className="p-4 pt-2">
                {loading ? (
                    <SectionLoading />
                ) : (
                    <>
                        <Table>
                            <Table.Head>
                                {headings.map((heading, index) => (
                                    <Table.Td key={index}>{heading}</Table.Td>
                                ))}
                            </Table.Head>
                            <Table.Body>
                                {data?.data.map((item, index) => (
                                    <tr key={index}>
                                        {headings.map((heading, colIndex) => (
                                            <Table.Td key={colIndex}>{renderColumn(item, colIndex, index, url)?.at(colIndex)}</Table.Td>
                                        ))}
                                    </tr>
                                ))}
                            </Table.Body>
                        </Table>
                        <PaginationCompo
                            pageSize={data?.per_page}
                            totalCount={data?.total}
                            currentPage={page}
                            onPageChange={(page) => setPage(page)}
                        />
                    </>
                )}
            </div>
        </Modal>
    );
}

export default ReportTableViewAllModal;
