import strings from '@lang/Lang';
import Select from '../Select';
import TimePeriod from './TimePeriod';
import cx from 'classix';

export interface BirthdayProps {
    value?: string;
    error?: string;
    onChange: (value: string) => void;
}
const Birthday: React.FC<BirthdayProps> = ({ onChange, value, error }) => {
    // const time_period = [
    //     strings.today.toLowerCase(),
    //     strings.tomorrow.toLowerCase(),
    //     `${strings.in_next} 7 ${strings.days.toLowerCase()}`,
    //     `${strings.in_next} 30 ${strings.days.toLowerCase()}`,
    // ];
    return (
        <div className={cx('w-full', error && 'pb-2')}>
            <div className="flex flex-wrap gap-2 items-center rounded-lg bg-grayishBlue px-3 py-1.5 dark:bg-darkGray">
                <p>
                    {strings.birthday} {strings.in_next}
                </p>
                <TimePeriod onChange={onChange} value={value} removeYearOption />
                {/* <Select className='!px-2 !py-1 dark:!bg-black' color='white' displayValue={(val) => val} placeholder={strings.Select} onChange={onChange} value={value}>
                    {time_period.map((time) => (
                        <Select.Option value={time}>{time}</Select.Option>
                    ))}
                </Select> */}
            </div>
            <p className="text-[10px] text-red-500 first-letter:capitalize">{error}</p>
        </div>
    );
};
export default Birthday;
