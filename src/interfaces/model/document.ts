import { User } from "./user";

export interface UserDocument {
  id: number;
  company_id: number;
  title: string;
  modelable_type?: string;
  modelable_id?: number;
  created_at: string;
  updated_at: string;
  process?: "PATIENT_SAFETY" | "DEVIATION_MGMT" | "THE_BUSINESS";
  signed_by?: User;
  version_count?: string;
  version?: DocumentVersion;
  deleted_at: string;
}

export interface DocumentVersion {
  id: number;
  company_id: number;
  company_document_id: number;
  pdf: string;
  response: string;
  questions: Question[];
  version: string;
  sign: string;
  signed_at: string;
  signed_by_id: number;
  created_at: string;
  updated_at: string;
  formatted_response: Formattedresponse[];
  should_regen_pdf: boolean;
  signed_by?: User;
}

export interface Question {
  question: string;
  type: string;
  default?: string,
  required?: boolean;
}

interface Formattedresponse {
  value?: string | null;
  text?: string | null;
}
