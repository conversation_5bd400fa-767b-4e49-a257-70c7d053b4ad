export default interface GoogleRatingsDetails {
    id: number;
    name?: string;
    rating?: string;
    reviews?: ReviewsDetails[];
    time?: string;
    sessionToken?: string;
    user_rating_count?: string;
    images: string;
    mode_of_payment: string;
    about?: string;
    opening_hours: string;
    social_links?: string;
    sub_text?: string;
    title?: string;
    place_id?:string;
    google_maps_links: links;
}
interface links {
    directionsUri?: string;
    photosUri?: string;
    placeUri?: string;
    reviewsUri?: string;
    writeAReviewUri?: string;
}

export interface RatingDetails {
    sessionToken?: string;
    placeId?: string;
    suggestions?: PlaceNameaddress[];
}
export interface PlaceNameaddress {
    placeId: string;
    name: string;
}
export interface PlaceDetails {
    name?: string;
    address?: string;
    rating?: number;
    ratingCount?: number;
    placeUri?: string;
    reviewsUri?: string;
    reviews?: ReviewsDetails[];
    sessionToken?: string;
    placeId?: string;
}
export interface ReviewsDetails {
    authorAttribution?: authorName;
    originalText?: text;
    rating?: number;
    relativePublishTimeDescription?: string;
}
interface text{
    text?: string
}
interface authorName{
    displayName: string
}
