import IconButton from '@components/form/IconButton';
import { Marketing } from '@interface/model/marketing';
import strings from '@lang/Lang';
import EyeIcon from '@partials/Icons/Eye';
import Table from '@partials/Table/PageTable';
import { useHours } from '@provider/TimeProvider';

export interface SMSMarketingListItemProps {
    marketing: Marketing;
    onViewClick: (template: Marketing) => void;
}

const SMSMarketingListItem: React.FC<SMSMarketingListItemProps> = ({ marketing, onViewClick }) => {
    const { renderDateTime } = useHours();
    return (
        <>
            <tr className="alternate-tr-mobile md:hidden">{mobileListItem()}</tr>
            <tr className={`alternate-tr-desktop hidden md:table-row`}>{desktopListItem()}</tr>
        </>
    );

    function desktopListItem() {
        return (
            <>
                <Table.Td>{renderDateTime(marketing.created_at)}</Table.Td>
                <Table.Td>
                    <p className="line-clamp-2 max-w-xl text-sm">{marketing.name}</p>
                </Table.Td>
                <Table.Td>{marketing.client_count}</Table.Td>
                <Table.Td>{marketing.credit_used}</Table.Td>
                <Table.Td>{marketing.failed}</Table.Td>
                <Table.Td className="p-2">
                    <div className="flex justify-end space-x-1.5">
                        <IconButton
                            onClick={() => {
                                onViewClick(marketing);
                            }}
                            name={strings.View}
                        >
                            <EyeIcon />
                        </IconButton>
                    </div>
                </Table.Td>
            </>
        );
    }

    function mobileListItem() {
        return (
            <Table.Td>
                <div className="space-y-1">
                    <p className="line-clamp-2 text-sm">{marketing.name}</p>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center">
                            <span className="mr-1 text-mediumGray dark:text-gray-500">{strings.send_to}</span> <span>: {marketing.client_count}</span>
                        </div>
                        <div className="flex items-center">
                            <span className="mr-1 text-mediumGray dark:text-gray-500">{strings.credit_used}</span>: {marketing.credit_used}
                        </div>
                    </div>
                    <div className="flex items-center justify-between">
                        <div>
                            <span className="mr-1 text-mediumGray dark:text-gray-500">{strings.failed}</span>: {marketing.failed}
                        </div>
                        <p>{renderDateTime(marketing.created_at)}</p>
                    </div>
                    <div className="-ml-2 flex space-x-0.5 text-primary">
                        <IconButton
                            onClick={() => {
                                onViewClick(marketing);
                            }}
                            name={strings.View}
                        >
                            <EyeIcon />
                        </IconButton>
                    </div>
                </div>
            </Table.Td>
        );
    }
};

export default SMSMarketingListItem;
